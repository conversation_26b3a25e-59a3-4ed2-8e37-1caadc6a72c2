"use client";

import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { logger, MemoryMonitor, PerformanceMonitor } from "@/lib/logger";
import {
  Activity,
  Clock,
  Database,
  Download,
  MemoryStick,
  RefreshCw,
  Server,
  Upload,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";

interface SystemHealth {
  database: boolean;
  authentication: boolean;
  fileUpload: boolean;
  api: boolean;
  overall: "excellent" | "good" | "fair" | "poor";
}

interface PerformanceMetrics {
  memoryUsage?: any;
  queryStats?: any;
  apiMetrics?: any;
  renderTime?: number;
}

export function DebugDashboard() {
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    database: false,
    authentication: false,
    fileUpload: false,
    api: false,
    overall: "poor",
  });

  const [performanceMetrics, setPerformanceMetrics] =
    useState<PerformanceMetrics>({});
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    // Start performance monitoring
    PerformanceMonitor.mark("debug-dashboard-mount");

    // Start memory monitoring
    const stopMemoryMonitoring = MemoryMonitor.startMemoryMonitoring(10000);

    // Initial health check
    checkSystemHealth();

    return () => {
      stopMemoryMonitoring?.();
      PerformanceMonitor.measure("debug-dashboard-mount");
    };
  }, []);

  const checkSystemHealth = async () => {
    setIsLoading(true);
    logger.info("debug", "Starting system health check");

    try {
      const health: SystemHealth = {
        database: false,
        authentication: false,
        fileUpload: false,
        api: false,
        overall: "poor",
      };

      // Check database
      try {
        const dbResponse = await fetch("/api/certificates", {
          credentials: "include",
        });
        health.database = dbResponse.status !== 500;
      } catch (error) {
        logger.error("debug", "Database health check failed", { error });
      }

      // Check authentication
      try {
        const authResponse = await fetch("/api/auth/session", {
          credentials: "include",
        });
        health.authentication = authResponse.ok || authResponse.status === 401; // 401 is expected if not logged in
      } catch (error) {
        logger.error("debug", "Auth health check failed", { error });
      }

      // Check file upload
      try {
        const uploadResponse = await fetch("/api/uploadthing");
        health.fileUpload = uploadResponse.status === 405; // Method not allowed is expected
      } catch (error) {
        logger.error("debug", "Upload health check failed", { error });
      }

      // Check API
      health.api = health.database && health.authentication;

      // Calculate overall health
      const healthyCount = Object.values(health).filter(
        (v) => typeof v === "boolean" && v
      ).length;
      const totalChecks = 4;

      if (healthyCount === totalChecks) {
        health.overall = "excellent";
      } else if (healthyCount >= totalChecks * 0.75) {
        health.overall = "good";
      } else if (healthyCount >= totalChecks * 0.5) {
        health.overall = "fair";
      } else {
        health.overall = "poor";
      }

      setSystemHealth(health);
      setLastUpdate(new Date());

      logger.info("debug", "System health check completed", { health });
    } catch (error) {
      logger.error("debug", "System health check failed", { error });
    } finally {
      setIsLoading(false);
    }
  };

  const updatePerformanceMetrics = () => {
    const metrics: PerformanceMetrics = {};

    // Memory usage (client-side only)
    if (typeof window !== "undefined" && "memory" in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
      };
    }

    setPerformanceMetrics(metrics);
  };

  const downloadDebugReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      systemHealth,
      performanceMetrics,
      environment: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      },
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `debug-report-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    logger.info("debug", "Debug report downloaded");
  };

  const getHealthColor = (healthy: boolean) => {
    return healthy ? "bg-green-500" : "bg-red-500";
  };

  const getOverallHealthColor = (health: string) => {
    switch (health) {
      case "excellent":
        return "bg-green-500";
      case "good":
        return "bg-blue-500";
      case "fair":
        return "bg-yellow-500";
      case "poor":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Debug Dashboard</h2>
          <p className="text-muted-foreground">
            System health monitoring and debugging tools
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={updatePerformanceMetrics}
            variant="outline"
            size="sm"
          >
            <Activity className="h-4 w-4 mr-2" />
            Update Metrics
          </Button>
          <Button onClick={checkSystemHealth} disabled={isLoading} size="sm">
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button onClick={downloadDebugReport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {lastUpdate && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertTitle>Last Updated</AlertTitle>
          <AlertDescription>{lastUpdate.toLocaleString()}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${getHealthColor(
                  systemHealth.database
                )}`}
              />
              <span className="text-sm">
                {systemHealth.database ? "Connected" : "Disconnected"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Authentication
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${getHealthColor(
                  systemHealth.authentication
                )}`}
              />
              <span className="text-sm">
                {systemHealth.authentication ? "Working" : "Error"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">File Upload</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${getHealthColor(
                  systemHealth.fileUpload
                )}`}
              />
              <span className="text-sm">
                {systemHealth.fileUpload ? "Ready" : "Error"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Overall Health
            </CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getOverallHealthColor(systemHealth.overall)}>
              {systemHealth.overall.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="environment">Environment</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {performanceMetrics.memoryUsage && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MemoryStick className="h-4 w-4" />
                    Memory Usage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Used:</span>
                      <span>{performanceMetrics.memoryUsage.used}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span>{performanceMetrics.memoryUsage.total}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Limit:</span>
                      <span>{performanceMetrics.memoryUsage.limit}MB</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="database">
          <Card>
            <CardHeader>
              <CardTitle>Database Debug Information</CardTitle>
              <CardDescription>
                Recent database queries and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Database debugging information would be displayed here. Enable
                database debugging in development mode to see query logs.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>Application Logs</CardTitle>
              <CardDescription>
                Recent application logs and error messages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Application logs would be displayed here. Check the browser
                console for detailed logs in development mode.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="environment">
          <Card>
            <CardHeader>
              <CardTitle>Environment Information</CardTitle>
              <CardDescription>
                Current environment configuration and settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Environment:</span>
                  <span>{process.env.NODE_ENV}</span>
                </div>
                <div className="flex justify-between">
                  <span>User Agent:</span>
                  <span className="truncate max-w-xs">
                    {navigator.userAgent}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Viewport:</span>
                  <span>
                    {window.innerWidth} × {window.innerHeight}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
