// Migration script to update database schema for enhanced authentication
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

const { neon } = require('@neondatabase/serverless');

async function migrateAuthSchema() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  const sql = neon(process.env.DATABASE_URL);

  console.log('🔄 Starting authentication schema migration...');

  try {
    // Add new columns to User table
    console.log('📝 Adding new columns to User table...');
    
    await sql`
      ALTER TABLE "User" 
      ALTER COLUMN password DROP NOT NULL
    `;
    console.log('✅ Made password column optional');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "emailVerified" BOOLEAN DEFAULT false NOT NULL
    `;
    console.log('✅ Added emailVerified column');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "emailVerificationToken" TEXT
    `;
    console.log('✅ Added emailVerificationToken column');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "emailVerificationExpires" TIMESTAMP
    `;
    console.log('✅ Added emailVerificationExpires column');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "twoFactorEnabled" BOOLEAN DEFAULT false NOT NULL
    `;
    console.log('✅ Added twoFactorEnabled column');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "twoFactorSecret" TEXT
    `;
    console.log('✅ Added twoFactorSecret column');

    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "lastLoginAt" TIMESTAMP
    `;
    console.log('✅ Added lastLoginAt column');

    // Create SocialAccount table
    console.log('📝 Creating SocialAccount table...');
    await sql`
      CREATE TABLE IF NOT EXISTS "SocialAccount" (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        "accessToken" TEXT,
        "refreshToken" TEXT,
        "expiresAt" TIMESTAMP,
        "tokenType" TEXT,
        scope TEXT,
        "idToken" TEXT,
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        UNIQUE(provider, "providerAccountId")
      )
    `;
    console.log('✅ Created SocialAccount table');

    // Create indexes for SocialAccount
    await sql`
      CREATE INDEX IF NOT EXISTS "social_account_user_idx" ON "SocialAccount"("userId")
    `;
    console.log('✅ Created SocialAccount user index');

    // Create EmailVerification table
    console.log('📝 Creating EmailVerification table...');
    await sql`
      CREATE TABLE IF NOT EXISTS "EmailVerification" (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        token TEXT UNIQUE NOT NULL,
        "expiresAt" TIMESTAMP NOT NULL,
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    console.log('✅ Created EmailVerification table');

    // Create indexes for EmailVerification
    await sql`
      CREATE INDEX IF NOT EXISTS "email_verification_user_idx" ON "EmailVerification"("userId")
    `;
    console.log('✅ Created EmailVerification user index');

    // Create PasswordReset table
    console.log('📝 Creating PasswordReset table...');
    await sql`
      CREATE TABLE IF NOT EXISTS "PasswordReset" (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        token TEXT UNIQUE NOT NULL,
        "expiresAt" TIMESTAMP NOT NULL,
        used BOOLEAN DEFAULT false NOT NULL,
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    console.log('✅ Created PasswordReset table');

    // Create indexes for PasswordReset
    await sql`
      CREATE INDEX IF NOT EXISTS "password_reset_user_idx" ON "PasswordReset"("userId")
    `;
    console.log('✅ Created PasswordReset user index');

    // Update existing users to have emailVerified = true for SSO compatibility
    console.log('📝 Updating existing users...');
    await sql`
      UPDATE "User" 
      SET "emailVerified" = true 
      WHERE password IS NOT NULL AND password != ''
    `;
    console.log('✅ Set emailVerified = true for existing password users');

    console.log('🎉 Authentication schema migration completed successfully!');
    console.log('');
    console.log('📋 Migration Summary:');
    console.log('   ✅ Updated User table with new authentication fields');
    console.log('   ✅ Created SocialAccount table for OAuth providers');
    console.log('   ✅ Created EmailVerification table for email confirmation');
    console.log('   ✅ Created PasswordReset table for password recovery');
    console.log('   ✅ Added necessary indexes for performance');
    console.log('   ✅ Updated existing users for compatibility');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrateAuthSchema();
