"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface OrganizationActionsProps {
  organizationId: string;
  organizationName: string;
  status: string;
}

export function OrganizationActions({
  organizationId,
  organizationName,
  status,
}: OrganizationActionsProps) {
  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);
  const [isSuspendDialogOpen, setIsSuspendDialogOpen] = useState(false);
  const [suspendReason, setSuspendReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const handleVerify = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/admin/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "verify",
          organizationId,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Organization verified successfully",
        });
        setIsVerifyDialogOpen(false);
        router.refresh();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to verify organization",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuspend = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/admin/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "suspend",
          organizationId,
          reason: suspendReason,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Organization suspended successfully",
        });
        setIsSuspendDialogOpen(false);
        setSuspendReason("");
        router.refresh();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to suspend organization",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex space-x-2">
        {status === "pending" && (
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsVerifyDialogOpen(true)}
          >
            Verify
          </Button>
        )}
        {status === "verified" && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setIsSuspendDialogOpen(true)}
          >
            Suspend
          </Button>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push(`/admin/organizations/${organizationId}`)}
        >
          View
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push(`/admin/organizations/${organizationId}`)}
        >
          Edit
        </Button>
      </div>

      {/* Verify Confirmation Dialog */}
      <Dialog open={isVerifyDialogOpen} onOpenChange={setIsVerifyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Verify Organization</DialogTitle>
            <DialogDescription>
              Are you sure you want to verify "{organizationName}"? This action will
              mark the organization as verified and allow them to access verified
              features.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsVerifyDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleVerify} disabled={isLoading}>
              {isLoading ? "Verifying..." : "Verify Organization"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Suspend Confirmation Dialog */}
      <Dialog open={isSuspendDialogOpen} onOpenChange={setIsSuspendDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Suspend Organization</DialogTitle>
            <DialogDescription>
              Are you sure you want to suspend "{organizationName}"? This action will
              prevent the organization from accessing the platform.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for suspension (optional)</Label>
              <Textarea
                id="reason"
                placeholder="Enter the reason for suspending this organization..."
                value={suspendReason}
                onChange={(e) => setSuspendReason(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsSuspendDialogOpen(false);
                setSuspendReason("");
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleSuspend}
              disabled={isLoading}
            >
              {isLoading ? "Suspending..." : "Suspend Organization"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
