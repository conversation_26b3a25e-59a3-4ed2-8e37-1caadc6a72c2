/**
 * Test Script: Organization Lifecycle Functions
 *
 * This script tests the new organization lifecycle management functions
 * to ensure they work correctly with the database.
 *
 * Run with: node scripts/test-lifecycle-functions.js
 */

const dotenv = require("dotenv");

// Load environment variables
dotenv.config({ path: ".env.local" });

// Import lifecycle functions from main db module
const {
  createOrganizationLifecycleEvent,
  getOrganizationLifecycleEvents,
  getLatestGracePeriodEvent,
  handleAdminAccountDeletion,
  isOrganizationInGracePeriod,
  getOrganizationGracePeriodDaysRemaining,
  createAdminRecoveryRequest,
  getAdminRecoveryRequests,
  reviewAdminRecoveryRequest,
} = require("../lib/db");

async function testLifecycleFunctions() {
  console.log("🧪 Testing organization lifecycle functions...");

  try {
    // Test data
    const testOrgId = "test-org-123";
    const testUserId = "test-user-456";
    const testAdminId = "test-admin-789";

    console.log("\n1️⃣ Testing createOrganizationLifecycleEvent...");
    const eventResult = await createOrganizationLifecycleEvent({
      organizationId: testOrgId,
      eventType: "grace_period_started",
      triggeredBy: testUserId,
      gracePeriodEnds: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      metadata: { test: true, reason: "admin_deleted" },
    });
    console.log("✅ Event created:", eventResult);

    console.log("\n2️⃣ Testing getOrganizationLifecycleEvents...");
    const events = await getOrganizationLifecycleEvents(testOrgId);
    console.log("✅ Retrieved events:", events.length, "events found");
    if (events.length > 0) {
      console.log(
        "   Latest event:",
        events[0].eventType,
        "triggered at",
        events[0].triggeredAt
      );
    }

    console.log("\n3️⃣ Testing getLatestGracePeriodEvent...");
    const latestGracePeriod = await getLatestGracePeriodEvent(testOrgId);
    console.log(
      "✅ Latest grace period event:",
      latestGracePeriod ? "Found" : "Not found"
    );
    if (latestGracePeriod) {
      console.log("   Grace period ends:", latestGracePeriod.gracePeriodEnds);
    }

    console.log("\n4️⃣ Testing isOrganizationInGracePeriod...");
    const inGracePeriod = await isOrganizationInGracePeriod(testOrgId);
    console.log("✅ Organization in grace period:", inGracePeriod);

    console.log("\n5️⃣ Testing getOrganizationGracePeriodDaysRemaining...");
    const daysRemaining = await getOrganizationGracePeriodDaysRemaining(
      testOrgId
    );
    console.log("✅ Days remaining in grace period:", daysRemaining);

    console.log("\n6️⃣ Testing createAdminRecoveryRequest...");
    const recoveryResult = await createAdminRecoveryRequest({
      organizationId: testOrgId,
      requestedBy: testUserId,
      requestReason:
        "I was a member of this organization and the admin account was deleted. I need access to recover the organization data.",
    });
    console.log("✅ Recovery request created:", recoveryResult);

    console.log("\n7️⃣ Testing getAdminRecoveryRequests...");
    const recoveryRequests = await getAdminRecoveryRequests("pending");
    console.log(
      "✅ Retrieved recovery requests:",
      recoveryRequests.length,
      "pending requests"
    );
    if (recoveryRequests.length > 0) {
      console.log(
        "   Latest request reason:",
        recoveryRequests[0].requestReason.substring(0, 50) + "..."
      );
    }

    if (recoveryRequests.length > 0) {
      console.log("\n8️⃣ Testing reviewAdminRecoveryRequest...");
      const reviewResult = await reviewAdminRecoveryRequest(
        recoveryRequests[0].id,
        testAdminId,
        "approved",
        "Request approved after verification of user's previous membership."
      );
      console.log("✅ Recovery request reviewed:", reviewResult);
    }

    console.log("\n9️⃣ Testing handleAdminAccountDeletion (full workflow)...");
    const deletionResult = await handleAdminAccountDeletion(
      testOrgId,
      testUserId
    );
    console.log("✅ Admin deletion handled:", deletionResult);

    console.log("\n🎉 All lifecycle function tests completed successfully!");
    console.log("\n📋 Test Summary:");
    console.log("- ✅ Lifecycle event creation");
    console.log("- ✅ Event retrieval and querying");
    console.log("- ✅ Grace period management");
    console.log("- ✅ Admin recovery requests");
    console.log("- ✅ Recovery request review workflow");
    console.log("- ✅ Full admin deletion workflow");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run the tests
testLifecycleFunctions()
  .then(() => {
    console.log("\n✅ Test script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ Test script failed:", error);
    process.exit(1);
  });
