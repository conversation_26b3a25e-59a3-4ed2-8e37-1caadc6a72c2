import { expect, test } from '@playwright/test';

test.describe('Organization Management - Core Functionality', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' });

  test.beforeEach(async ({ page }) => {
    // Clean up any existing test organizations
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_organizations' }
    });
  });

  test.afterEach(async ({ page }) => {
    // Clean up test organizations after each test
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_organizations' }
    });
  });

  test('should display organization selection page correctly', async ({ page }) => {
    await page.goto('/organizations/new');

    // Check if organization selection page loads
    await expect(page.locator('h1')).toContainText('Create Your Organization');

    // Check for organization type cards (using specific test IDs)
    await expect(page.locator('[data-testid="yacht-card-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="training-provider-card-title"]')).toBeVisible();

    // Check for yacht features (using actual text)
    await expect(page.locator('text=Post job openings with yacht details')).toBeVisible();
    await expect(page.locator('text=Manage crew certificates and documents')).toBeVisible();

    // Check for training provider features (using actual text)
    await expect(page.locator('text=Create and manage course catalogs')).toBeVisible();
    await expect(page.locator('text=Issue certificates and credentials')).toBeVisible();

    console.log('✅ Organization selection page loads correctly');
  });

  test('should create yacht company and verify dashboard', async ({ page }) => {
    await page.goto('/organizations/new');

    // Select yacht company
    await page.click('[data-testid="create-yacht-button"]');
    await expect(page).toHaveURL('/organizations/create/yacht');

    // Verify yacht creation form loads (using actual title from yacht page)
    await expect(page.locator('h1')).toContainText('Create Yacht Profile');
    await expect(page.locator('input[name="name"]')).toBeVisible();

    // Fill out yacht company form
    const yachtName = 'Test Yacht Company E2E';
    await page.fill('input[name="name"]', yachtName);

    // Select yacht type (required field)
    await page.click('[data-testid="yacht-type-select"]');
    await page.waitForTimeout(500);
    await page.click('text=Motor Yacht');

    await page.fill('textarea[name="description"]', 'Test yacht company for E2E testing');
    await page.fill('input[name="website"]', 'https://testyacht.example.com');

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for success message or redirect
    await page.waitForTimeout(3000);

    // Should redirect to dashboard with success message
    await expect(page).toHaveURL('/dashboard');

    // Look for success message
    const successMessage = page.locator('text=Organization created successfully').or(
      page.locator('text=Business account created').or(
        page.locator('[role="alert"]')
      )
    );

    const successCount = await successMessage.count();
    if (successCount > 0) {
      await expect(successMessage.first()).toBeVisible();
      console.log('✅ Organization creation success message displayed');
    }

    // Verify organization appears in context switcher
    const contextSwitcher = page.locator('[data-testid="organization-context-switcher"]').or(
      page.locator('button').filter({ hasText: /personal|organization/i })
    );

    if (await contextSwitcher.isVisible()) {
      await contextSwitcher.click();
      await page.waitForTimeout(1000);

      // Look for the created organization
      const orgOption = page.locator(`text=${yachtName}`);
      const orgCount = await orgOption.count();

      if (orgCount > 0) {
        await expect(orgOption.first()).toBeVisible();
        console.log('✅ Organization appears in context switcher');

        // Switch to organization context
        await orgOption.first().click();
        await page.waitForTimeout(2000);

        // Verify we're in organization context
        const orgIndicator = page.locator(`text=${yachtName}`).or(
          page.locator('text=Yacht Company')
        );

        if (await orgIndicator.count() > 0) {
          console.log('✅ Successfully switched to organization context');
        }
      } else {
        console.log('ℹ️ Organization not found in context switcher - may need time to appear');
      }
    } else {
      console.log('ℹ️ Organization context switcher not found');
    }

    console.log('✅ Yacht company creation flow completed');
  });

  test('should create training provider and verify dashboard', async ({ page }) => {
    await page.goto('/organizations/new');

    // Select training provider
    await page.click('[data-testid="create-training-provider-button"]');
    await expect(page).toHaveURL('/organizations/create/training-provider');

    // Verify training provider creation form loads (using actual title)
    await expect(page.locator('h1')).toContainText('Create Training Provider');
    await expect(page.locator('input[name="name"]')).toBeVisible();

    // Fill out training provider form
    const providerName = 'Test Training Provider E2E';
    await page.fill('input[name="name"]', providerName);
    await page.fill('textarea[name="description"]', 'Test training provider for E2E testing');
    await page.fill('input[name="website"]', 'https://testtraining.example.com');

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for success message or redirect
    await page.waitForTimeout(3000);

    // Should redirect to dashboard with success message
    await expect(page).toHaveURL('/dashboard');

    // Look for success message
    const successMessage = page.locator('text=Organization created successfully').or(
      page.locator('text=Business account created').or(
        page.locator('[role="alert"]')
      )
    );

    const successCount = await successMessage.count();
    if (successCount > 0) {
      await expect(successMessage.first()).toBeVisible();
      console.log('✅ Training provider creation success message displayed');
    }

    console.log('✅ Training provider creation flow completed');
  });

  test('should handle organization creation form validation', async ({ page }) => {
    await page.goto('/organizations/create/yacht');

    // Try to submit empty form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);

    // Check for validation errors
    const nameField = page.locator('input[name="name"]');
    const isInvalid = await nameField.evaluate((el: HTMLInputElement) => !el.validity.valid);

    if (isInvalid) {
      console.log('✅ Empty form submission prevented by validation');
    }

    // Fill in minimum required fields
    await page.fill('input[name="name"]', 'Valid Organization Name');

    // Check that form is now valid
    const isValid = await nameField.evaluate((el: HTMLInputElement) => el.validity.valid);

    if (isValid) {
      console.log('✅ Form validation working correctly');
    }

    console.log('✅ Organization form validation tested');
  });

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/organizations/new');

    // Check if page is responsive
    await expect(page.locator('h1')).toBeVisible();

    // Check if organization cards are still visible and clickable (using test IDs)
    await expect(page.locator('[data-testid="yacht-card-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="training-provider-card-title"]')).toBeVisible();

    // Test navigation on mobile
    await page.click('[data-testid="create-yacht-button"]');
    await expect(page).toHaveURL('/organizations/create/yacht');

    // Check if form is responsive
    await expect(page.locator('input[name="name"]')).toBeVisible();

    console.log('✅ Organization pages are mobile responsive');
  });

  test('should navigate back to dashboard from organization creation', async ({ page }) => {
    await page.goto('/organizations/create/yacht');

    // Look for back button or navigation
    const backButton = page.locator('text=Back to Dashboard').or(
      page.locator('button').filter({ hasText: /back/i }).or(
        page.locator('[aria-label*="back"]')
      )
    );

    if (await backButton.isVisible()) {
      await backButton.click();
      await expect(page).toHaveURL('/dashboard');
      console.log('✅ Back navigation working');
    } else {
      // Try browser back button
      await page.goBack();
      await expect(page).toHaveURL('/organizations/new');
      console.log('✅ Browser back navigation working');
    }

    console.log('✅ Navigation from organization creation tested');
  });
});
