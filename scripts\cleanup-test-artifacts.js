/**
 * <PERSON><PERSON>t to clean up test artifacts and reports
 * Run this manually when test artifacts get too large
 */

const fs = require('fs');
const path = require('path');

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        totalSize += getDirectorySize(fullPath);
      } else {
        const stats = fs.statSync(fullPath);
        totalSize += stats.size;
      }
    }
  } catch (error) {
    // Ignore errors for individual files
  }
  
  return totalSize;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function cleanupDirectory(dirPath, description) {
  if (fs.existsSync(dirPath)) {
    const size = getDirectorySize(dirPath);
    console.log(`📁 ${description}: ${formatBytes(size)}`);
    
    if (size > 0) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Cleaned up ${description}`);
      return size;
    }
  }
  return 0;
}

function cleanupOldFiles(dirPath, maxAge = 7) {
  if (!fs.existsSync(dirPath)) return 0;
  
  let cleanedSize = 0;
  const cutoffTime = Date.now() - (maxAge * 24 * 60 * 60 * 1000);
  
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      const stats = fs.statSync(fullPath);
      
      if (stats.mtime.getTime() < cutoffTime) {
        const size = entry.isDirectory() ? getDirectorySize(fullPath) : stats.size;
        fs.rmSync(fullPath, { recursive: true, force: true });
        cleanedSize += size;
        console.log(`🗑️ Removed old ${entry.isDirectory() ? 'directory' : 'file'}: ${entry.name}`);
      }
    }
  } catch (error) {
    console.warn(`⚠️ Error cleaning old files in ${dirPath}:`, error.message);
  }
  
  return cleanedSize;
}

async function main() {
  console.log('🧹 Starting test artifacts cleanup...\n');
  
  let totalCleaned = 0;
  
  // Clean up main test directories
  totalCleaned += cleanupDirectory('test-results', 'Test Results');
  totalCleaned += cleanupDirectory('playwright-report', 'Playwright Reports');
  totalCleaned += cleanupDirectory('coverage', 'Coverage Reports');
  
  // Clean up temporary files
  const tempFiles = [
    'e2e/auth-state.json',
    '.nyc_output',
    'junit.xml',
    'test-report.xml'
  ];
  
  for (const file of tempFiles) {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      const size = stats.isDirectory() ? getDirectorySize(file) : stats.size;
      fs.rmSync(file, { recursive: true, force: true });
      totalCleaned += size;
      console.log(`🗑️ Removed: ${file}`);
    }
  }
  
  // Clean up old log files
  const logPatterns = [
    'npm-debug.log*',
    'yarn-debug.log*',
    'yarn-error.log*',
    '.pnpm-debug.log*',
    '*.log'
  ];
  
  for (const pattern of logPatterns) {
    try {
      const files = require('glob').sync(pattern);
      for (const file of files) {
        const stats = fs.statSync(file);
        fs.unlinkSync(file);
        totalCleaned += stats.size;
        console.log(`🗑️ Removed log: ${file}`);
      }
    } catch (error) {
      // Ignore glob errors
    }
  }
  
  // Clean up old node_modules/.cache if it exists
  const cacheDir = 'node_modules/.cache';
  if (fs.existsSync(cacheDir)) {
    totalCleaned += cleanupOldFiles(cacheDir, 3); // 3 days
  }
  
  // Clean up Next.js cache
  const nextCache = '.next/cache';
  if (fs.existsSync(nextCache)) {
    totalCleaned += cleanupOldFiles(nextCache, 1); // 1 day
  }
  
  console.log(`\n✅ Cleanup complete!`);
  console.log(`📊 Total space freed: ${formatBytes(totalCleaned)}`);
  
  if (totalCleaned === 0) {
    console.log('🎉 No cleanup needed - everything is already clean!');
  }
}

// Install glob if not available
try {
  require('glob');
} catch (error) {
  console.log('📦 Installing glob for pattern matching...');
  require('child_process').execSync('npm install glob --no-save', { stdio: 'inherit' });
}

main().catch(error => {
  console.error('❌ Cleanup failed:', error);
  process.exit(1);
});
