import { createPasswordReset, getUserByEmail } from "@/lib/db"
import { sendPasswordResetEmail } from "@/lib/email"
import { nanoid } from "nanoid"
import { NextResponse } from "next/server"

// Simple in-memory rate limiting (for production, use Redis or database)
const resetAttempts = new Map<string, { count: number; lastAttempt: number }>()
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const MAX_ATTEMPTS = 3 // Max 3 attempts per 15 minutes per IP

/**
 * Check rate limiting for password reset requests
 */
function checkRateLimit(ip: string): { allowed: boolean; remainingTime?: number } {
  const now = Date.now()
  const attempts = resetAttempts.get(ip)

  if (!attempts) {
    resetAttempts.set(ip, { count: 1, lastAttempt: now })
    return { allowed: true }
  }

  // Reset if window has passed
  if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {
    resetAttempts.set(ip, { count: 1, lastAttempt: now })
    return { allowed: true }
  }

  // Check if limit exceeded
  if (attempts.count >= MAX_ATTEMPTS) {
    const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - attempts.lastAttempt)) / 1000 / 60)
    return { allowed: false, remainingTime }
  }

  // Increment count
  resetAttempts.set(ip, { count: attempts.count + 1, lastAttempt: now })
  return { allowed: true }
}

/**
 * Handle forgot password requests
 */
export async function POST(request: Request) {
  try {
    // Get client IP for rate limiting
    const forwarded = request.headers.get("x-forwarded-for")
    const ip = forwarded ? forwarded.split(",")[0] : "unknown"

    // Check rate limiting
    const rateLimitCheck = checkRateLimit(ip)
    if (!rateLimitCheck.allowed) {
      return NextResponse.json({
        success: false,
        error: `Too many password reset attempts. Please try again in ${rateLimitCheck.remainingTime} minutes.`
      }, { status: 429 })
    }

    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({
        success: false,
        error: "Email address is required"
      }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        error: "Please enter a valid email address"
      }, { status: 400 })
    }

    // Get user by email
    const user = await getUserByEmail(email.toLowerCase().trim())

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists and has a password
    if (user && user.password) {
      // Generate password reset token
      const token = nanoid(32)
      const expiresAt = new Date()
      expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.PASSWORD_RESET_TOKEN_EXPIRY_HOURS || '48'))

      // Create password reset record
      await createPasswordReset(user.id, token, expiresAt)

      // Send password reset email
      const emailResult = await sendPasswordResetEmail(user.email, user.name, token)

      if (!emailResult.success) {
        console.error("Failed to send password reset email:", emailResult.error)
        // Don't expose email sending errors to prevent information disclosure
      }
    } else if (user && !user.password) {
      // User exists but uses OAuth only - don't send reset email
      console.log(`Password reset requested for OAuth-only user: ${email}`)
    } else {
      // User doesn't exist - don't send email
      console.log(`Password reset requested for non-existent user: ${email}`)
    }

    // Always return success message to prevent email enumeration
    return NextResponse.json({
      success: true,
      message: "If an account with that email exists and has a password, we've sent password reset instructions."
    })

  } catch (error) {
    console.error("Forgot password error:", error)
    return NextResponse.json({
      success: false,
      error: "Something went wrong. Please try again later."
    }, { status: 500 })
  }
}
