/**
 * Utility functions for handling certificate document downloads
 */

export interface DownloadResponse {
  downloadUrl?: string;
  viewUrl?: string;
  fileName: string;
  fileSize?: string;
  fileType?: string;
  certificateName: string;
}

export interface CertificateFile {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
}

export interface ZipDownloadData {
  certificateName: string;
  zipFileName: string;
  files: CertificateFile[];
}

/**
 * Download a certificate document using the download API
 */
export async function downloadCertificateDocument(certificateId: string): Promise<void> {
  const response = await fetch(`/api/certificates/${certificateId}/download`, {
    method: 'GET',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `Download failed with status ${response.status}`);
  }

  // Get the filename from the Content-Disposition header
  const contentDisposition = response.headers.get('Content-Disposition');
  let fileName = `certificate-${certificateId}.pdf`;

  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
    if (fileNameMatch) {
      fileName = fileNameMatch[1];
    }
  }

  // Create blob and trigger download
  const blob = await response.blob();
  triggerBlobDownload(blob, fileName);
}

/**
 * Get certificate document URL for viewing
 */
export async function getCertificateViewUrl(certificateId: string): Promise<DownloadResponse> {
  const response = await fetch(`/api/certificates/${certificateId}/view`, {
    method: 'GET',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `View failed with status ${response.status}`);
  }

  return response.json();
}

/**
 * Download a certificate document by proxying through the server
 * This method provides more security but uses more server resources
 */
export async function downloadCertificateDocumentProxy(certificateId: string): Promise<Blob> {
  const response = await fetch(`/api/certificates/${certificateId}/download`, {
    method: 'POST',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `Download failed with status ${response.status}`);
  }

  return response.blob();
}

/**
 * Trigger a file download in the browser
 */
export function triggerFileDownload(url: string, fileName: string) {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';

  // Append to body, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Trigger a blob download in the browser
 */
export function triggerBlobDownload(blob: Blob, fileName: string) {
  const url = URL.createObjectURL(blob);
  triggerFileDownload(url, fileName);
  // Clean up the object URL after a reasonable delay
  setTimeout(() => URL.revokeObjectURL(url), 1000);
}

/**
 * Open a file in a new browser tab for viewing
 */
export function openFileInNewTab(url: string) {
  window.open(url, '_blank', 'noopener,noreferrer');
}

/**
 * Determine if a file type is viewable in browser
 */
export function isFileViewable(fileName?: string, fileType?: string): boolean {
  if (!fileName && !fileType) return false;

  const extension = fileName?.toLowerCase().split('.').pop();
  const mimeType = fileType?.toLowerCase();

  // Viewable file types
  const viewableExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'htm'];
  const viewableMimeTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'text/plain',
    'text/html'
  ];

  return Boolean(extension && viewableExtensions.includes(extension)) ||
    Boolean(mimeType && viewableMimeTypes.some(type => mimeType.includes(type)));
}

/**
 * Get file type icon based on file extension or MIME type
 */
export function getFileTypeIcon(fileName?: string, fileType?: string): string {
  if (!fileName && !fileType) return '📄';

  const extension = fileName?.toLowerCase().split('.').pop();
  const mimeType = fileType?.toLowerCase();

  if (extension === 'pdf' || mimeType?.includes('pdf')) return '📄';
  if (extension === 'jpg' || extension === 'jpeg' || mimeType?.includes('jpeg')) return '🖼️';
  if (extension === 'png' || mimeType?.includes('png')) return '🖼️';
  if (extension === 'doc' || extension === 'docx' || mimeType?.includes('word')) return '📝';
  if (extension === 'xls' || extension === 'xlsx' || mimeType?.includes('excel')) return '📊';

  return '📄';
}

/**
 * Format file size for display
 */
export function formatFileSize(sizeString?: string): string {
  if (!sizeString) return '';

  // If it's already formatted (e.g., "1.2 MB"), return as is
  if (sizeString.includes(' ')) return sizeString;

  // If it's a number string, format it
  const size = parseInt(sizeString, 10);
  if (isNaN(size)) return sizeString;

  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${Math.round(size / 1024)} KB`;
  if (size < 1024 * 1024 * 1024) return `${Math.round(size / (1024 * 1024))} MB`;

  return `${Math.round(size / (1024 * 1024 * 1024))} GB`;
}

/**
 * Check if a certificate has a downloadable document
 */
export function hasDownloadableDocument(certificate: { documentUrl?: string | null }): boolean {
  return Boolean(certificate.documentUrl);
}

/**
 * Get all files for a certificate
 */
export async function getCertificateFiles(certificateId: string): Promise<CertificateFile[]> {
  const response = await fetch(`/api/certificates/${certificateId}/files`, {
    method: 'GET',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `Failed to get files with status ${response.status}`);
  }

  return response.json();
}

/**
 * Open all certificate files in new tabs
 */
export async function viewAllCertificateFiles(certificateId: string): Promise<void> {
  try {
    const files = await getCertificateFiles(certificateId);

    if (files.length === 0) {
      throw new Error('No files found for this certificate');
    }

    // Open each file in a new tab
    files.forEach((file, index) => {
      // Add a small delay between opening tabs to avoid popup blockers
      setTimeout(() => {
        openFileInNewTab(file.fileUrl);
      }, index * 100);
    });
  } catch (error) {
    console.error('Error viewing certificate files:', error);
    throw error;
  }
}

/**
 * Download all certificate files as a ZIP (client-side)
 */
export async function downloadCertificateFilesAsZip(certificateId: string): Promise<void> {
  try {
    // Get ZIP download data from API
    const response = await fetch(`/api/certificates/${certificateId}/download-zip`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `Failed to prepare ZIP download with status ${response.status}`);
    }

    const zipData: ZipDownloadData = await response.json();

    if (zipData.files.length === 0) {
      throw new Error('No files found for this certificate');
    }

    // If only one file, download it directly
    if (zipData.files.length === 1) {
      const file = zipData.files[0];
      triggerFileDownload(file.fileUrl, file.fileName);
      return;
    }

    // For multiple files, create ZIP using dynamic import
    await createAndDownloadZip(zipData);
  } catch (error) {
    console.error('Error downloading certificate files as ZIP:', error);
    throw error;
  }
}

/**
 * Create and download ZIP file using dynamic import of JSZip
 */
async function createAndDownloadZip(zipData: ZipDownloadData): Promise<void> {
  try {
    // Dynamic import of JSZip to avoid build issues
    const JSZip = (await import('jszip')).default;
    const zip = new JSZip();
    const fileNameCounts: { [key: string]: number } = {};

    // Fetch and add each file to the ZIP
    for (const file of zipData.files) {
      try {
        const response = await fetch(file.fileUrl);
        if (!response.ok) {
          console.warn(`Failed to fetch file ${file.fileName}, skipping...`);
          continue;
        }

        const blob = await response.blob();

        // Handle duplicate filenames
        let fileName = file.fileName;
        if (fileNameCounts[fileName]) {
          fileNameCounts[fileName]++;
          const extension = fileName.split('.').pop();
          const nameWithoutExt = fileName.replace(`.${extension}`, '');
          fileName = `${nameWithoutExt}_${fileNameCounts[fileName]}.${extension}`;
        } else {
          fileNameCounts[fileName] = 1;
        }

        zip.file(fileName, blob);
      } catch (error) {
        console.warn(`Error adding file ${file.fileName} to ZIP:`, error);
        // Continue with other files
      }
    }

    // Generate and download ZIP
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    triggerBlobDownload(zipBlob, zipData.zipFileName);
  } catch (error) {
    console.error('Error creating ZIP file:', error);
    throw new Error('Failed to create ZIP file. Please try downloading files individually.');
  }
}
