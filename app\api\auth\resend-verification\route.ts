import { createEmailVerification, getUserByEmail, updateUserVerificationToken } from "@/lib/db"
import { sendVerificationReminderEmail } from "@/lib/email"
import { nanoid } from "nanoid"
import { NextResponse } from "next/server"

// Simple in-memory rate limiting (for production, use Redis or database)
const rateLimitMap = new Map<string, { lastRequest: number; attempts: number }>()
const RATE_LIMIT_WINDOW_MS = 60 * 1000 // 1 minute
const MAX_ATTEMPTS_PER_WINDOW = 3

/**
 * Check rate limiting for email resend requests
 */
function checkRateLimit(email: string): { allowed: boolean; remainingTime?: number } {
  const now = Date.now()
  const userLimit = rateLimitMap.get(email)

  if (!userLimit) {
    rateLimitMap.set(email, { lastRequest: now, attempts: 1 })
    return { allowed: true }
  }

  // Reset if window has passed
  if (now - userLimit.lastRequest > RATE_LIMIT_WINDOW_MS) {
    rateLimitMap.set(email, { lastRequest: now, attempts: 1 })
    return { allowed: true }
  }

  // Check if exceeded attempts
  if (userLimit.attempts >= MAX_ATTEMPTS_PER_WINDOW) {
    const remainingTime = Math.ceil((RATE_LIMIT_WINDOW_MS - (now - userLimit.lastRequest)) / 1000)
    return { allowed: false, remainingTime }
  }

  // Increment attempts
  userLimit.attempts += 1
  userLimit.lastRequest = now
  return { allowed: true }
}

/**
 * Resend email verification with rate limiting
 */
export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({
        success: false,
        error: "Email address is required"
      }, { status: 400 })
    }

    // Check rate limiting
    const rateCheck = checkRateLimit(email)
    if (!rateCheck.allowed) {
      return NextResponse.json({
        success: false,
        error: `Too many requests. Please wait ${rateCheck.remainingTime} seconds before trying again.`
      }, { status: 429 })
    }

    // Get user by email
    const user = await getUserByEmail(email)

    if (!user) {
      // Don't reveal if email exists or not for security
      return NextResponse.json({
        success: true,
        message: "If an account with this email exists and is unverified, a verification email has been sent."
      })
    }

    // Check if already verified
    if (user.emailVerified) {
      return NextResponse.json({
        success: false,
        error: "Email address is already verified"
      }, { status: 400 })
    }

    // Generate new verification token
    const token = nanoid(32)
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS || '48'))

    // Create verification record
    await createEmailVerification(user.id, token, expiresAt)

    // Update user with new token
    await updateUserVerificationToken(user.id, token, expiresAt)

    // Send verification email
    const emailResult = await sendVerificationReminderEmail(user.email, user.name, token)

    if (!emailResult.success) {
      console.error("Failed to send verification email:", emailResult.error)

      // Development mode: Show verification link in console for testing
      if (process.env.NODE_ENV === 'development') {
        const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${token}`
        console.log('\n🔧 DEVELOPMENT MODE - Manual Verification Link (Resend):')
        console.log(`📧 User: ${user.name} (${user.email})`)
        console.log(`🔗 Verification URL: ${verificationUrl}`)
        console.log('✅ Use this link to manually verify the account\n')
      }

      return NextResponse.json({
        success: false,
        error: "Failed to send verification email. Please try again later."
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: "Verification email sent successfully"
    })

  } catch (error) {
    console.error("Resend verification error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to resend verification email. Please try again."
    }, { status: 500 })
  }
}
