#!/usr/bin/env node

/**
 * Verify Admin User Script
 * Checks that the admin user exists with proper password and verification status
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/neon-http");
const { neon } = require("@neondatabase/serverless");
const { eq } = require("drizzle-orm");
const bcrypt = require("bcryptjs");
const { text, timestamp, boolean, pgTable } = require("drizzle-orm/pg-core");

// Define users table schema for this script
const users = pgTable("User", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password"),
  role: text("role").default("individual_user").notNull(),
  subscriptionPlan: text("subscriptionPlan").default("individual_free").notNull(),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  lastLoginAt: timestamp("lastLoginAt"),
  deletedAt: timestamp("deletedAt"),
});

// Admin user configuration
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'AdminSealog2025!';

async function verifyAdminUser() {
  try {
    console.log('🔍 Verifying admin user in database...');
    
    // Validate environment
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    // Initialize database connection
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    // Find admin user
    const adminUsers = await db
      .select()
      .from(users)
      .where(eq(users.email, ADMIN_EMAIL))
      .limit(1);

    if (adminUsers.length === 0) {
      console.log('❌ Admin user not found in database');
      console.log(`   Expected email: ${ADMIN_EMAIL}`);
      console.log('   Run: pnpm seed:admin-user');
      return;
    }

    const admin = adminUsers[0];
    console.log('✅ Admin user found in database');
    console.log('');
    console.log('📋 Admin User Details:');
    console.log(`   ID: ${admin.id}`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Name: ${admin.name}`);
    console.log(`   Role: ${admin.role}`);
    console.log(`   Subscription: ${admin.subscriptionPlan}`);
    console.log(`   Email Verified: ${admin.emailVerified}`);
    console.log(`   Created: ${admin.createdAt}`);
    console.log(`   Updated: ${admin.updatedAt}`);
    console.log(`   Last Login: ${admin.lastLoginAt || 'Never'}`);
    console.log(`   Deleted: ${admin.deletedAt || 'No'}`);
    console.log('');

    // Check password
    if (!admin.password) {
      console.log('❌ Admin user has no password set');
      console.log('   Run: pnpm seed:admin-user (to update with password)');
      return;
    }

    console.log('✅ Admin user has encrypted password');

    // Verify password can be checked
    try {
      const passwordValid = await bcrypt.compare(ADMIN_PASSWORD, admin.password);
      if (passwordValid) {
        console.log('✅ Password verification successful');
      } else {
        console.log('❌ Password verification failed');
        console.log('   The stored password does not match the expected password');
      }
    } catch (error) {
      console.log('❌ Error verifying password:', error.message);
    }

    // Check role and verification status
    if (admin.role === 'system_admin') {
      console.log('✅ Admin role correctly set');
    } else {
      console.log(`❌ Admin role incorrect: ${admin.role} (expected: system_admin)`);
    }

    if (admin.emailVerified) {
      console.log('✅ Email verified status correct');
    } else {
      console.log('❌ Email not verified (should be true for admin)');
    }

    console.log('');
    console.log('🌐 Admin Access:');
    console.log('   Login URL: http://localhost:3000/login');
    console.log('   Admin Dashboard: http://localhost:3000/admin');
    console.log(`   Email: ${admin.email}`);
    console.log(`   Password: ${ADMIN_PASSWORD}`);

  } catch (error) {
    console.error('❌ Error verifying admin user:', error.message);
    process.exit(1);
  }
}

// Main execution
verifyAdminUser();
