import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Running global teardown...');
  
  // Clean up authentication state
  try {
    if (fs.existsSync('e2e/auth-state.json')) {
      fs.unlinkSync('e2e/auth-state.json');
      console.log('✅ Cleaned up authentication state');
    }
  } catch (error) {
    console.warn('⚠️ Could not clean up auth state:', error);
  }
  
  // Keep only the latest test results (last 3 runs)
  try {
    const testResultsDir = 'test-results';
    if (fs.existsSync(testResultsDir)) {
      const entries = fs.readdirSync(testResultsDir, { withFileTypes: true });
      const directories = entries
        .filter(entry => entry.isDirectory())
        .map(entry => ({
          name: entry.name,
          path: path.join(testResultsDir, entry.name),
          stats: fs.statSync(path.join(testResultsDir, entry.name))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());
      
      // Keep only the 3 most recent test result directories
      const toDelete = directories.slice(3);
      for (const dir of toDelete) {
        fs.rmSync(dir.path, { recursive: true, force: true });
        console.log(`✅ Cleaned up old test results: ${dir.name}`);
      }
    }
  } catch (error) {
    console.warn('⚠️ Could not clean up old test results:', error);
  }
  
  // Compress large report files if they exist
  try {
    const reportDir = 'playwright-report';
    if (fs.existsSync(reportDir)) {
      const reportSize = getDirectorySize(reportDir);
      if (reportSize > 50 * 1024 * 1024) { // 50MB
        console.log(`📦 Report directory is ${Math.round(reportSize / 1024 / 1024)}MB, consider archiving`);
      }
    }
  } catch (error) {
    console.warn('⚠️ Could not check report size:', error);
  }
  
  console.log('✅ Global teardown complete');
}

function getDirectorySize(dirPath: string): number {
  let totalSize = 0;
  
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        totalSize += getDirectorySize(fullPath);
      } else {
        const stats = fs.statSync(fullPath);
        totalSize += stats.size;
      }
    }
  } catch (error) {
    // Ignore errors for individual files
  }
  
  return totalSize;
}

export default globalTeardown;
