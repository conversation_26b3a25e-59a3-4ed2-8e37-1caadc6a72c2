"use client";

import {
  Bell,
  HelpCircle,
  LogOut,
  Search,
  Settings,
  Shield,
  User,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

import { OrganizationContextSwitcher } from "@/components/organization-context-switcher";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useNotifications } from "@/hooks/use-notifications";
import { useRole } from "@/hooks/use-session";

interface UserData {
  name: string;
  email: string;
}

export function AppTopbar() {
  const pathname = usePathname();
  const [userData, setUserData] = useState<UserData | null>(null);
  const { isSystemAdmin } = useRole();

  // Get real notification data
  const { notifications, unreadCount } = useNotifications({
    includeRead: false,
    autoRefresh: true,
    refreshInterval: 30000,
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch("/api/user/profile", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setUserData({
              name: data.user.name,
              email: data.user.email,
            });
          }
        }
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        // Fallback to default values if fetch fails
        setUserData({
          name: "User",
          email: "<EMAIL>",
        });
      }
    };

    fetchUserData();
  }, []);

  // Function to get the current page title based on the pathname
  const getPageTitle = () => {
    if (pathname === "/dashboard") return "Dashboard";
    if (pathname === "/certificates") return "Certificates";
    if (pathname?.startsWith("/certificates/new")) return "Add New Certificate";
    if (pathname?.startsWith("/certificates/")) return "Certificate Details";
    if (pathname === "/profile") return "My Profile";
    if (pathname === "/settings") return "Settings";
    return "Sealog";
  };

  const handleLogout = async () => {
    try {
      // First, call our custom logout API to clear all sessions
      const logoutResponse = await fetch("/api/logout", {
        method: "POST",
        credentials: "include",
      });

      if (!logoutResponse.ok) {
        console.error("Logout API failed:", await logoutResponse.text());
      }

      // Also use NextAuth signOut to ensure complete cleanup
      // Import signOut dynamically to avoid SSR issues
      const { signOut } = await import("next-auth/react");
      await signOut({
        redirect: false, // We'll handle redirect manually
        callbackUrl: "/login",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Force redirect to login page and clear any cached data
    // Use replace to prevent back button issues
    window.location.replace("/login");
  };

  // Format date for display
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="flex h-16 items-center gap-2 sm:gap-4 w-full">
      {/* Mobile: Add left padding to account for mobile menu button */}
      <div className="text-lg font-semibold ml-12 md:ml-0 truncate">
        {getPageTitle()}
      </div>

      <div className="ml-auto flex items-center gap-1 sm:gap-2 md:gap-4">
        {/* Mobile Search - Hidden on very small screens, shown as icon on small screens */}
        <div className="hidden sm:flex md:hidden">
          <Button variant="ghost" size="icon" className="h-9 w-9">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
        </div>

        {/* Desktop Search */}
        <div className="hidden md:flex relative w-40 lg:w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search..."
            className="w-full pl-8 md:w-40 lg:w-64"
            autoComplete="off"
            name="search"
          />
        </div>

        {/* Organization Context Switcher */}
        <OrganizationContextSwitcher />

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="relative h-9 w-9"
              data-testid="notification-bell"
            >
              <Bell className="h-4 w-4" />
              {unreadCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs">
                  {unreadCount > 99 ? "99+" : unreadCount}
                </Badge>
              )}
              <span className="sr-only">Notifications</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-80 sm:w-96 p-0">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">Notifications</h4>
                <Link
                  href="/notifications"
                  className="text-xs text-primary hover:underline"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="max-h-80 overflow-auto">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No new notifications
                </div>
              ) : (
                notifications.slice(0, 5).map((notification) => (
                  <div
                    key={notification.id}
                    className="p-4 border-b hover:bg-muted/50"
                  >
                    <div className="flex justify-between items-start mb-1">
                      <h5 className="font-medium text-sm">
                        {notification.title}
                      </h5>
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(notification.createdAt)}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {notification.message}
                    </p>
                  </div>
                ))
              )}
            </div>
          </PopoverContent>
        </Popover>

        <Button
          variant="ghost"
          size="icon"
          className="hidden sm:flex h-9 w-9"
          asChild
        >
          <Link href="/help">
            <HelpCircle className="h-4 w-4" />
            <span className="sr-only">Help</span>
          </Link>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full h-9 w-9"
            >
              <Avatar className="h-7 w-7">
                <AvatarImage
                  src="/placeholder.svg?height=32&width=32"
                  alt={userData?.name || "User"}
                />
                <AvatarFallback className="text-xs">
                  {userData?.name
                    ? userData.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()
                        .slice(0, 2)
                    : "U"}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {userData?.name || "Loading..."}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {userData?.email || ""}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile" className="cursor-pointer">
                <User className="mr-2 h-4 w-4" /> My Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="cursor-pointer">
                <Settings className="mr-2 h-4 w-4" /> Account Settings
              </Link>
            </DropdownMenuItem>
            {isSystemAdmin && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin" className="cursor-pointer">
                    <Shield className="mr-2 h-4 w-4" /> Admin Dashboard
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleLogout}
              className="text-red-500 focus:text-red-500 cursor-pointer"
            >
              <LogOut className="mr-2 h-4 w-4" /> Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
