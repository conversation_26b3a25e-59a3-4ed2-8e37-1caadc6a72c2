import { cookies } from "next/headers";
import { createUploadthing, type FileRouter } from "uploadthing/next";

const f = createUploadthing();

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Certificate document uploader
  certificateUploader: f({
    "application/pdf": { maxFileSize: "8MB", maxFileCount: 5 },
    "image/jpeg": { maxFileSize: "8MB", maxFileCount: 5 },
    "image/png": { maxFileSize: "8MB", maxFileCount: 5 },
  })
    .middleware(async ({ req, files }) => {
      // Authentication check
      const user = await getUserFromSession();
      if (!user) throw new Error("Unauthorized");

      // File validation
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      const maxFileSize = 8 * 1024 * 1024; // 8MB in bytes
      const maxFiles = 5;

      if (files.length > maxFiles) {
        throw new Error(`Maximum ${maxFiles} files allowed`);
      }

      for (const file of files) {
        // Validate file type
        if (!allowedTypes.includes(file.type)) {
          throw new Error(`File type ${file.type} not allowed. Allowed types: ${allowedTypes.join(', ')}`);
        }

        // Validate file size
        if (file.size > maxFileSize) {
          throw new Error(`File ${file.name} exceeds maximum size of 8MB`);
        }

        // Validate file name (prevent path traversal)
        if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
          throw new Error(`Invalid file name: ${file.name}`);
        }

        // Check for suspicious file extensions
        const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.jar'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        if (suspiciousExtensions.includes(fileExtension)) {
          throw new Error(`File extension ${fileExtension} not allowed for security reasons`);
        }
      }

      return {
        userId: user.id,
        uploadedAt: new Date().toISOString(),
        userAgent: req.headers.get('user-agent') || 'unknown'
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId);
      console.log("File uploaded:", {
        name: file.name,
        size: file.size,
        type: file.type,
        url: file.url
      });

      // Return complete file metadata to the client
      return {
        uploadedBy: metadata.userId,
        url: file.url,
        name: file.name,
        size: file.size,
        type: file.type,
        key: file.key
      };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
