import { z } from 'zod';

// Account deletion validation
export const accountDeletionSchema = z.object({
  confirmEmail: z.string().email('Invalid email format'),
  reason: z.string().optional(),
  immediateDelete: z.boolean().default(false)
});

// Certificate creation validation
export const certificateCreateSchema = z.object({
  name: z.string().min(1, 'Certificate name is required').max(200, 'Name too long'),
  issuingAuthority: z.string().min(1, 'Issuing authority is required').max(200, 'Authority name too long'),
  certificateNumber: z.string().min(1, 'Certificate number is required').max(100, 'Number too long'),
  dateIssued: z.string().datetime('Invalid date format'),
  expiryDate: z.string().datetime('Invalid date format').optional().nullable(),
  notes: z.string().max(1000, 'Notes too long').optional().nullable(),
  isFavorite: z.boolean().default(false)
});

// File upload validation
export const fileUploadSchema = z.object({
  files: z.array(z.object({
    id: z.string(),
    fileName: z.string().min(1, 'File name required'),
    fileUrl: z.string().url('Invalid file URL'),
    fileSize: z.number().positive('File size must be positive'),
    fileType: z.string().min(1, 'File type required'),
    uploadthingKey: z.string().optional()
  })).max(5, 'Maximum 5 files allowed')
});

// User registration validation
export const userRegistrationSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long'),
  role: z.enum(['individual_user', 'system_admin']).default('individual_user') // Only valid user roles
});

// Account recovery validation
export const accountRecoverySchema = z.object({
  userId: z.string().min(1, 'User ID required'),
  recoveryToken: z.string().min(1, 'Recovery token required'),
  email: z.string().email('Invalid email format').optional()
});

// Email verification validation
export const emailVerificationSchema = z.object({
  token: z.string().min(1, 'Verification token required'),
  email: z.string().email('Invalid email format').optional()
});

// Password reset validation
export const passwordResetSchema = z.object({
  token: z.string().min(1, 'Reset token required'),
  password: z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Login validation
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
});

// Forgot password validation
export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format')
});

// Resend verification validation
export const resendVerificationSchema = z.object({
  email: z.string().email('Invalid email format')
});

// Link account validation
export const linkAccountSchema = z.object({
  provider: z.enum(['google', 'facebook'], {
    errorMap: () => ({ message: 'Provider must be google or facebook' })
  })
});

// Helper function to validate request body
export async function validateRequestBody<T>(
  request: Request,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(', ')}`);
    }
    throw new Error('Invalid request body');
  }
}

// Helper function to validate query parameters
export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): T {
  try {
    const params = Object.fromEntries(searchParams.entries());
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Query validation failed: ${error.errors.map(e => e.message).join(', ')}`);
    }
    throw new Error('Invalid query parameters');
  }
}
