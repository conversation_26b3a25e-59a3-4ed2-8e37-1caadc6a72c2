import { authUrls, getRouteConfig, SessionContext } from "@/lib/auth-config"
import { getToken } from "next-auth/jwt"
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"

/**
 * Parse session from cookie (middleware version)
 * Simplified version for middleware - full parsing in session.ts
 */
function parseSessionFromCookie(sessionCookie: string): SessionContext | null {
  try {
    const sessionData = JSON.parse(sessionCookie)

    // Handle current simple session format
    if (sessionData.user && !sessionData.user.role) {
      return {
        userId: sessionData.user.id,
        email: sessionData.user.email,
        name: sessionData.user.name,
        role: "individual_user" as any, // Default role for existing users
        subscriptionPlan: "individual_free" as any, // Default plan
        permissions: [], // Will be populated by session utilities
        emailVerified: sessionData.user.emailVerified || false, // Default to false for safety
        tenantId: undefined,
        tenantRole: undefined
      }
    }

    // Handle enhanced session format (future)
    if (sessionData.user && sessionData.user.role) {
      return {
        userId: sessionData.user.id,
        email: sessionData.user.email,
        name: sessionData.user.name,
        role: sessionData.user.role,
        subscriptionPlan: sessionData.user.subscriptionPlan,
        permissions: sessionData.user.permissions || [],
        emailVerified: sessionData.user.emailVerified || false,
        tenantId: sessionData.user.tenantId,
        tenantRole: sessionData.user.tenantRole
      }
    }

    return null
  } catch {
    return null
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check for NextAuth JWT token first
  const nextAuthToken = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  })

  // Get the custom session cookie as fallback
  const sessionCookie = request.cookies.get("session")
  const hasCustomSession = !!sessionCookie?.value
  const sessionContext = sessionCookie?.value ? parseSessionFromCookie(sessionCookie.value) : null

  // User is authenticated if they have either NextAuth token or custom session
  const hasSession = !!nextAuthToken || hasCustomSession
  const effectivelyAuthenticated = hasSession

  // If we have a NextAuth token but no custom session cookie, create one
  let response: NextResponse | undefined
  if (nextAuthToken && !hasCustomSession) {
    console.log("Middleware: Creating custom session cookie from NextAuth token", {
      userId: nextAuthToken.id,
      email: nextAuthToken.email,
      role: nextAuthToken.role
    })

    const sessionData = {
      user: {
        id: nextAuthToken.id as string,
        email: nextAuthToken.email as string,
        name: nextAuthToken.name as string,
        role: nextAuthToken.role as string || "individual_user",
        subscriptionPlan: nextAuthToken.subscriptionPlan as string || "individual_free",
        emailVerified: nextAuthToken.emailVerified as boolean || false
      }
    }

    response = NextResponse.next()
    response.cookies.set({
      name: "session",
      value: JSON.stringify(sessionData),
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 1 week
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict", // ✅ More secure
      // ✅ Add security headers
      ...(process.env.NODE_ENV === "production" && {
        domain: process.env.COOKIE_DOMAIN || undefined
      })
    })

    // ✅ Add security headers to response
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  // Get route configuration
  const routeConfig = getRouteConfig(pathname)

  if (!routeConfig) {
    // Fallback: require authentication for unknown routes
    if (!effectivelyAuthenticated) {
      const url = new URL(authUrls.login, request.url)
      url.searchParams.set("callbackUrl", encodeURI(request.url))
      return NextResponse.redirect(url)
    }
    return response || NextResponse.next()
  }

  // Handle protected routes with role-based access control
  if (routeConfig.requiresAuth && !effectivelyAuthenticated) {
    const url = new URL(authUrls.login, request.url)
    url.searchParams.set("callbackUrl", encodeURI(request.url))
    return NextResponse.redirect(url)
  }

  // Enhanced RBAC validation for admin routes
  if (effectivelyAuthenticated && routeConfig.allowedRoles) {
    try {
      let userRole: string | null = null

      // Get user role from NextAuth token or session context
      if (nextAuthToken) {
        userRole = nextAuthToken.role as string
      } else if (sessionContext) {
        userRole = sessionContext.role
      }

      // Check if user has required role for this route
      if (userRole && !routeConfig.allowedRoles.includes(userRole as any)) {
        console.log(`Access denied: User role ${userRole} not in allowed roles ${routeConfig.allowedRoles.join(", ")} for ${pathname}`)
        return NextResponse.redirect(new URL("/dashboard?error=unauthorized", request.url))
      }
    } catch (error) {
      console.error("Error checking role-based access:", error)
      // On error, allow access to prevent blocking users
    }
  }

  // Check email verification for authenticated users accessing protected routes
  if (routeConfig.requiresAuth && effectivelyAuthenticated) {
    try {
      let emailVerified: boolean = false

      // CRITICAL FIX: Always fetch fresh user status from database
      // This ensures verification status and deletion status changes are immediately reflected in middleware
      let user: any = null
      if (nextAuthToken) {
        // For NextAuth users, check the database
        if (nextAuthToken.sub) {
          const { getUserById } = await import("@/lib/db-edge")
          user = await getUserById(nextAuthToken.sub)
          emailVerified = user?.emailVerified || false
        }
      } else if (sessionContext) {
        // For custom sessions, also check the database for fresh status
        const { getUserById } = await import("@/lib/db-edge")
        user = await getUserById(sessionContext.userId)
        emailVerified = user?.emailVerified || false
      }

      // Check if user account is deleted or scheduled for deletion
      if (user && (user.deletedAt || user.deletionRequestedAt)) {
        console.log(`Blocking access for deleted/scheduled user ${user.id}, redirecting to login`)

        // Clear all session cookies for deleted accounts
        const response = NextResponse.redirect(new URL('/login?message=account_deleted', request.url))
        response.cookies.delete("session")
        response.cookies.delete("next-auth.session-token")
        response.cookies.delete("__Secure-next-auth.session-token")

        return response
      }

      // Block unverified users from protected routes (except verification-related routes)
      const isVerificationRoute = pathname.startsWith('/verify-email') ||
        pathname.startsWith('/verification-pending') ||
        pathname.startsWith('/api/auth/verify-email') ||
        pathname.startsWith('/api/auth/resend-verification')

      if (!emailVerified && !isVerificationRoute) {
        console.log(`Blocking unverified user from ${pathname}, redirecting to verification-pending`)
        return NextResponse.redirect(new URL('/verification-pending', request.url))
      }

      // If user is verified and trying to access verification-pending, redirect to dashboard
      // But allow access to verify-email page for processing verification links
      if (emailVerified && pathname.startsWith('/verification-pending')) {
        return NextResponse.redirect(new URL(authUrls.dashboard, request.url))
      }

    } catch (error) {
      console.error('Email verification check error in middleware:', error)
      // On error, allow access but log the issue
    }
  }

  // Future: Enhanced route validation
  // if (sessionContext && !validateRouteAccess(sessionContext, routeConfig)) {
  //   return NextResponse.redirect(new URL("/unauthorized", request.url))
  // }

  // Handle auth routes when already logged in
  // CRITICAL FIX: Only redirect VERIFIED users away from auth routes
  // Allow unverified users to access signup to create new accounts
  const isAuthRoute = pathname.startsWith("/login") || pathname.startsWith("/signup")
  if (isAuthRoute && effectivelyAuthenticated) {
    try {
      let emailVerified: boolean = false

      // Check verification status for authenticated users on auth routes
      if (nextAuthToken) {
        if (nextAuthToken.sub) {
          const { getUserById } = await import("@/lib/db-edge")
          const user = await getUserById(nextAuthToken.sub)
          emailVerified = user?.emailVerified || false
        }
      } else if (sessionContext) {
        const { getUserById } = await import("@/lib/db-edge")
        const user = await getUserById(sessionContext.userId)
        emailVerified = user?.emailVerified || false
      }

      // Only redirect verified users away from auth routes
      // Unverified users can access signup to create new accounts
      if (emailVerified) {
        console.log(`Redirecting verified user from ${pathname} to dashboard`)
        return NextResponse.redirect(new URL(authUrls.dashboard, request.url))
      } else {
        console.log(`Allowing unverified user to access ${pathname}`)
        // Allow unverified users to access auth routes
      }
    } catch (error) {
      console.error('Email verification check error for auth routes:', error)
      // On error, allow access to prevent blocking users
    }
  }

  // Redirect authenticated users from root to dashboard
  if (pathname === "/" && effectivelyAuthenticated) {
    return NextResponse.redirect(new URL(authUrls.dashboard, request.url))
  }

  return response || NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}
