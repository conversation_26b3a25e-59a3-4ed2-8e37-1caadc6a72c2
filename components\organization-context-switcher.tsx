"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getRoleDisplayName } from "@/lib/organization-permissions";
import { Building2, Check, ChevronDown, User } from "lucide-react";
import { useEffect, useState } from "react";

interface Organization {
  id: string;
  name: string;
  type: "yacht_company" | "cert_provider";
  status: "pending" | "verified" | "suspended";
  membershipRole: "owner" | "admin" | "member";
  joinedAt: string;
}

interface OrganizationContextSwitcherProps {
  onContextChange?: (context: {
    type: "personal" | "organization";
    organizationId?: string;
  }) => void;
}

export function OrganizationContextSwitcher({
  onContextChange,
}: OrganizationContextSwitcherProps) {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentContext, setCurrentContext] = useState<{
    type: "personal" | "organization";
    organizationId?: string;
    organizationName?: string;
  }>({ type: "personal" });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await fetch("/api/user/organizations", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setOrganizations(data.organizations);
          }
        }
      } catch (error) {
        console.error("Failed to fetch organizations:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, []);

  const handleContextSwitch = (newContext: {
    type: "personal" | "organization";
    organizationId?: string;
    organizationName?: string;
  }) => {
    setCurrentContext(newContext);
    onContextChange?.(newContext);
  };

  const getContextDisplay = () => {
    if (currentContext.type === "personal") {
      return {
        icon: <User className="h-4 w-4" />,
        label: "Personal",
        description: "Your individual certificates",
      };
    } else {
      return {
        icon: <Building2 className="h-4 w-4" />,
        label: currentContext.organizationName || "Business Account",
        description: "Business certificates",
      };
    }
  };

  const getOrganizationTypeLabel = (type: string) => {
    return type === "yacht_company" ? "Yacht Company" : "Training Provider";
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 text-xs"
          >
            Verified
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-amber-100 text-amber-800 text-xs"
          >
            Pending
          </Badge>
        );
      case "suspended":
        return (
          <Badge variant="destructive" className="text-xs">
            Suspended
          </Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Button variant="ghost" size="sm" disabled>
        <User className="h-4 w-4 mr-2" />
        Personal
        <ChevronDown className="h-4 w-4 ml-2" />
      </Button>
    );
  }

  const contextDisplay = getContextDisplay();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-9">
          {contextDisplay.icon}
          <span className="ml-2 hidden sm:inline">{contextDisplay.label}</span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Switch Context</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Personal Context */}
        <DropdownMenuItem
          onClick={() => handleContextSwitch({ type: "personal" })}
          className="flex items-center justify-between cursor-pointer"
        >
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2" />
            <div>
              <div className="font-medium">Personal</div>
              <div className="text-xs text-muted-foreground">
                Your individual certificates
              </div>
            </div>
          </div>
          {currentContext.type === "personal" && (
            <Check className="h-4 w-4 text-primary" />
          )}
        </DropdownMenuItem>

        {/* Company/Provider Contexts */}
        {organizations.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Business Accounts ({organizations.length})
            </DropdownMenuLabel>
            {organizations.map((org) => (
              <DropdownMenuItem
                key={org.id}
                onClick={() =>
                  handleContextSwitch({
                    type: "organization",
                    organizationId: org.id,
                    organizationName: org.name,
                  })
                }
                className="flex items-center justify-between cursor-pointer"
                disabled={org.status === "suspended"}
              >
                <div className="flex items-center min-w-0 flex-1">
                  <Building2 className="h-4 w-4 mr-2 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="font-medium truncate">{org.name}</div>
                    <div className="text-xs text-muted-foreground flex items-center gap-2">
                      <span>{getOrganizationTypeLabel(org.type)}</span>
                      <span>•</span>
                      <span>{getRoleDisplayName(org.membershipRole)}</span>
                      {getStatusBadge(org.status)}
                    </div>
                  </div>
                </div>
                {currentContext.type === "organization" &&
                  currentContext.organizationId === org.id && (
                    <Check className="h-4 w-4 text-primary flex-shrink-0" />
                  )}
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
