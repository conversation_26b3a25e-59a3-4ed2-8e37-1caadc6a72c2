{"crons": [{"path": "/api/cron/cleanup?type=account_deletion", "schedule": "0 2 * * *"}, {"path": "/api/cron/cleanup?type=expired_tokens", "schedule": "0 3 * * *"}, {"path": "/api/cron/cleanup?type=file_cleanup", "schedule": "0 4 * * 0"}], "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/cron/**/*.ts": {"maxDuration": 300}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}