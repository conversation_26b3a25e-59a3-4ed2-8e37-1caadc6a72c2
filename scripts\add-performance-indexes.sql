-- Performance optimization indexes for Sealog platform
-- Run this after the main migration

-- User table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_email_verified_idx"
ON "User"("emailVerified");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_role_idx"
ON "User"("role");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_subscription_idx"
ON "User"("subscriptionPlan");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_last_login_idx"
ON "User"("lastLoginAt");

-- Certificate table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificates_user_expiry_idx"
ON "Certificate"("userId", "expiryDate");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificates_user_created_idx"
ON "Certificate"("userId", "createdAt");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificates_expiry_date_idx"
ON "Certificate"("expiryDate")
WHERE "expiryDate" IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificates_favorite_idx"
ON "Certificate"("userId", "isFavorite")
WHERE "isFavorite" = true;

-- Full-text search index for certificates
CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificates_search_idx"
ON "Certificate" USING gin(
  to_tsvector('english',
    COALESCE(name, '') || ' ' ||
    COALESCE("issuingAuthority", '') || ' ' ||
    COALESCE("certificateNumber", '') || ' ' ||
    COALESCE(notes, '')
  )
);

-- Certificate files indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificate_files_certificate_idx"
ON "CertificateFile"("certificateId");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "certificate_files_created_idx"
ON "CertificateFile"("createdAt");

-- Social accounts indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "social_accounts_user_idx"
ON "SocialAccount"("userId");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "social_accounts_provider_idx"
ON "SocialAccount"("provider", "providerAccountId");

-- Email verification indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "email_verification_user_idx"
ON "EmailVerification"("userId");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "email_verification_token_idx"
ON "EmailVerification"("token");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "email_verification_expires_idx"
ON "EmailVerification"("expiresAt");

-- Password reset indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "password_reset_user_idx"
ON "PasswordReset"("userId");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "password_reset_token_idx"
ON "PasswordReset"("token");

-- Analyze tables for better query planning
ANALYZE "User";
ANALYZE "Certificate";
ANALYZE "CertificateFile";
ANALYZE "SocialAccount";
ANALYZE "EmailVerification";
ANALYZE "PasswordReset";

-- Create a view for certificate search
CREATE OR REPLACE VIEW "CertificateSearchView" AS
SELECT
  c.*,
  to_tsvector('english',
    COALESCE(c.name, '') || ' ' ||
    COALESCE(c."issuingAuthority", '') || ' ' ||
    COALESCE(c."certificateNumber", '') || ' ' ||
    COALESCE(c.notes, '')
  ) as search_vector,
  CASE
    WHEN c."expiryDate" IS NULL THEN 'no_expiry'
    WHEN c."expiryDate" < CURRENT_DATE THEN 'expired'
    WHEN c."expiryDate" < CURRENT_DATE + INTERVAL '30 days' THEN 'expiring_soon'
    ELSE 'valid'
  END as expiry_status
FROM "Certificate" c;

COMMIT;
