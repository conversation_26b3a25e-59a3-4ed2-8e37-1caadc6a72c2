/**
 * Organization permission utilities
 * Handles role-based access control within organizations
 */

export type OrganizationRole = "owner" | "admin" | "member";

export interface OrganizationPermissions {
  canManageOrganization: boolean;
  canManageMembers: boolean;
  canManageCertificates: boolean;
  canViewCertificates: boolean;
  canInviteMembers: boolean;
  canDeleteOrganization: boolean;
}

/**
 * Get permissions for a user's role in an organization
 */
export function getOrganizationPermissions(role: OrganizationRole): OrganizationPermissions {
  switch (role) {
    case "owner":
      return {
        canManageOrganization: true,
        canManageMembers: true,
        canManageCertificates: true,
        canViewCertificates: true,
        canInviteMembers: true,
        canDeleteOrganization: true,
      };
    
    case "admin":
      return {
        canManageOrganization: false, // Cannot change org settings
        canManageMembers: true,
        canManageCertificates: true,
        canViewCertificates: true,
        canInviteMembers: true,
        canDeleteOrganization: false,
      };
    
    case "member":
      return {
        canManageOrganization: false,
        canManageMembers: false,
        canManageCertificates: false, // Can only view, not manage
        canViewCertificates: true,
        canInviteMembers: false,
        canDeleteOrganization: false,
      };
    
    default:
      // No permissions for unknown roles
      return {
        canManageOrganization: false,
        canManageMembers: false,
        canManageCertificates: false,
        canViewCertificates: false,
        canInviteMembers: false,
        canDeleteOrganization: false,
      };
  }
}

/**
 * Check if a user has a specific permission in an organization
 */
export function hasOrganizationPermission(
  role: OrganizationRole,
  permission: keyof OrganizationPermissions
): boolean {
  const permissions = getOrganizationPermissions(role);
  return permissions[permission];
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: OrganizationRole): number {
  switch (role) {
    case "owner":
      return 3;
    case "admin":
      return 2;
    case "member":
      return 1;
    default:
      return 0;
  }
}

/**
 * Check if one role can manage another role
 */
export function canManageRole(managerRole: OrganizationRole, targetRole: OrganizationRole): boolean {
  return getRoleLevel(managerRole) > getRoleLevel(targetRole);
}

/**
 * Get user-friendly role display name
 */
export function getRoleDisplayName(role: OrganizationRole): string {
  switch (role) {
    case "owner":
      return "Owner";
    case "admin":
      return "Administrator";
    case "member":
      return "Member";
    default:
      return "Unknown";
  }
}

/**
 * Get role description
 */
export function getRoleDescription(role: OrganizationRole): string {
  switch (role) {
    case "owner":
      return "Full access to organization settings, members, and certificates";
    case "admin":
      return "Can manage members and certificates, but not organization settings";
    case "member":
      return "Can view organization certificates";
    default:
      return "No permissions";
  }
}
