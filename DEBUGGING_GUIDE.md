# 🔍 Debugging Guide

Essential debugging setup for Next.js 15 with <PERSON><PERSON>, Drizzle ORM, St<PERSON> Auth, and Uploadthing.

## 🚀 Quick Start

### 1. Start Debugging
```bash
# Start with Node.js inspector
pnpm run dev:debug

# Or enable enhanced logging
NEXT_PUBLIC_ENABLE_LOGGING=true NEXT_PUBLIC_LOG_LEVEL=debug pnpm dev
```

## 🛠️ VSCode Debugging

### Available Configurations
- **Next.js: debug server-side** - Debug API routes and server components
- **Next.js: debug client-side** - Debug React components in browser
- **Debug Jest Tests** - Debug test files

### How to Use
1. Open VSCode and press `Ctrl+Shift+D`
2. Select a debug configuration
3. Set breakpoints in your code
4. Press `F5` to start debugging

### Essential Scripts
```bash
pnpm run dev:debug      # Start with Node.js inspector
pnpm run test:debug     # Debug Jest tests
pnpm run debug:session  # Test authentication
pnpm run debug:db       # Test database connection
```

## 🔧 Essential Debugging

### Logging
```typescript
import { logger } from "@/lib/logger"

// Basic logging
logger.info("component", "User action", { userId, action })
logger.error("api", "Request failed", { error: error.message })

// Debug database queries
import { withDbDebug } from "@/lib/db-debug"
const result = await withDbDebug(
  () => db.select().from(users),
  { query: "SELECT users" }
)
```

### Error Boundaries
```typescript
import { ErrorBoundary } from "@/components/error-boundary"

<ErrorBoundary showDetails={true}>
  <YourComponent />
</ErrorBoundary>
```

### API Debugging
```typescript
import { withApiDebug } from "@/lib/api-debug-middleware"

export const GET = withApiDebug(async (req) => {
  // Your API logic
})
```

## 📊 Monitoring

### Debug Dashboard
Access at `/debug-dashboard` for:
- System health status
- Memory usage monitoring
- Error tracking

### Health Check
Check system status at `/api/health`

## 🚨 Common Issues

```bash
# Clear Next.js cache
rm -rf .next && pnpm dev

# Test database connection
pnpm run debug:db

# Debug authentication flow
pnpm run debug:session
```

## 📚 Resources

- [Next.js Debugging](https://nextjs.org/docs/advanced-features/debugging)
- [VSCode Node.js Debugging](https://code.visualstudio.com/docs/nodejs/nodejs-debugging)
- [React Developer Tools](https://react.dev/learn/react-developer-tools)
