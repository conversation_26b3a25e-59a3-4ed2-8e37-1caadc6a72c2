"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { handleApiError } from "@/lib/auth-utils";
import { logger } from "@/lib/logger";
import { ArrowLeft, CheckCircle, KeyRound, Loader2 } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function ForgotPasswordForm() {
  const searchParams = useSearchParams();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Populate email from URL parameter if provided
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    const endTimer = logger.authStart("forgot-password", email);
    logger.info("auth", "Forgot password attempt started", {
      email: logger.isLoggingEnabled()
        ? email.substring(0, 3) + "***"
        : undefined,
    });

    try {
      const apiEndTimer = logger.apiRequest(
        "POST",
        "/api/auth/forgot-password"
      );

      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email.toLowerCase().trim(),
        }),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/auth/forgot-password", response.status);

      const data = await response.json();

      if (data.success) {
        logger.authSuccess("forgot-password", email);
        setSuccessMessage(data.message);
        setIsSubmitted(true);
      } else {
        const errorMessage = data.error || "Failed to send reset email.";
        logger.authFailure("forgot-password", errorMessage, email);
        setError(errorMessage);
      }
    } catch (error) {
      const errorMessage = handleApiError(
        error,
        "Failed to send reset email. Please try again."
      );
      logger.error("auth", "Forgot password error", {
        error: errorMessage,
      });
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm w-full">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <KeyRound className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            {isSubmitted ? "Check Your Email" : "Forgot Password?"}
          </CardTitle>
          <CardDescription className="text-center">
            {isSubmitted
              ? "We've sent password reset instructions to your email"
              : "Enter your email address and we'll send you a link to reset your password"}
          </CardDescription>
        </CardHeader>

        {!isSubmitted ? (
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button className="w-full" type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending Reset Link...
                  </>
                ) : (
                  "Send Reset Link"
                )}
              </Button>
              <div className="text-center">
                <Link
                  href="/login"
                  className="inline-flex items-center text-sm text-primary hover:underline"
                >
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  Back to Login
                </Link>
              </div>
            </CardFooter>
          </form>
        ) : (
          <>
            <CardContent className="space-y-4">
              {successMessage && (
                <Alert
                  variant="default"
                  className="border-green-200 bg-green-50"
                >
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="text-green-800">
                    {successMessage}
                  </AlertDescription>
                </Alert>
              )}
              <div className="text-center text-sm text-muted-foreground">
                <p>
                  Didn't receive an email? Check your spam folder or{" "}
                  <button
                    type="button"
                    onClick={() => {
                      setIsSubmitted(false);
                      setSuccessMessage("");
                      setError("");
                      // Keep the email populated for retry
                    }}
                    className="text-primary hover:underline"
                  >
                    try again
                  </button>
                  .
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <div className="w-full text-center">
                <Link
                  href="/login"
                  className="inline-flex items-center text-sm text-primary hover:underline"
                >
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  Back to Login
                </Link>
              </div>
            </CardFooter>
          </>
        )}
      </Card>
    </div>
  );
}

export default function ForgotPasswordPage() {
  return (
    <Suspense
      fallback={
        <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
          <Card className="mx-auto max-w-sm w-full">
            <CardHeader className="space-y-1">
              <div className="flex justify-center mb-2">
                <KeyRound className="h-10 w-10 text-primary" />
              </div>
              <CardTitle className="text-2xl text-center">Loading...</CardTitle>
            </CardHeader>
          </Card>
        </div>
      }
    >
      <ForgotPasswordForm />
    </Suspense>
  );
}
