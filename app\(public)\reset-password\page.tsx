"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { handleApiError } from "@/lib/auth-utils";
import { logger } from "@/lib/logger";
import {
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  KeyRound,
  Loader2,
  XCircle,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

interface TokenValidationState {
  status: "loading" | "valid" | "invalid" | "expired";
  message: string;
  userEmail?: string;
}

// Loading skeleton component
const LoadingSkeleton = () => (
  <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
    <Card className="mx-auto max-w-sm w-full">
      <CardHeader className="space-y-1">
        <div className="flex justify-center mb-2">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
        <Skeleton className="h-6 w-3/4 mx-auto" />
        <Skeleton className="h-4 w-full" />
      </CardHeader>
      <CardContent className="space-y-4">
        <Skeleton className="h-16 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </CardContent>
    </Card>
  </div>
);

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [tokenState, setTokenState] = useState<TokenValidationState>({
    status: "loading",
    message: "Validating reset token...",
  });

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);

  // Validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setTokenState({
          status: "invalid",
          message:
            "No reset token provided. Please request a new password reset.",
        });
        return;
      }

      try {
        const response = await fetch(`/api/auth/reset-password?token=${token}`);
        const data = await response.json();

        if (data.success) {
          setTokenState({
            status: "valid",
            message: "Token validated successfully",
            userEmail: data.userEmail,
          });
        } else {
          if (data.error.includes("expired")) {
            setTokenState({
              status: "expired",
              message: "This reset link has expired. Please request a new one.",
            });
          } else {
            setTokenState({
              status: "invalid",
              message: data.error || "Invalid reset token",
            });
          }
        }
      } catch (error) {
        console.error("Token validation error:", error);
        setTokenState({
          status: "invalid",
          message: "Failed to validate reset token. Please try again.",
        });
      }
    };

    validateToken();
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Client-side validation
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      setIsLoading(false);
      return;
    }

    const endTimer = logger.authStart(
      "reset-password",
      tokenState.userEmail || "unknown"
    );
    logger.info("auth", "Password reset attempt started");

    try {
      const apiEndTimer = logger.apiRequest("POST", "/api/auth/reset-password");

      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password,
          confirmPassword,
        }),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/auth/reset-password", response.status);

      const data = await response.json();

      if (data.success) {
        logger.authSuccess("reset-password", tokenState.userEmail || "unknown");
        setIsSuccess(true);

        // Redirect to login after a short delay
        setTimeout(() => {
          router.push("/login");
        }, 2000);
      } else {
        const errorMessage = data.error || "Failed to reset password.";
        logger.authFailure(
          "reset-password",
          errorMessage,
          tokenState.userEmail || "unknown"
        );
        setError(errorMessage);
      }
    } catch (error) {
      const errorMessage = handleApiError(
        error,
        "Failed to reset password. Please try again."
      );
      logger.error("auth", "Password reset error", {
        error: errorMessage,
      });
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  // Show loading skeleton while validating token
  if (tokenState.status === "loading") {
    return <LoadingSkeleton />;
  }

  // Show error states for invalid/expired tokens
  if (tokenState.status === "invalid" || tokenState.status === "expired") {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
        <Card className="mx-auto max-w-sm w-full">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-2">
              {tokenState.status === "expired" ? (
                <AlertTriangle className="h-10 w-10 text-amber-500" />
              ) : (
                <XCircle className="h-10 w-10 text-red-500" />
              )}
            </div>
            <CardTitle className="text-2xl text-center">
              {tokenState.status === "expired"
                ? "Link Expired"
                : "Invalid Link"}
            </CardTitle>
            <CardDescription className="text-center">
              {tokenState.message}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>{tokenState.message}</AlertDescription>
            </Alert>
            <div className="text-center">
              <Button
                onClick={() => router.push("/forgot-password")}
                className="w-full"
              >
                Request New Reset Link
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show success state
  if (isSuccess) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
        <Card className="mx-auto max-w-sm w-full">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-2">
              <CheckCircle className="h-10 w-10 text-green-500" />
            </div>
            <CardTitle className="text-2xl text-center">
              Password Reset Successfully
            </CardTitle>
            <CardDescription className="text-center">
              Your password has been updated. Redirecting to login...
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="default" className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-green-800">
                You can now log in with your new password.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show password reset form
  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm w-full">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <KeyRound className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            Set New Password
          </CardTitle>
          <CardDescription className="text-center">
            {tokenState.userEmail && (
              <span className="block text-sm text-gray-600 mt-2">
                {tokenState.userEmail}
              </span>
            )}
            Enter your new password below
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="password">New Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  minLength={8}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Must be at least 8 characters long
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  disabled={isLoading}
                  minLength={8}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Password...
                </>
              ) : (
                "Update Password"
              )}
            </Button>
          </CardContent>
        </form>
      </Card>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<LoadingSkeleton />}>
      <ResetPasswordForm />
    </Suspense>
  );
}
