import { NextRequest, NextResponse } from "next/server";
import { getPerformanceMetrics, getPerformanceHealth } from "@/lib/performance-monitoring";
import { handleApiError, AuthenticationError, AuthorizationError } from "@/lib/api-error-handler";
import { getServerSession } from "next-auth";
import { logger } from "@/lib/logger";

/**
 * Get performance metrics (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      throw new AuthenticationError();
    }
    
    // Check if user is admin (for now, just check if user exists - implement proper RBAC later)
    if (!session.user) {
      throw new AuthorizationError("Admin access required");
    }
    
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "metrics";
    
    logger.info("admin", "Performance data requested", {
      userId: session.user.id,
      type,
    });
    
    let data;
    
    switch (type) {
      case "health":
        data = getPerformanceHealth();
        break;
        
      case "metrics":
      default:
        data = getPerformanceMetrics();
        break;
    }
    
    return NextResponse.json({
      success: true,
      type,
      timestamp: new Date().toISOString(),
      data,
    });
    
  } catch (error) {
    return handleApiError(error, "admin-performance");
  }
}
