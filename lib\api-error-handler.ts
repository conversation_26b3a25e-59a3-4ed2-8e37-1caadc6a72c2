import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";

export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(400, message, 'VALIDATION_ERROR', details);
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(401, message, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(403, message, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Resource not found') {
    super(404, message, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends ApiError {
  constructor(message: string) {
    super(409, message, 'CONFLICT_ERROR');
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = 'Too many requests') {
    super(429, message, 'RATE_LIMIT_ERROR');
  }
}

export function handleApiError(error: unknown, context?: string): NextResponse {
  // Log the error for debugging
  const errorId = generateErrorId();

  if (error instanceof ApiError) {
    logger.warn('api', `API Error [${errorId}]: ${error.message}`, {
      statusCode: error.statusCode,
      code: error.code,
      details: error.details,
      context,
      stack: error.stack
    });

    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        errorId,
        ...(process.env.NODE_ENV === 'development' && { details: error.details })
      },
      { status: error.statusCode }
    );
  }

  // Handle validation errors from Zod or similar
  if (error && typeof error === 'object' && 'issues' in error) {
    logger.warn('api', `Validation Error [${errorId}]`, { error, context });
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errorId,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 400 }
    );
  }

  // Handle unexpected errors
  logger.error('api', `Unexpected API Error [${errorId}]`, {
    error: error instanceof Error ? {
      message: error.message,
      stack: error.stack,
      name: error.name
    } : error,
    context
  });

  return NextResponse.json(
    {
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      errorId
    },
    { status: 500 }
  );
}

function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Async wrapper for API route handlers
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>,
  context?: string
) {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleApiError(error, context);
    }
  };
}
