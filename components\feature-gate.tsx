/**
 * Feature Gate Component
 * 
 * This component demonstrates how to use the feature access control system
 * to gate features based on organization verification and subscription status.
 */

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  FeatureCategory, 
  Organization, 
  hasFeatureAccess, 
  getFeatureAccessInfo,
  getFeatureDescription 
} from "@/lib/feature-access-control"
import { Shield, Lock, CreditCard, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

interface FeatureGateProps {
  organization: Organization
  feature: FeatureCategory
  children: React.ReactNode
  fallback?: React.ReactNode
  showAccessInfo?: boolean
}

/**
 * Feature Gate Component - Conditionally renders content based on feature access
 */
export function FeatureGate({ 
  organization, 
  feature, 
  children, 
  fallback,
  showAccessInfo = false 
}: FeatureGateProps) {
  const hasAccess = hasFeatureAccess(organization, feature)
  
  if (hasAccess) {
    return <>{children}</>
  }
  
  if (fallback) {
    return <>{fallback}</>
  }
  
  if (showAccessInfo) {
    return <FeatureAccessCard organization={organization} feature={feature} />
  }
  
  return null
}

/**
 * Feature Access Card - Shows detailed access information and upgrade prompts
 */
export function FeatureAccessCard({ 
  organization, 
  feature 
}: { 
  organization: Organization
  feature: FeatureCategory 
}) {
  const accessInfo = getFeatureAccessInfo(organization, feature)
  const description = getFeatureDescription(feature)
  
  const getStatusIcon = () => {
    if (accessInfo.hasAccess) {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    }
    if (organization.status === "suspended") {
      return <XCircle className="h-5 w-5 text-red-500" />
    }
    return <AlertTriangle className="h-5 w-5 text-amber-500" />
  }
  
  const getStatusColor = () => {
    if (accessInfo.hasAccess) return "border-green-200 bg-green-50"
    if (organization.status === "suspended") return "border-red-200 bg-red-50"
    return "border-amber-200 bg-amber-50"
  }
  
  return (
    <Card className={`${getStatusColor()}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">
              {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </CardTitle>
          </div>
          <div className="flex space-x-2">
            {accessInfo.verificationRequired && (
              <Badge variant={accessInfo.verificationMet ? "default" : "secondary"}>
                <Shield className="h-3 w-3 mr-1" />
                {accessInfo.verificationMet ? "Verified" : "Verification Required"}
              </Badge>
            )}
            {accessInfo.subscriptionRequired && (
              <Badge variant={accessInfo.subscriptionMet ? "default" : "secondary"}>
                <CreditCard className="h-3 w-3 mr-1" />
                {accessInfo.minimumSubscription?.toUpperCase()}
              </Badge>
            )}
          </div>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <Alert>
          <Lock className="h-4 w-4" />
          <AlertDescription>{accessInfo.message}</AlertDescription>
        </Alert>
        
        {!accessInfo.hasAccess && (
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              <strong>Requirements:</strong>
            </div>
            
            <div className="space-y-2">
              {accessInfo.verificationRequired && (
                <div className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span className="text-sm">Organization Verification</span>
                  </div>
                  <Badge variant={accessInfo.verificationMet ? "default" : "secondary"}>
                    {accessInfo.verificationMet ? "✓ Complete" : "Required"}
                  </Badge>
                </div>
              )}
              
              {accessInfo.subscriptionRequired && (
                <div className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4" />
                    <span className="text-sm">
                      {accessInfo.minimumSubscription?.toUpperCase()} Subscription
                    </span>
                  </div>
                  <Badge variant={accessInfo.subscriptionMet ? "default" : "secondary"}>
                    {accessInfo.subscriptionMet ? "✓ Active" : "Required"}
                  </Badge>
                </div>
              )}
            </div>
            
            {accessInfo.actionRequired && (
              <div className="pt-2">
                <Button className="w-full">
                  {accessInfo.actionRequired}
                </Button>
              </div>
            )}
          </div>
        )}
        
        {accessInfo.hasAccess && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Feature Available</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Organization Status Badge - Shows current verification and subscription status
 */
export function OrganizationStatusBadge({ organization }: { organization: Organization }) {
  const getStatusColor = () => {
    switch (organization.status) {
      case "verified": return "bg-green-100 text-green-800"
      case "pending": return "bg-amber-100 text-amber-800"
      case "suspended": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }
  
  const getSubscriptionColor = () => {
    switch (organization.subscriptionPlan) {
      case "enterprise": return "bg-purple-100 text-purple-800"
      case "premium": return "bg-blue-100 text-blue-800"
      case "basic": return "bg-green-100 text-green-800"
      case "free": return "bg-gray-100 text-gray-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }
  
  return (
    <div className="flex space-x-2">
      <Badge className={getStatusColor()}>
        <Shield className="h-3 w-3 mr-1" />
        {organization.status.charAt(0).toUpperCase() + organization.status.slice(1)}
      </Badge>
      {organization.subscriptionPlan && (
        <Badge className={getSubscriptionColor()}>
          <CreditCard className="h-3 w-3 mr-1" />
          {organization.subscriptionPlan.charAt(0).toUpperCase() + organization.subscriptionPlan.slice(1)}
        </Badge>
      )}
    </div>
  )
}

/**
 * Feature List Component - Shows all features and their access status
 */
export function FeatureList({ organization }: { organization: Organization }) {
  const features = Object.values(FeatureCategory)
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Feature Access Overview</h3>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature) => {
          const hasAccess = hasFeatureAccess(organization, feature)
          const accessInfo = getFeatureAccessInfo(organization, feature)
          
          return (
            <Card key={feature} className={hasAccess ? "border-green-200" : "border-gray-200"}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">
                    {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </CardTitle>
                  {hasAccess ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <Lock className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-xs text-muted-foreground">
                  {getFeatureDescription(feature)}
                </div>
                {!hasAccess && (
                  <div className="mt-2 flex space-x-1">
                    {accessInfo.verificationRequired && (
                      <Badge variant="outline" className="text-xs">
                        Verification
                      </Badge>
                    )}
                    {accessInfo.subscriptionRequired && (
                      <Badge variant="outline" className="text-xs">
                        {accessInfo.minimumSubscription}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
