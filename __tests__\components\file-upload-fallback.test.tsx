import { FileUploadFallback } from "@/components/file-upload-fallback";
import { fireEvent, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
  cleanup,
  createMockDragEvent,
  createMockFile,
  createMockKeyboardEvent,
  render,
} from "../utils/test-utils";

describe("FileUploadFallback", () => {
  const mockOnFileSelect = jest.fn();
  const mockOnFileRemove = jest.fn();

  beforeEach(() => {
    cleanup();
  });

  const defaultProps = {
    onFileSelect: mockOnFileSelect,
    onFileRemove: mockOnFileRemove,
  };

  describe("Initial Render", () => {
    it("renders upload area when no file is uploaded", () => {
      render(<FileUploadFallback {...defaultProps} />);

      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByText("Click to upload")).toBeInTheDocument();
      expect(
        screen.getByText("PDF, JPG or PNG (max. 10MB)")
      ).toBeInTheDocument();
    });

    it("renders uploaded file when fileName is provided", () => {
      render(
        <FileUploadFallback
          {...defaultProps}
          uploadedFileName="test-document.pdf"
        />
      );

      expect(screen.getByText("test-document.pdf")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument(); // Remove button
      expect(screen.queryByText("Click to upload")).not.toBeInTheDocument();
    });

    it("applies custom props correctly", () => {
      render(
        <FileUploadFallback
          {...defaultProps}
          id="custom-upload"
          aria-label="Custom upload area"
          maxSize={5}
        />
      );

      expect(screen.getByRole("button")).toHaveAttribute(
        "aria-label",
        "Custom upload area"
      );
      expect(
        screen.getByText("PDF, JPG or PNG (max. 5MB)")
      ).toBeInTheDocument();
    });
  });

  describe("File Selection via Click", () => {
    it("opens file dialog when upload area is clicked", async () => {
      const user = userEvent.setup();
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload file");

      // Mock the click method
      const clickSpy = jest.spyOn(fileInput, "click").mockImplementation();

      await user.click(uploadArea);

      expect(clickSpy).toHaveBeenCalled();
      clickSpy.mockRestore();
    });

    it("handles file selection through input", async () => {
      const user = userEvent.setup();
      const mockFile = createMockFile("test.pdf", "application/pdf");

      render(<FileUploadFallback {...defaultProps} />);

      const fileInput = screen.getByLabelText("Upload file");

      await user.upload(fileInput, mockFile);

      expect(mockOnFileSelect).toHaveBeenCalledWith(mockFile);
    });

    it("validates file type on selection", async () => {
      const user = userEvent.setup();
      const invalidFile = createMockFile("test.txt", "text/plain");

      render(<FileUploadFallback {...defaultProps} />);

      const fileInput = screen.getByLabelText("Upload file");

      await user.upload(fileInput, invalidFile);

      expect(mockOnFileSelect).not.toHaveBeenCalled();
      expect(screen.getByText(/File type not supported/)).toBeInTheDocument();
    });

    it("validates file size on selection", async () => {
      const user = userEvent.setup();
      const largeFile = createMockFile(
        "large.pdf",
        "application/pdf",
        15 * 1024 * 1024
      ); // 15MB

      render(<FileUploadFallback {...defaultProps} />);

      const fileInput = screen.getByLabelText("Upload file");

      await user.upload(fileInput, largeFile);

      expect(mockOnFileSelect).not.toHaveBeenCalled();
      expect(
        screen.getByText(/File size must be less than 10MB/)
      ).toBeInTheDocument();
    });
  });

  describe("Keyboard Navigation", () => {
    it("opens file dialog on Enter key", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload file");

      const clickSpy = jest.spyOn(fileInput, "click").mockImplementation();

      fireEvent.keyDown(uploadArea, createMockKeyboardEvent("Enter"));

      expect(clickSpy).toHaveBeenCalled();
      clickSpy.mockRestore();
    });

    it("opens file dialog on Space key", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload file");

      const clickSpy = jest.spyOn(fileInput, "click").mockImplementation();

      fireEvent.keyDown(uploadArea, createMockKeyboardEvent(" "));

      expect(clickSpy).toHaveBeenCalled();
      clickSpy.mockRestore();
    });

    it("ignores other keys", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload file");

      const clickSpy = jest.spyOn(fileInput, "click").mockImplementation();

      fireEvent.keyDown(uploadArea, createMockKeyboardEvent("Tab"));

      expect(clickSpy).not.toHaveBeenCalled();
      clickSpy.mockRestore();
    });
  });

  describe("Drag and Drop", () => {
    it("handles drag over events", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      fireEvent.dragOver(uploadArea, createMockDragEvent());

      expect(uploadArea).toHaveClass("border-primary");
    });

    it("handles drag leave events", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      // First drag over to set state
      fireEvent.dragOver(uploadArea, createMockDragEvent());
      expect(uploadArea).toHaveClass("border-primary");

      // Then drag leave
      fireEvent.dragLeave(uploadArea, createMockDragEvent());

      expect(uploadArea).not.toHaveClass("border-primary");
    });

    it("handles file drop with valid file", () => {
      const mockFile = createMockFile("test.pdf", "application/pdf");

      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      fireEvent.drop(uploadArea, createMockDragEvent([mockFile]));

      expect(mockOnFileSelect).toHaveBeenCalledWith(mockFile);
    });

    it("validates dropped files", () => {
      const invalidFile = createMockFile("test.txt", "text/plain");

      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      fireEvent.drop(uploadArea, createMockDragEvent([invalidFile]));

      expect(mockOnFileSelect).not.toHaveBeenCalled();
      expect(screen.getByText(/File type not supported/)).toBeInTheDocument();
    });

    it("handles multiple files by selecting the first one", () => {
      const file1 = createMockFile("test1.pdf", "application/pdf");
      const file2 = createMockFile("test2.pdf", "application/pdf");

      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      fireEvent.drop(uploadArea, createMockDragEvent([file1, file2]));

      expect(mockOnFileSelect).toHaveBeenCalledWith(file1);
      expect(mockOnFileSelect).toHaveBeenCalledTimes(1);
    });
  });

  describe("File Removal", () => {
    it("calls onFileRemove when remove button is clicked", async () => {
      const user = userEvent.setup();

      render(
        <FileUploadFallback
          {...defaultProps}
          uploadedFileName="test-document.pdf"
        />
      );

      const removeButton = screen.getByRole("button");

      await user.click(removeButton);

      expect(mockOnFileRemove).toHaveBeenCalled();
    });
  });

  describe("Disabled State", () => {
    it("disables interactions when disabled prop is true", () => {
      render(<FileUploadFallback {...defaultProps} disabled />);

      const uploadArea = screen.getByRole("button");

      expect(uploadArea).toHaveAttribute("aria-disabled", "true");
      expect(uploadArea).toHaveAttribute("tabIndex", "-1");
      expect(uploadArea).toHaveClass("cursor-not-allowed opacity-50");
    });

    it("prevents file selection when disabled", () => {
      render(<FileUploadFallback {...defaultProps} disabled />);

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload file");

      const clickSpy = jest.spyOn(fileInput, "click").mockImplementation();

      fireEvent.click(uploadArea);

      expect(clickSpy).not.toHaveBeenCalled();
      clickSpy.mockRestore();
    });

    it("prevents drag and drop when disabled", () => {
      const mockFile = createMockFile("test.pdf", "application/pdf");

      render(<FileUploadFallback {...defaultProps} disabled />);

      const uploadArea = screen.getByRole("button");

      fireEvent.drop(uploadArea, createMockDragEvent([mockFile]));

      expect(mockOnFileSelect).not.toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("has proper ARIA attributes", () => {
      render(
        <FileUploadFallback
          {...defaultProps}
          id="test-upload"
          aria-label="Upload certificate document"
        />
      );

      const uploadArea = screen.getByRole("button");
      const fileInput = screen.getByLabelText("Upload certificate document");

      expect(uploadArea).toHaveAttribute(
        "aria-label",
        "Upload certificate document"
      );
      expect(uploadArea).toHaveAttribute("aria-describedby");
      expect(fileInput).toHaveAttribute("id", "test-upload");
    });

    it("associates error messages with input", () => {
      const invalidFile = createMockFile("test.txt", "text/plain");

      render(<FileUploadFallback {...defaultProps} id="test-upload" />);

      const uploadArea = screen.getByRole("button");

      fireEvent.drop(uploadArea, createMockDragEvent([invalidFile]));

      const errorAlert = screen.getByRole("alert");
      expect(errorAlert).toHaveAttribute("id", "test-upload-error");
    });

    it("is keyboard accessible", () => {
      render(<FileUploadFallback {...defaultProps} />);

      const uploadArea = screen.getByRole("button");

      expect(uploadArea).toHaveAttribute("tabIndex", "0");
      expect(uploadArea).toHaveAttribute("role", "button");
    });
  });

  describe("File Type Validation", () => {
    const testCases = [
      { file: createMockFile("test.pdf", "application/pdf"), shouldPass: true },
      { file: createMockFile("test.jpg", "image/jpeg"), shouldPass: true },
      { file: createMockFile("test.jpeg", "image/jpeg"), shouldPass: true },
      { file: createMockFile("test.png", "image/png"), shouldPass: true },
      { file: createMockFile("test.txt", "text/plain"), shouldPass: false },
      {
        file: createMockFile("test.doc", "application/msword"),
        shouldPass: false,
      },
    ];

    testCases.forEach(({ file, shouldPass }) => {
      it(`${shouldPass ? "accepts" : "rejects"} ${file.name} (${
        file.type
      })`, () => {
        render(<FileUploadFallback {...defaultProps} />);

        const uploadArea = screen.getByRole("button");

        fireEvent.drop(uploadArea, createMockDragEvent([file]));

        if (shouldPass) {
          expect(mockOnFileSelect).toHaveBeenCalledWith(file);
          expect(screen.queryByRole("alert")).not.toBeInTheDocument();
        } else {
          expect(mockOnFileSelect).not.toHaveBeenCalled();
          expect(screen.getByRole("alert")).toBeInTheDocument();
        }
      });
    });
  });
});
