import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';

async function globalSetup(config: FullConfig) {
  console.log('🧹 Cleaning up old test artifacts...');

  // Clean up old test results and reports
  const cleanupPaths = [
    'test-results',
    'playwright-report',
    'test-results-*',
  ];

  for (const cleanupPath of cleanupPaths) {
    try {
      if (fs.existsSync(cleanupPath)) {
        fs.rmSync(cleanupPath, { recursive: true, force: true });
        console.log(`✅ Cleaned up ${cleanupPath}`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not clean up ${cleanupPath}:`, error);
    }
  }

  // Create fresh directories
  fs.mkdirSync('test-results', { recursive: true });

  console.log('🔧 Setting up test environment...');

  // Wait for server to be ready
  const browser = await chromium.launch();
  const page = await browser.newPage();

  let retries = 0;
  const maxRetries = 30; // 30 seconds

  while (retries < maxRetries) {
    try {
      const response = await page.goto('http://localhost:3000/api/health');
      if (response?.status() === 200) {
        console.log('✅ Server is ready');
        break;
      }
    } catch (error) {
      retries++;
      if (retries === maxRetries) {
        throw new Error(`Server not ready after ${maxRetries} seconds`);
      }
      await page.waitForTimeout(1000);
    }
  }

  // Setup test user authentication
  console.log('👤 Setting up test user authentication...');

  try {
    // Start from landing page (correct flow for unauthenticated users)
    await page.goto('http://localhost:3000/');
    await page.waitForLoadState('networkidle');

    // Navigate to login page (as unauthenticated users would)
    await page.click('text=Login');
    await page.waitForURL('**/login');

    // Fill in test user credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'AutoTest123!');

    // Submit login form
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard (indicates successful login)
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Successfully logged in and redirected to dashboard');

    // Verify we're actually logged in by checking for user-specific content
    await page.waitForSelector('text=Auto Test User', { timeout: 5000 });

    // Save authentication state
    await page.context().storageState({ path: 'e2e/auth-state.json' });
    console.log('✅ Test user authentication saved');

  } catch (error) {
    console.warn('⚠️ Could not setup test user authentication:', error);
    console.log('ℹ️ Tests will run without pre-authenticated state');

    // Create a minimal auth state file to prevent file not found errors
    const fs = require('fs');
    const minimalAuthState = {
      cookies: [],
      origins: []
    };
    fs.writeFileSync('e2e/auth-state.json', JSON.stringify(minimalAuthState, null, 2));
    console.log('ℹ️ Created minimal auth state file');
  }

  await browser.close();

  console.log('🚀 Test environment setup complete');
}

export default globalSetup;
