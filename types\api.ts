/**
 * Centralized API Response Types
 *
 * This file contains all standardized API response types used across the application.
 * These types ensure consistency in API responses and improve type safety.
 */

// Base API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  errorId?: string;
  message?: string;
}

// Paginated response structure
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication responses
export interface LoginResponse extends ApiResponse {
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    subscriptionPlan: string;
    emailVerified: boolean;
  };
  requiresVerification?: boolean;
  userEmail?: string;
}

export interface RegisterResponse extends ApiResponse {
  user?: {
    id: string;
    name: string;
    email: string;
  };
  requiresVerification?: boolean;
}

export interface VerificationResponse extends ApiResponse {
  userId?: string;
  shouldRedirect?: boolean;
  redirectUrl?: string;
  alreadyVerified?: boolean;
}

// Certificate responses
export interface CertificateResponse extends ApiResponse {
  id?: string;
}

export interface CertificateListResponse extends ApiResponse {
  data: Certificate[];
}

export interface CertificateStatsResponse extends ApiResponse {
  data: {
    total: number;
    favorites: number;
    expired: number;
    expiringSoon: number;
  };
}

// User management responses
export interface UserResponse extends ApiResponse {
  user?: User;
}

export interface UserListResponse extends PaginatedResponse<User> { }

export interface UserStatsResponse extends ApiResponse {
  data: {
    total: number;
    active: number;
    deleted: number;
    verified: number;
    recent: number;
  };
}

// Organization responses
export interface OrganizationResponse extends ApiResponse {
  organization?: Organization;
}

export interface OrganizationListResponse extends PaginatedResponse<Organization> { }

// File upload responses
export interface FileUploadResponse extends ApiResponse {
  files?: UploadedFile[];
}

export interface FileDownloadResponse extends ApiResponse {
  downloadUrl?: string;
  fileName?: string;
}

// Admin responses
export interface AdminStatsResponse extends ApiResponse {
  data: {
    users: UserStatsResponse['data'];
    certificates: CertificateStatsResponse['data'];
    organizations: {
      total: number;
      verified: number;
      pending: number;
      suspended: number;
    };
    system: {
      uptime: number;
      version: string;
      environment: string;
    };
  };
}

// Error response types
export interface ValidationErrorResponse extends ApiResponse {
  details?: {
    field: string;
    message: string;
  }[];
}

export interface RateLimitErrorResponse extends ApiResponse {
  retryAfter?: number;
  remainingTime?: number;
}

// Type definitions for entities (these should match your database schema)
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  subscriptionPlan: string;
  emailVerified: boolean;
  tenantId?: string;
  tenantRole?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  deletedAt?: string;
}

export interface Certificate {
  id: string;
  name: string;
  issuingAuthority: string;
  certificateNumber: string;
  dateIssued: string;
  expiryDate?: string;
  notes?: string;
  isFavorite: boolean;
  userId: string;
  organizationId?: string;
  scope: string;
  // Tags for flexible organization
  tags?: string;
  createdAt: string;
  updatedAt: string;
  // Legacy fields for backward compatibility
  documentUrl?: string;
  documentName?: string;
  documentSize?: string;
  documentType?: string;
}

export interface Organization {
  id: string;
  name: string;
  type: 'yacht_company' | 'cert_provider';
  status: 'pending' | 'verified' | 'suspended';
  contactEmail: string;
  description?: string;
  website?: string;
  verifiedAt?: string;
  verifiedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UploadedFile {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadthingKey?: string;
  uploadOrder: number;
  createdAt: string;
}

// Request body types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  role?: string;
}

export interface CertificateCreateRequest {
  name: string;
  issuingAuthority: string;
  certificateNumber: string;
  dateIssued: string;
  expiryDate?: string;
  notes?: string;
  isFavorite?: boolean;
  // Tags for flexible organization
  tags?: string;
  files?: UploadedFile[];
  // Legacy fields
  documentUrl?: string;
  documentName?: string;
  documentSize?: string;
  documentType?: string;
}

export interface CertificateUpdateRequest extends Partial<CertificateCreateRequest> {
  id: string;
}

// Query parameter types
export interface CertificateFilters {
  search?: string;
  filter?: 'all' | 'favorites' | 'expiring-soon' | 'expired';
  tags?: string[];
  sortBy?: 'name' | 'dateIssued' | 'expiryDate';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface UserFilters {
  search?: string;
  role?: string;
  verified?: boolean;
  limit?: number;
  offset?: number;
}

export interface OrganizationFilters {
  search?: string;
  type?: 'yacht_company' | 'cert_provider';
  status?: 'pending' | 'verified' | 'suspended';
  limit?: number;
  offset?: number;
}
