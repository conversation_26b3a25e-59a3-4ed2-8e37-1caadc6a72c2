#!/usr/bin/env node

/**
 * Database migration script to add role and subscription plan columns
 * and migrate existing users to INDIVIDUAL_USER role
 */

const { neon } = require("@neondatabase/serverless");

async function migrateUserRoles() {
  console.log("🚀 Starting user role migration...");
  
  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  const sql = neon(process.env.DATABASE_URL);

  try {
    console.log("1️⃣ Adding role and subscription plan columns...");
    
    // Add columns if they don't exist
    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "role" TEXT DEFAULT 'individual_user'
    `;
    
    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "subscriptionPlan" TEXT DEFAULT 'individual_free'
    `;
    
    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "tenantId" TEXT
    `;
    
    await sql`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "tenantRole" TEXT
    `;

    console.log("✅ Columns added successfully");

    console.log("2️⃣ Updating existing users...");
    
    // Update existing users without roles to have INDIVIDUAL_USER role
    const usersUpdated = await sql`
      UPDATE "User" 
      SET "role" = 'individual_user' 
      WHERE "role" IS NULL
    `;
    
    console.log(`✅ Updated ${usersUpdated.length} users with INDIVIDUAL_USER role`);
    
    // Update existing users without subscription plans
    const plansUpdated = await sql`
      UPDATE "User" 
      SET "subscriptionPlan" = 'individual_free' 
      WHERE "subscriptionPlan" IS NULL AND "role" = 'individual_user'
    `;
    
    console.log(`✅ Updated ${plansUpdated.length} users with individual_free subscription plan`);

    console.log("3️⃣ Migrating legacy subscription plans...");
    
    // Migrate legacy subscription plans
    const basicUpdated = await sql`
      UPDATE "User" 
      SET "subscriptionPlan" = 'individual_basic' 
      WHERE "subscriptionPlan" = 'basic' AND "role" = 'individual_user'
    `;
    
    const premiumUpdated = await sql`
      UPDATE "User" 
      SET "subscriptionPlan" = 'individual_premium' 
      WHERE "subscriptionPlan" = 'premium' AND "role" = 'individual_user'
    `;
    
    const enterpriseUpdated = await sql`
      UPDATE "User" 
      SET "subscriptionPlan" = 'individual_professional' 
      WHERE "subscriptionPlan" = 'enterprise' AND "role" = 'individual_user'
    `;

    console.log(`✅ Migrated legacy plans: ${basicUpdated.length} basic, ${premiumUpdated.length} premium, ${enterpriseUpdated.length} enterprise`);

    console.log("4️⃣ Verifying migration...");
    
    // Verify the migration
    const userStats = await sql`
      SELECT 
        "role",
        "subscriptionPlan",
        COUNT(*) as count
      FROM "User" 
      GROUP BY "role", "subscriptionPlan"
      ORDER BY "role", "subscriptionPlan"
    `;

    console.log("📊 User distribution after migration:");
    userStats.forEach(stat => {
      console.log(`   ${stat.role} (${stat.subscriptionPlan}): ${stat.count} users`);
    });

    console.log("🎉 User role migration completed successfully!");

  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  migrateUserRoles()
    .then(() => {
      console.log("✅ Migration script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Migration script failed:", error);
      process.exit(1);
    });
}

module.exports = { migrateUserRoles };
