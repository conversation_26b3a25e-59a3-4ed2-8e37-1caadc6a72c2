/**
 * Certificate Management Functions
 *
 * This module handles all certificate-related database operations including:
 * - Certificate CRUD operations
 * - Certificate file management
 * - Certificate queries (expiring, recent, favorites, etc.)
 * - Multi-file certificate support
 */

import { neon } from "@neondatabase/serverless"
import { and, asc, count, desc, eq, gte, ilike, lte, or } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import {
  certificateFiles,
  certificates
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// BASIC CERTIFICATE OPERATIONS
// ============================================================================

/**
 * Get all certificates for a user
 */
export async function getCertificatesByUserId(userId: string) {
  try {
    // Explicitly select only existing columns until migration is run
    return await db.select({
      id: certificates.id,
      name: certificates.name,
      issuingAuthority: certificates.issuingAuthority,
      certificateNumber: certificates.certificateNumber,
      dateIssued: certificates.dateIssued,
      expiryDate: certificates.expiryDate,
      documentUrl: certificates.documentUrl,
      documentName: certificates.documentName,
      documentSize: certificates.documentSize,
      documentType: certificates.documentType,
      notes: certificates.notes,
      isFavorite: certificates.isFavorite,
      createdAt: certificates.createdAt,
      updatedAt: certificates.updatedAt,
      userId: certificates.userId,
      organizationId: certificates.organizationId,
      scope: certificates.scope,
    }).from(certificates).where(eq(certificates.userId, userId))
  } catch (error) {
    console.error("Error getting certificates by user ID:", error)
    return []
  }
}

/**
 * Get certificate by ID (with user ownership check)
 */
export async function getCertificateById(id: string, userId: string) {
  try {
    // Explicitly select only existing columns until migration is run
    const result = await db
      .select({
        id: certificates.id,
        name: certificates.name,
        issuingAuthority: certificates.issuingAuthority,
        certificateNumber: certificates.certificateNumber,
        dateIssued: certificates.dateIssued,
        expiryDate: certificates.expiryDate,
        documentUrl: certificates.documentUrl,
        documentName: certificates.documentName,
        documentSize: certificates.documentSize,
        documentType: certificates.documentType,
        notes: certificates.notes,
        isFavorite: certificates.isFavorite,
        createdAt: certificates.createdAt,
        updatedAt: certificates.updatedAt,
        userId: certificates.userId,
        organizationId: certificates.organizationId,
        scope: certificates.scope,
      })
      .from(certificates)
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    return result[0] || null
  } catch (error) {
    console.error("Error getting certificate by ID:", error)
    return null
  }
}

/**
 * Create a new certificate
 */
export async function createCertificate(certificateData: {
  id: string
  name: string
  issuingAuthority: string
  certificateNumber: string
  dateIssued: Date
  expiryDate?: Date | null
  documentUrl?: string | null
  documentName?: string | null
  documentSize?: string | null
  documentType?: string | null
  notes?: string | null
  isFavorite?: boolean
  tags?: string | null
  userId: string
}) {
  try {
    const now = new Date()
    await db.insert(certificates).values({
      ...certificateData,
      isFavorite: certificateData.isFavorite || false,
      tags: certificateData.tags || null,
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating certificate:", error)
    throw error
  }
}

/**
 * Update certificate information
 */
export async function updateCertificate(
  id: string,
  userId: string,
  certificateData: Partial<{
    name: string
    issuingAuthority: string
    certificateNumber: string
    dateIssued: Date
    expiryDate: Date | null
    documentUrl: string | null
    documentName: string | null
    documentSize: string | null
    documentType: string | null
    notes: string | null
    isFavorite: boolean
    tags: string | null
  }>,
) {
  try {
    await db
      .update(certificates)
      .set({
        ...certificateData,
        updatedAt: new Date(),
      })
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    return { success: true }
  } catch (error) {
    console.error("Error updating certificate:", error)
    throw error
  }
}

/**
 * Delete certificate and its files
 */
export async function deleteCertificate(id: string, userId: string) {
  try {
    // First delete associated files (cascade should handle this, but being explicit)
    await db.delete(certificateFiles).where(eq(certificateFiles.certificateId, id))

    // Then delete the certificate
    await db
      .delete(certificates)
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))

    return { success: true }
  } catch (error) {
    console.error("Error deleting certificate:", error)
    throw error
  }
}

// ============================================================================
// CERTIFICATE FILE MANAGEMENT
// ============================================================================

/**
 * Get certificate files for a specific certificate
 */
export async function getCertificateFiles(certificateId: string, userId: string) {
  try {
    // First verify the certificate belongs to the user
    const certificate = await getCertificateById(certificateId, userId)
    if (!certificate) {
      return []
    }

    const files = await db
      .select()
      .from(certificateFiles)
      .where(eq(certificateFiles.certificateId, certificateId))
      .orderBy(asc(certificateFiles.uploadOrder), asc(certificateFiles.createdAt))

    return files
  } catch (error) {
    console.error("Error fetching certificate files:", error)
    throw error
  }
}

/**
 * Get certificate with all its files
 */
export async function getCertificateWithFiles(id: string, userId: string) {
  try {
    const certificate = await getCertificateById(id, userId)
    if (!certificate) {
      return null
    }

    const files = await getCertificateFiles(id, userId)

    return {
      ...certificate,
      files
    }
  } catch (error) {
    console.error("Error fetching certificate with files:", error)
    throw error
  }
}

/**
 * Create certificate with multiple files
 */
export async function createCertificateWithFiles(
  certificateData: {
    id: string
    name: string
    issuingAuthority: string
    certificateNumber: string
    dateIssued: Date
    expiryDate?: Date | null
    notes?: string | null
    isFavorite?: boolean
    tags?: string | null
    userId: string
  },
  files: Array<{
    id: string
    fileName: string
    fileUrl: string
    fileSize: number
    fileType: string
    uploadthingKey?: string
  }>
) {
  try {
    const now = new Date()

    // Create certificate record
    await db.insert(certificates).values({
      ...certificateData,
      // For backward compatibility, store first file in legacy fields
      documentUrl: files.length > 0 ? files[0].fileUrl : null,
      documentName: files.length > 0 ? files[0].fileName : null,
      documentSize: files.length > 0 ? `${Math.round(files[0].fileSize / 1024)} KB` : null,
      documentType: files.length > 0 ? files[0].fileType : null,
      isFavorite: certificateData.isFavorite || false,
      tags: certificateData.tags || null,
      createdAt: now,
      updatedAt: now,
    })

    // Create file records
    if (files.length > 0) {
      const fileRecords = files.map((file, index) => ({
        id: file.id,
        certificateId: certificateData.id,
        fileName: file.fileName,
        fileUrl: file.fileUrl,
        fileSize: file.fileSize,
        fileType: file.fileType,
        uploadthingKey: file.uploadthingKey,
        uploadOrder: index,
        createdAt: now,
      }))

      await db.insert(certificateFiles).values(fileRecords)
    }

    return { success: true }
  } catch (error) {
    console.error("Error creating certificate with files:", error)
    throw error
  }
}

/**
 * Add files to an existing certificate
 */
export async function addCertificateFiles(
  certificateId: string,
  userId: string,
  files: Array<{
    id: string
    fileName: string
    fileUrl: string
    fileSize: number
    fileType: string
    uploadthingKey?: string
  }>
) {
  try {
    // Verify certificate ownership
    const certificate = await getCertificateById(certificateId, userId)
    if (!certificate) {
      throw new Error("Certificate not found or access denied")
    }

    // Get current max upload order
    const existingFiles = await getCertificateFiles(certificateId, userId)
    const maxOrder = existingFiles.length > 0 ? Math.max(...existingFiles.map(f => f.uploadOrder)) : -1

    const now = new Date()
    const fileRecords = files.map((file, index) => ({
      id: file.id,
      certificateId,
      fileName: file.fileName,
      fileUrl: file.fileUrl,
      fileSize: file.fileSize,
      fileType: file.fileType,
      uploadthingKey: file.uploadthingKey,
      uploadOrder: maxOrder + 1 + index,
      createdAt: now,
    }))

    await db.insert(certificateFiles).values(fileRecords)
    return { success: true }
  } catch (error) {
    console.error("Error adding certificate files:", error)
    throw error
  }
}

/**
 * Delete a specific certificate file
 */
export async function deleteCertificateFile(fileId: string, userId: string) {
  try {
    // First verify the file belongs to a certificate owned by the user
    const fileResult = await db
      .select({
        file: certificateFiles,
        certificate: certificates
      })
      .from(certificateFiles)
      .innerJoin(certificates, eq(certificateFiles.certificateId, certificates.id))
      .where(and(
        eq(certificateFiles.id, fileId),
        eq(certificates.userId, userId)
      ))
      .limit(1)

    if (!fileResult[0]) {
      throw new Error("File not found or access denied")
    }

    await db.delete(certificateFiles).where(eq(certificateFiles.id, fileId))
    return { success: true, file: fileResult[0].file }
  } catch (error) {
    console.error("Error deleting certificate file:", error)
    throw error
  }
}

// ============================================================================
// CERTIFICATE QUERIES AND FILTERS
// ============================================================================

/**
 * Get expiring certificates within specified days
 */
export async function getExpiringCertificates(userId: string, days: number) {
  try {
    const today = new Date()
    const futureDate = new Date()
    futureDate.setDate(today.getDate() + days)

    return await db
      .select()
      .from(certificates)
      .where(
        and(
          eq(certificates.userId, userId),
          gte(certificates.expiryDate!, today),
          lte(certificates.expiryDate!, futureDate),
        ),
      )
  } catch (error) {
    console.error("Error getting expiring certificates:", error)
    return []
  }
}

/**
 * Get recently created certificates
 */
export async function getRecentCertificates(userId: string, days: number) {
  try {
    const today = new Date()
    const pastDate = new Date()
    pastDate.setDate(today.getDate() - days)

    return await db
      .select()
      .from(certificates)
      .where(and(eq(certificates.userId, userId), gte(certificates.createdAt, pastDate)))
  } catch (error) {
    console.error("Error getting recent certificates:", error)
    return []
  }
}

/**
 * Get favorite certificates
 */
export async function getFavoriteCertificates(userId: string) {
  try {
    return await db
      .select()
      .from(certificates)
      .where(and(eq(certificates.userId, userId), eq(certificates.isFavorite, true)))
      .orderBy(desc(certificates.updatedAt))
  } catch (error) {
    console.error("Error getting favorite certificates:", error)
    return []
  }
}

/**
 * Get expired certificates
 */
export async function getExpiredCertificates(userId: string) {
  try {
    const today = new Date()
    return await db
      .select()
      .from(certificates)
      .where(
        and(
          eq(certificates.userId, userId),
          lte(certificates.expiryDate!, today)
        )
      )
      .orderBy(desc(certificates.expiryDate))
  } catch (error) {
    console.error("Error getting expired certificates:", error)
    return []
  }
}

/**
 * Enhanced certificate search with filters and sorting
 */
export async function getCertificatesWithFilters(
  userId: string,
  options: {
    search?: string
    filter?: "all" | "favorites" | "expiring-soon" | "expired"
    tags?: string[]
    sortBy?: "name" | "dateIssued" | "expiryDate"
    sortOrder?: "asc" | "desc"
    limit?: number
    offset?: number
  } = {}
) {
  try {
    const {
      search = "",
      filter = "all",
      tags,
      sortBy = "expiryDate",
      sortOrder = "asc",
      limit,
      offset = 0,
    } = options

    // Build where conditions
    const conditions = [eq(certificates.userId, userId)]

    // Apply search filter
    if (search) {
      conditions.push(
        or(
          ilike(certificates.name, `%${search}%`),
          ilike(certificates.certificateNumber, `%${search}%`),
          ilike(certificates.issuingAuthority, `%${search}%`),
          ilike(certificates.tags, `%${search}%`)
        )!
      )
    }

    // Apply tags filter
    if (tags && tags.length > 0) {
      const tagConditions = tags.map(tag =>
        ilike(certificates.tags, `%${tag}%`)
      )
      conditions.push(or(...tagConditions)!)
    }

    // Apply specific filters
    const today = new Date()
    switch (filter) {
      case "favorites":
        conditions.push(eq(certificates.isFavorite, true))
        break
      case "expiring-soon":
        const thirtyDaysFromNow = new Date()
        thirtyDaysFromNow.setDate(today.getDate() + 30)
        conditions.push(
          and(
            gte(certificates.expiryDate!, today),
            lte(certificates.expiryDate!, thirtyDaysFromNow)
          )!
        )
        break
      case "expired":
        conditions.push(lte(certificates.expiryDate!, today))
        break
    }

    // Build and execute query with sorting
    let orderByClause
    if (sortBy === 'name') {
      orderByClause = sortOrder === "desc" ? desc(certificates.name) : asc(certificates.name)
    } else if (sortBy === 'dateIssued') {
      orderByClause = sortOrder === "desc" ? desc(certificates.dateIssued) : asc(certificates.dateIssued)
    } else {
      orderByClause = sortOrder === "desc" ? desc(certificates.expiryDate) : asc(certificates.expiryDate)
    }

    // Build complete query
    if (limit && offset > 0) {
      return await db.select().from(certificates).where(and(...conditions)).orderBy(orderByClause).limit(limit).offset(offset)
    } else if (limit) {
      return await db.select().from(certificates).where(and(...conditions)).orderBy(orderByClause).limit(limit)
    } else if (offset > 0) {
      return await db.select().from(certificates).where(and(...conditions)).orderBy(orderByClause).offset(offset)
    } else {
      return await db.select().from(certificates).where(and(...conditions)).orderBy(orderByClause)
    }
  } catch (error) {
    console.error("Error getting certificates with filters:", error)
    return []
  }
}

/**
 * Get certificate statistics for a user
 */
export async function getCertificateStats(userId: string) {
  try {
    const totalCerts = await db
      .select({ count: count() })
      .from(certificates)
      .where(eq(certificates.userId, userId))

    const favoriteCerts = await db
      .select({ count: count() })
      .from(certificates)
      .where(and(eq(certificates.userId, userId), eq(certificates.isFavorite, true)))

    const today = new Date()
    const expiredCerts = await db
      .select({ count: count() })
      .from(certificates)
      .where(and(eq(certificates.userId, userId), lte(certificates.expiryDate!, today)))

    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(today.getDate() + 30)
    const expiringSoonCerts = await db
      .select({ count: count() })
      .from(certificates)
      .where(
        and(
          eq(certificates.userId, userId),
          gte(certificates.expiryDate!, today),
          lte(certificates.expiryDate!, thirtyDaysFromNow)
        )
      )

    return {
      total: totalCerts[0].count,
      favorites: favoriteCerts[0].count,
      expired: expiredCerts[0].count,
      expiringSoon: expiringSoonCerts[0].count,
    }
  } catch (error) {
    console.error("Error getting certificate stats:", error)
    return {
      total: 0,
      favorites: 0,
      expired: 0,
      expiringSoon: 0,
    }
  }
}

// ============================================================================
// CERTIFICATE ACTIONS AND BULK OPERATIONS
// ============================================================================

/**
 * Toggle certificate favorite status
 */
export async function toggleCertificateFavorite(id: string, userId: string) {
  try {
    const certificate = await getCertificateById(id, userId)
    if (!certificate) {
      throw new Error("Certificate not found")
    }

    await db
      .update(certificates)
      .set({
        isFavorite: !certificate.isFavorite,
        updatedAt: new Date(),
      })
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))

    return { success: true, isFavorite: !certificate.isFavorite }
  } catch (error) {
    console.error("Error toggling certificate favorite:", error)
    throw error
  }
}

/**
 * Bulk delete certificates
 */
export async function bulkDeleteCertificates(ids: string[], userId: string) {
  try {
    // Use the enhanced deleteCertificate function for each certificate
    // This ensures proper file cleanup for both legacy and multi-file certificates
    for (const id of ids) {
      await deleteCertificate(id, userId)
    }
    return { success: true }
  } catch (error) {
    console.error("Error bulk deleting certificates:", error)
    throw error
  }
}

/**
 * Bulk update certificate favorites
 */
export async function bulkUpdateCertificateFavorites(
  ids: string[],
  userId: string,
  isFavorite: boolean
) {
  try {
    for (const id of ids) {
      await db
        .update(certificates)
        .set({
          isFavorite,
          updatedAt: new Date(),
        })
        .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    }
    return { success: true }
  } catch (error) {
    console.error("Error bulk updating certificate favorites:", error)
    throw error
  }
}
