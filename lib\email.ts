import { sendCriticalEmailWithRetry } from '@/lib/email-retry'
import { Resend } from 'resend'

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY)

// Email configuration
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>'
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
const APP_NAME = 'Sealog'

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

/**
 * Send email using Resend
 */
export async function sendEmail(options: EmailOptions): Promise<{ success: boolean; error?: string }> {
  try {
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not configured')
      return { success: false, error: 'Email service not configured' }
    }

    const result = await resend.emails.send({
      from: FROM_EMAIL,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    })

    if (result.error) {
      console.error('Resend error:', result.error)

      // Development mode: Log email content when delivery fails due to sandbox restrictions
      if (process.env.NODE_ENV === 'development' && result.error.message?.includes('testing emails')) {
        console.log('\n🔧 DEVELOPMENT MODE - Email Content (delivery restricted to verified addresses):')
        console.log('📧 To:', options.to)
        console.log('📧 Subject:', options.subject)
        console.log('📧 Text Content:')
        console.log(options.text || 'No text content')
        console.log('\n📧 HTML Content:')
        console.log(options.html)
        console.log('\n✅ Email would be delivered in production with verified domain\n')
      }

      return { success: false, error: result.error.message }
    }

    console.log('Email sent successfully:', result.data?.id)
    return { success: true }
  } catch (error) {
    console.error('Email sending failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown email error'
    }
  }
}

/**
 * Send email verification email
 */
export async function sendVerificationEmail(
  email: string,
  name: string,
  token: string
): Promise<{ success: boolean; error?: string }> {
  const verificationUrl = `${APP_URL}/verify-email?token=${token}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - ${APP_NAME}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #0066cc; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
        .security-note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">⚓ ${APP_NAME}</div>
          <h1>Verify Your Email Address</h1>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          <p>Welcome to ${APP_NAME}! To complete your account setup and start managing your maritime certifications, please verify your email address.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </div>

          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${verificationUrl}
          </p>

          <div class="security-note">
            <strong>🔒 Security Note:</strong> This verification link will expire in 48 hours for your security. If you didn't create an account with ${APP_NAME}, please ignore this email.
          </div>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Welcome to ${APP_NAME}!

    Hello ${name},

    To complete your account setup, please verify your email address by clicking the link below:

    ${verificationUrl}

    This link will expire in 48 hours for your security.

    If you didn't create an account with ${APP_NAME}, please ignore this email.

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendEmail({
    to: email,
    subject: `Verify your email address - ${APP_NAME}`,
    html,
    text,
  })
}

/**
 * Send welcome email for OAuth users (auto-verified)
 */
export async function sendWelcomeEmail(
  email: string,
  name: string,
  provider: string
): Promise<{ success: boolean; error?: string }> {
  const dashboardUrl = `${APP_URL}/dashboard`
  const profileUrl = `${APP_URL}/profile`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to ${APP_NAME}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #0066cc; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 10px; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
        .security-tips { background: #e8f5e8; border: 1px solid #c3e6c3; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .feature-list { background: white; padding: 20px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">⚓ ${APP_NAME}</div>
          <h1>Welcome Aboard!</h1>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          <p>Welcome to ${APP_NAME}! Your account has been successfully created using your ${provider} account. You're all set to start managing your maritime certifications.</p>

          <div class="feature-list">
            <h3>🎯 What you can do now:</h3>
            <ul>
              <li><strong>Upload Certificates:</strong> Add your maritime certifications with file attachments</li>
              <li><strong>Track Expiry Dates:</strong> Never miss a renewal with automatic reminders</li>
              <li><strong>Organize by Category:</strong> Keep your certifications organized and searchable</li>
              <li><strong>Secure Storage:</strong> Your certificates are safely stored and backed up</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${dashboardUrl}" class="button">Go to Dashboard</a>
            <a href="${profileUrl}" class="button" style="background: #28a745;">Manage Profile</a>
          </div>

          <div class="security-tips">
            <h3>🔒 Account Security Tips:</h3>
            <ul>
              <li>Your email has been automatically verified through ${provider}</li>
              <li>Consider adding a password to your account for additional login options</li>
              <li>Review your profile settings and privacy preferences</li>
              <li>Enable two-factor authentication for enhanced security (coming soon)</li>
            </ul>
          </div>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Welcome to ${APP_NAME}!

    Hello ${name},

    Your account has been successfully created using your ${provider} account. You're all set to start managing your maritime certifications.

    What you can do now:
    - Upload Certificates: Add your maritime certifications with file attachments
    - Track Expiry Dates: Never miss a renewal with automatic reminders
    - Organize by Category: Keep your certifications organized and searchable
    - Secure Storage: Your certificates are safely stored and backed up

    Get started: ${dashboardUrl}
    Manage your profile: ${profileUrl}

    Account Security:
    - Your email has been automatically verified through ${provider}
    - Consider adding a password to your account for additional login options
    - Review your profile settings and privacy preferences

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendEmail({
    to: email,
    subject: `Welcome to ${APP_NAME} - Your Maritime Certification Platform`,
    html,
    text,
  })
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  token: string
): Promise<{ success: boolean; error?: string }> {
  const resetUrl = `${APP_URL}/reset-password?token=${token}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - ${APP_NAME}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%); color: white; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .button { display: inline-block; background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%); color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .footer { background-color: #f1f5f9; padding: 30px; text-align: center; font-size: 14px; color: #64748b; }
        .security-notice { background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 20px 0; }
        .security-notice h3 { margin: 0 0 8px 0; color: #92400e; }
        .security-notice p { margin: 0; color: #92400e; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Reset Your Password</h1>
          <p>Secure access to your maritime certification platform</p>
        </div>

        <div class="content">
          <h2>Hello ${name},</h2>

          <p>We received a request to reset the password for your ${APP_NAME} account. If you made this request, click the button below to set a new password:</p>

          <div style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset My Password</a>
          </div>

          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #f1f5f9; padding: 12px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>

          <div class="security-notice">
            <h3>🔒 Security Information</h3>
            <p>This password reset link will expire in 48 hours for your security. If you didn't request this password reset, please ignore this email - your account remains secure.</p>
          </div>

          <p>If you continue to have problems, please contact our support team.</p>

          <p>Best regards,<br>The ${APP_NAME} Team</p>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Reset Your Password - ${APP_NAME}

    Hello ${name},

    We received a request to reset the password for your ${APP_NAME} account. If you made this request, click the link below to set a new password:

    ${resetUrl}

    This link will expire in 48 hours for your security.

    If you didn't request this password reset, please ignore this email - your account remains secure.

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendEmail({
    to: email,
    subject: `Reset your password - ${APP_NAME}`,
    html,
    text,
  })
}

/**
 * Send verification reminder email
 */
export async function sendVerificationReminderEmail(
  email: string,
  name: string,
  token: string
): Promise<{ success: boolean; error?: string }> {
  const verificationUrl = `${APP_URL}/verify-email?token=${token}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification Reminder - ${APP_NAME}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #0066cc; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
        .reminder-note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">⚓ ${APP_NAME}</div>
          <h1>Email Verification Reminder</h1>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          <p>We noticed you haven't verified your email address yet. To access your ${APP_NAME} account and start managing your maritime certifications, please verify your email.</p>

          <div class="reminder-note">
            <strong>⏰ Action Required:</strong> Your verification link will expire soon. Please verify your email address to avoid account restrictions.
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </div>

          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${verificationUrl}
          </p>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Email Verification Reminder - ${APP_NAME}

    Hello ${name},

    We noticed you haven't verified your email address yet. To access your ${APP_NAME} account, please verify your email:

    ${verificationUrl}

    This link will expire soon for your security.

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendEmail({
    to: email,
    subject: `Please verify your email - ${APP_NAME}`,
    html,
    text,
  })
}

/**
 * Send account deletion confirmation email
 */
export async function sendAccountDeletionEmail(
  email: string,
  name: string,
  options: {
    type: "immediate" | "scheduled"
    deletionDate: Date
    recoveryToken?: string
    reason?: string
  }
): Promise<{ success: boolean; error?: string }> {
  const { type, deletionDate, recoveryToken, reason } = options
  const recoveryUrl = recoveryToken ? `${APP_URL}/recover-account?token=${recoveryToken}&email=${encodeURIComponent(email)}` : null

  const isImmediate = type === "immediate"
  const subject = isImmediate
    ? `Account Deleted - ${APP_NAME}`
    : `Account Deletion Scheduled - ${APP_NAME}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .warning-box { background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .recovery-box { background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 10px 0; }
        .recovery-button { background: #0ea5e9; }
        .footer { background: #f8fafc; padding: 30px; text-align: center; color: #64748b; font-size: 14px; }
        .timeline { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1 style="margin: 0; font-size: 28px;">⚠️ Account Deletion ${isImmediate ? 'Completed' : 'Scheduled'}</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">${APP_NAME} - Maritime Certification Platform</p>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          ${isImmediate ? `
            <div class="warning-box">
              <h3 style="margin-top: 0; color: #dc2626;">🗑️ Your account has been permanently deleted</h3>
              <p><strong>Deletion completed:</strong> ${deletionDate.toLocaleDateString()} at ${deletionDate.toLocaleTimeString()}</p>
              ${reason ? `<p><strong>Reason provided:</strong> ${reason}</p>` : ''}
              <p>All your personal information has been anonymized and your certificates and files have been removed from our systems.</p>
            </div>

            <h3>What happens now:</h3>
            <ul>
              <li>✅ Personal information anonymized</li>
              <li>✅ All certificates and files deleted</li>
              <li>✅ Account access permanently disabled</li>
              <li>✅ Email subscriptions cancelled</li>
            </ul>
          ` : `
            <div class="warning-box">
              <h3 style="margin-top: 0; color: #dc2626;">⏰ Your account deletion has been scheduled</h3>
              <p><strong>Deletion scheduled for:</strong> ${deletionDate.toLocaleDateString()} at ${deletionDate.toLocaleTimeString()}</p>
              ${reason ? `<p><strong>Reason provided:</strong> ${reason}</p>` : ''}
              <p>You have <strong>30 days</strong> to recover your account if you change your mind.</p>
            </div>

            ${recoveryUrl ? `
              <div class="recovery-box">
                <h3 style="margin-top: 0; color: #0ea5e9;">🔄 Account Recovery Available</h3>
                <p>If you change your mind, you can recover your account within the next 30 days:</p>
                <div style="text-align: center; margin: 20px 0;">
                  <a href="${recoveryUrl}" class="button recovery-button">Recover My Account</a>
                </div>
                <p style="font-size: 14px; color: #64748b;">This recovery link will expire on ${deletionDate.toLocaleDateString()}</p>
              </div>
            ` : ''}

            <div class="timeline">
              <h3>📅 Deletion Timeline:</h3>
              <ul>
                <li><strong>Now:</strong> Account access disabled, data marked for deletion</li>
                <li><strong>Next 30 days:</strong> Data retained for recovery (if requested)</li>
                <li><strong>After 30 days:</strong> Permanent deletion of all data and files</li>
              </ul>
            </div>
          `}

          <h3>🔒 Data Protection & Privacy:</h3>
          <ul>
            <li>This deletion process complies with GDPR and data protection regulations</li>
            <li>Audit logs are retained for legal compliance purposes only</li>
            <li>No personal information is stored in audit records</li>
            ${!isImmediate ? '<li>During the 30-day period, your data is securely stored and inaccessible</li>' : ''}
          </ul>

          <p>If you have any questions about this process or need assistance, please contact our support team.</p>

          <p>Thank you for using ${APP_NAME}.</p>
          <p>Best regards,<br>The ${APP_NAME} Team</p>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    ${subject}

    Hello ${name},

    ${isImmediate ? `
    Your ${APP_NAME} account has been permanently deleted.

    Deletion completed: ${deletionDate.toLocaleDateString()} at ${deletionDate.toLocaleTimeString()}
    ${reason ? `Reason: ${reason}` : ''}

    What happened:
    - Personal information anonymized
    - All certificates and files deleted
    - Account access permanently disabled
    - Email subscriptions cancelled
    ` : `
    Your ${APP_NAME} account deletion has been scheduled.

    Deletion scheduled for: ${deletionDate.toLocaleDateString()} at ${deletionDate.toLocaleTimeString()}
    ${reason ? `Reason: ${reason}` : ''}

    You have 30 days to recover your account if you change your mind.

    ${recoveryUrl ? `
    To recover your account, visit: ${recoveryUrl}
    This recovery link expires on ${deletionDate.toLocaleDateString()}
    ` : ''}

    Deletion Timeline:
    - Now: Account access disabled, data marked for deletion
    - Next 30 days: Data retained for recovery (if requested)
    - After 30 days: Permanent deletion of all data and files
    `}

    Data Protection & Privacy:
    - This deletion process complies with GDPR and data protection regulations
    - Audit logs are retained for legal compliance purposes only
    - No personal information is stored in audit records

    If you have questions, please contact our support team.

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendCriticalEmailWithRetry(
    () => sendEmail({
      to: email,
      subject,
      html,
      text,
    }),
    `account-deletion-${type}`
  )
}

/**
 * Send account recovery confirmation email
 */
export async function sendAccountRecoveryEmail(
  email: string,
  name: string,
  options: {
    recoveryDate: Date
    ipAddress?: string
    userAgent?: string
  }
): Promise<{ success: boolean; error?: string }> {
  const { recoveryDate, ipAddress, userAgent } = options
  const dashboardUrl = `${APP_URL}/dashboard`
  const securityUrl = `${APP_URL}/settings`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Recovered - ${APP_NAME}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .success-box { background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .security-box { background: #fef3c7; border: 1px solid #fde68a; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 10px; }
        .footer { background: #f8fafc; padding: 30px; text-align: center; color: #64748b; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1 style="margin: 0; font-size: 28px;">✅ Account Successfully Recovered</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">${APP_NAME} - Maritime Certification Platform</p>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          <div class="success-box">
            <h3 style="margin-top: 0; color: #059669;">🎉 Welcome back!</h3>
            <p><strong>Recovery completed:</strong> ${recoveryDate.toLocaleDateString()} at ${recoveryDate.toLocaleTimeString()}</p>
            <p>Your account has been successfully recovered and all your data has been restored. You can now access your maritime certifications and continue using ${APP_NAME}.</p>
          </div>

          <h3>What's been restored:</h3>
          <ul>
            <li>✅ Full account access</li>
            <li>✅ All certificates and files</li>
            <li>✅ Profile information</li>
            <li>✅ Account settings and preferences</li>
            <li>✅ Email notifications (if previously enabled)</li>
          </ul>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${dashboardUrl}" class="button">Go to Dashboard</a>
            <a href="${securityUrl}" class="button" style="background: #0ea5e9;">Review Security Settings</a>
          </div>

          <div class="security-box">
            <h3 style="margin-top: 0; color: #d97706;">🔒 Security Information</h3>
            <p>For your security, here are the details of this recovery:</p>
            <ul>
              <li><strong>Recovery time:</strong> ${recoveryDate.toLocaleDateString()} at ${recoveryDate.toLocaleTimeString()}</li>
              ${ipAddress ? `<li><strong>IP address:</strong> ${ipAddress}</li>` : ''}
              ${userAgent ? `<li><strong>Device/Browser:</strong> ${userAgent.substring(0, 100)}${userAgent.length > 100 ? '...' : ''}</li>` : ''}
            </ul>
            <p>If you did not initiate this recovery, please contact our support team immediately.</p>
          </div>

          <h3>🛡️ Recommended Next Steps:</h3>
          <ul>
            <li>Review your account security settings</li>
            <li>Update your password if you haven't recently</li>
            <li>Check your certificates for any updates needed</li>
            <li>Review your notification preferences</li>
          </ul>

          <p>We're glad to have you back! If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

          <p>Best regards,<br>The ${APP_NAME} Team</p>
        </div>

        <div class="footer">
          <p>This email was sent to ${email}</p>
          <p>© ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Account Successfully Recovered - ${APP_NAME}

    Hello ${name},

    Welcome back! Your account has been successfully recovered.

    Recovery completed: ${recoveryDate.toLocaleDateString()} at ${recoveryDate.toLocaleTimeString()}

    What's been restored:
    - Full account access
    - All certificates and files
    - Profile information
    - Account settings and preferences
    - Email notifications (if previously enabled)

    Security Information:
    - Recovery time: ${recoveryDate.toLocaleDateString()} at ${recoveryDate.toLocaleTimeString()}
    ${ipAddress ? `- IP address: ${ipAddress}` : ''}
    ${userAgent ? `- Device/Browser: ${userAgent.substring(0, 100)}${userAgent.length > 100 ? '...' : ''}` : ''}

    If you did not initiate this recovery, please contact our support team immediately.

    Recommended Next Steps:
    - Review your account security settings
    - Update your password if you haven't recently
    - Check your certificates for any updates needed
    - Review your notification preferences

    Access your account: ${dashboardUrl}
    Security settings: ${securityUrl}

    © ${new Date().getFullYear()} ${APP_NAME} - Maritime Certification Platform
  `

  return sendEmail({
    to: email,
    subject: `Account Successfully Recovered - ${APP_NAME}`,
    html,
    text,
  })
}
