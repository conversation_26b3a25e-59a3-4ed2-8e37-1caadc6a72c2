import { logger } from "@/lib/logger";

// Environment variable configuration
export interface EnvVarConfig {
  name: string;
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'json';
  description: string;
  defaultValue?: string;
  validation?: (value: string) => boolean;
  sensitive?: boolean; // Don't log the actual value
  environments?: ('development' | 'production' | 'test')[];
}

// Environment variable definitions
export const environmentVariables: EnvVarConfig[] = [
  // Database
  {
    name: 'DATABASE_URL',
    required: true,
    type: 'url',
    description: 'PostgreSQL database connection URL',
    sensitive: true,
    validation: (value) => value.startsWith('postgresql://') || value.startsWith('postgres://'),
  },
  
  // Authentication
  {
    name: 'NEXTAUTH_SECRET',
    required: true,
    type: 'string',
    description: 'NextAuth.js secret key for JWT signing',
    sensitive: true,
    validation: (value) => value.length >= 32,
  },
  {
    name: 'NEXTAUTH_URL',
    required: true,
    type: 'url',
    description: 'Base URL for NextAuth.js callbacks',
    environments: ['production'],
  },
  
  // Email Service
  {
    name: 'RESEND_API_KEY',
    required: true,
    type: 'string',
    description: 'Resend API key for email delivery',
    sensitive: true,
    validation: (value) => value.startsWith('re_'),
  },
  {
    name: 'FROM_EMAIL',
    required: true,
    type: 'email',
    description: 'From email address for system emails',
  },
  
  // File Upload
  {
    name: 'UPLOADTHING_SECRET',
    required: true,
    type: 'string',
    description: 'UploadThing secret key',
    sensitive: true,
    validation: (value) => value.startsWith('sk_'),
  },
  {
    name: 'UPLOADTHING_APP_ID',
    required: true,
    type: 'string',
    description: 'UploadThing application ID',
  },
  
  // Application URLs
  {
    name: 'NEXT_PUBLIC_APP_URL',
    required: true,
    type: 'url',
    description: 'Public application URL',
  },
  {
    name: 'COOKIE_DOMAIN',
    required: false,
    type: 'string',
    description: 'Cookie domain for session management',
    environments: ['production'],
  },
  
  // Rate Limiting (Optional - Redis)
  {
    name: 'UPSTASH_REDIS_REST_URL',
    required: false,
    type: 'url',
    description: 'Upstash Redis URL for distributed rate limiting',
    sensitive: true,
  },
  {
    name: 'UPSTASH_REDIS_REST_TOKEN',
    required: false,
    type: 'string',
    description: 'Upstash Redis token',
    sensitive: true,
  },
  
  // Cron Jobs
  {
    name: 'CRON_SECRET',
    required: true,
    type: 'string',
    description: 'Secret key for cron job authentication',
    sensitive: true,
    validation: (value) => value.length >= 32,
  },
  
  // Logging Configuration
  {
    name: 'NEXT_PUBLIC_ENABLE_LOGGING',
    required: false,
    type: 'boolean',
    description: 'Enable application logging',
    defaultValue: 'true',
  },
  {
    name: 'NEXT_PUBLIC_LOG_LEVEL',
    required: false,
    type: 'string',
    description: 'Logging level (debug, info, warn, error)',
    defaultValue: 'info',
    validation: (value) => ['debug', 'info', 'warn', 'error'].includes(value),
  },
  {
    name: 'NEXT_PUBLIC_LOG_CONTEXTS',
    required: false,
    type: 'string',
    description: 'Comma-separated list of log contexts to enable',
    defaultValue: 'auth,api,database,email',
  },
  
  // Production-specific
  {
    name: 'NODE_ENV',
    required: true,
    type: 'string',
    description: 'Node.js environment',
    validation: (value) => ['development', 'production', 'test'].includes(value),
  },
  {
    name: 'ENABLE_SOURCE_MAPS',
    required: false,
    type: 'boolean',
    description: 'Enable source maps in production',
    defaultValue: 'false',
    environments: ['production'],
  },
  
  // OAuth Providers (Optional)
  {
    name: 'GOOGLE_CLIENT_ID',
    required: false,
    type: 'string',
    description: 'Google OAuth client ID',
  },
  {
    name: 'GOOGLE_CLIENT_SECRET',
    required: false,
    type: 'string',
    description: 'Google OAuth client secret',
    sensitive: true,
  },
];

// Validation result interface
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  missing: string[];
  invalid: string[];
  summary: {
    total: number;
    required: number;
    present: number;
    valid: number;
  };
}

// Type validation functions
const typeValidators = {
  string: (value: string) => typeof value === 'string' && value.length > 0,
  number: (value: string) => !isNaN(Number(value)),
  boolean: (value: string) => ['true', 'false', '1', '0'].includes(value.toLowerCase()),
  url: (value: string) => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },
  email: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
  json: (value: string) => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  },
};

// Validate environment variables
export function validateEnvironment(): ValidationResult {
  const currentEnv = process.env.NODE_ENV || 'development';
  const errors: string[] = [];
  const warnings: string[] = [];
  const missing: string[] = [];
  const invalid: string[] = [];
  
  let requiredCount = 0;
  let presentCount = 0;
  let validCount = 0;
  
  for (const config of environmentVariables) {
    // Check if variable applies to current environment
    if (config.environments && !config.environments.includes(currentEnv as any)) {
      continue;
    }
    
    const value = process.env[config.name];
    
    // Count required variables
    if (config.required) {
      requiredCount++;
    }
    
    // Check if variable is present
    if (value === undefined || value === '') {
      if (config.required) {
        missing.push(config.name);
        errors.push(`Missing required environment variable: ${config.name} - ${config.description}`);
      } else if (!config.defaultValue) {
        warnings.push(`Optional environment variable not set: ${config.name} - ${config.description}`);
      }
      continue;
    }
    
    presentCount++;
    
    // Validate type
    const typeValidator = typeValidators[config.type];
    if (!typeValidator(value)) {
      invalid.push(config.name);
      errors.push(`Invalid type for ${config.name}: expected ${config.type}, got "${config.sensitive ? '[REDACTED]' : value}"`);
      continue;
    }
    
    // Custom validation
    if (config.validation && !config.validation(value)) {
      invalid.push(config.name);
      errors.push(`Custom validation failed for ${config.name}: ${config.description}`);
      continue;
    }
    
    validCount++;
  }
  
  const result: ValidationResult = {
    valid: errors.length === 0,
    errors,
    warnings,
    missing,
    invalid,
    summary: {
      total: environmentVariables.length,
      required: requiredCount,
      present: presentCount,
      valid: validCount,
    },
  };
  
  // Log validation results
  if (result.valid) {
    logger.info("environment", "Environment validation passed", {
      summary: result.summary,
      warningCount: warnings.length,
    });
  } else {
    logger.error("environment", "Environment validation failed", {
      summary: result.summary,
      errorCount: errors.length,
      missingCount: missing.length,
      invalidCount: invalid.length,
    });
  }
  
  return result;
}

// Generate environment template
export function generateEnvironmentTemplate(): string {
  const currentEnv = process.env.NODE_ENV || 'development';
  const lines: string[] = [];
  
  lines.push('# Sealog Maritime Certification Platform - Environment Variables');
  lines.push('# Generated template - replace values with your actual configuration');
  lines.push('');
  
  const categories = {
    'Database': ['DATABASE_URL'],
    'Authentication': ['NEXTAUTH_SECRET', 'NEXTAUTH_URL'],
    'Email Service': ['RESEND_API_KEY', 'FROM_EMAIL'],
    'File Upload': ['UPLOADTHING_SECRET', 'UPLOADTHING_APP_ID'],
    'Application URLs': ['NEXT_PUBLIC_APP_URL', 'COOKIE_DOMAIN'],
    'Rate Limiting': ['UPSTASH_REDIS_REST_URL', 'UPSTASH_REDIS_REST_TOKEN'],
    'Cron Jobs': ['CRON_SECRET'],
    'Logging': ['NEXT_PUBLIC_ENABLE_LOGGING', 'NEXT_PUBLIC_LOG_LEVEL', 'NEXT_PUBLIC_LOG_CONTEXTS'],
    'Production': ['NODE_ENV', 'ENABLE_SOURCE_MAPS'],
    'OAuth Providers': ['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET'],
  };
  
  for (const [category, varNames] of Object.entries(categories)) {
    const categoryVars = environmentVariables.filter(v => varNames.includes(v.name));
    if (categoryVars.length === 0) continue;
    
    lines.push(`# ${category}`);
    
    for (const config of categoryVars) {
      // Skip if not applicable to current environment
      if (config.environments && !config.environments.includes(currentEnv as any)) {
        continue;
      }
      
      const required = config.required ? ' (REQUIRED)' : ' (OPTIONAL)';
      lines.push(`# ${config.description}${required}`);
      
      let value = '';
      if (config.defaultValue) {
        value = config.defaultValue;
      } else if (config.sensitive) {
        value = 'your_secret_key_here';
      } else {
        switch (config.type) {
          case 'url':
            value = config.name.includes('PUBLIC') ? 'http://localhost:3000' : 'https://your-domain.com';
            break;
          case 'email':
            value = '<EMAIL>';
            break;
          case 'boolean':
            value = 'true';
            break;
          default:
            value = 'your_value_here';
        }
      }
      
      lines.push(`${config.name}="${value}"`);
      lines.push('');
    }
  }
  
  return lines.join('\n');
}

// Check critical environment variables at startup
export function checkCriticalEnvironment(): boolean {
  const critical = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'RESEND_API_KEY'];
  const missing = critical.filter(name => !process.env[name]);
  
  if (missing.length > 0) {
    logger.error("environment", "Critical environment variables missing", { missing });
    return false;
  }
  
  return true;
}

// Environment health check
export function getEnvironmentHealth() {
  const validation = validateEnvironment();
  
  return {
    healthy: validation.valid,
    summary: validation.summary,
    issues: validation.errors.length > 0 ? validation.errors : validation.warnings,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  };
}
