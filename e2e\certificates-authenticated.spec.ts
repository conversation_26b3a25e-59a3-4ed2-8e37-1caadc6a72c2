import { expect, test } from '@playwright/test';

test.describe('Certificates Management - Core Functionality', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' });

  test.beforeEach(async ({ page }) => {
    // Clean up any existing test data
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_certificates' }
    });

    // Seed fresh test data
    await page.request.post('/api/admin/seed-notifications', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'seed_test_data' }
    });
  });

  test('should display certificates dashboard and basic functionality', async ({ page }) => {
    await page.goto('/certificates');

    // Check if certificates page loads
    await expect(page.locator('h1')).toContainText('Certificates');

    // Check if filter cards are visible using data attributes
    await expect(page.locator('[data-testid="filter-all"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-favorites"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-expiring-soon"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-expired"]')).toBeVisible();

    // Should show test certificates (if any exist)
    const certificateCards = page.locator('[data-testid^="certificate-card"]');
    const cardCount = await certificateCards.count();
    if (cardCount > 0) {
      await expect(certificateCards.first()).toBeVisible();
    }

    // Check view toggle functionality
    const gridTab = page.locator('text=Grid');
    const tableTab = page.locator('text=Table');
    await expect(gridTab).toBeVisible();
    await expect(tableTab).toBeVisible();

    // Check if selection controls are available (checkboxes should be visible by default)
    // Switch to table view to verify checkboxes
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(300);

      // Should have select all checkbox
      const selectAllCheckbox = page.locator('[data-testid="select-all-checkbox"]');
      await expect(selectAllCheckbox).toBeVisible();
    }

    console.log('✅ Certificates dashboard loads correctly with new bulk operations UI');
  });

  test('should handle search and filtering with result verification', async ({ page }) => {
    await page.goto('/certificates');

    // Test search functionality using specific data attribute
    const searchInput = page.locator('[data-testid="certificates-search"]');
    if (await searchInput.isVisible()) {
      // Search for STCW (we know this exists from our test data)
      await searchInput.fill('STCW');
      await page.waitForTimeout(500); // Wait for search to process

      // Verify search results
      const results = page.locator('[data-testid^="certificate-card"]');
      const resultCount = await results.count();

      if (resultCount > 0) {
        // Verify all results contain "STCW"
        for (let i = 0; i < resultCount; i++) {
          await expect(results.nth(i)).toContainText('STCW');
        }
        console.log(`✅ Found ${resultCount} STCW certificates`);
      } else {
        console.log('ℹ️ No STCW certificates found - check test data seeding');
      }

      // Clear search to reset
      await searchInput.clear();
      await page.waitForTimeout(500);
    }

    // Test filter functionality using data attributes
    const favoritesFilter = page.locator('[data-testid="filter-favorites"]');
    if (await favoritesFilter.isVisible()) {
      // Get initial certificate count
      const allCerts = await page.locator('[data-testid^="certificate-card"]').count();

      // Apply favorites filter
      await favoritesFilter.click();
      await page.waitForTimeout(500); // Wait for filter to apply

      // Verify filtering worked
      const favoriteCerts = await page.locator('[data-testid^="certificate-card"]').count();
      expect(favoriteCerts).toBeLessThanOrEqual(allCerts);

      // If favorites exist, verify they have favorite indicators
      if (favoriteCerts > 0) {
        console.log(`✅ Found ${favoriteCerts} favorite certificates (filtered from ${allCerts} total)`);

        // Check for favorite indicators (if they exist in the UI)
        const favoriteIndicators = page.locator('[data-testid="favorite-indicator"]');
        const indicatorCount = await favoriteIndicators.count();
        if (indicatorCount > 0) {
          expect(indicatorCount).toBe(favoriteCerts);
          console.log('✅ All favorite certificates have favorite indicators');
        }
      } else {
        console.log('ℹ️ No favorite certificates found');
      }
    }

    console.log('✅ Search and filtering functionality working with verification');
  });

  test('should navigate to certificate creation', async ({ page }) => {
    await page.goto('/certificates');

    // Find and click add certificate button using data attribute
    const addButton = page.locator('[data-testid="add-certificate-button"]');
    await addButton.click();

    // Should navigate to new certificate page
    await expect(page).toHaveURL('/certificates/new');

    // Should show the form
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="authority"]')).toBeVisible();

    console.log('✅ Certificate creation navigation working');
  });

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/certificates');

    // Should show mobile-optimized layout
    await expect(page.locator('h1')).toBeVisible();

    // Basic mobile functionality should work
    const searchInput = page.locator('[data-testid="certificates-search"]');
    if (await searchInput.isVisible()) {
      await expect(searchInput).toBeVisible();
    }

    console.log('✅ Mobile responsiveness working');
  });
});
