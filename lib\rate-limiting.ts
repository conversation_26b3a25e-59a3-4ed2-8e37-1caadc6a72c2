import { logger } from "@/lib/logger";
import { NextRequest, NextResponse } from "next/server";

// Rate limiting configuration for different endpoint types
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: NextRequest) => string; // Custom key generator
  message?: string; // Custom error message
  headers?: boolean; // Include rate limit headers in response
}

// Default configurations for different endpoint types
export const rateLimitConfigs = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    message: "Too many authentication attempts. Please try again later.",
    headers: true,
  },

  // Password reset - very strict
  passwordReset: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 3, // 3 attempts per 15 minutes
    message: "Too many password reset attempts. Please try again later.",
    headers: true,
  },

  // Email verification - moderate limits
  emailVerification: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3, // 3 attempts per minute
    message: "Too many verification email requests. Please wait before trying again.",
    headers: true,
  },

  // File upload - moderate limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
    message: "Too many upload requests. Please wait before uploading more files.",
    headers: true,
  },

  // Certificate operations - generous limits
  certificates: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 requests per minute
    message: "Too many certificate requests. Please slow down.",
    headers: true,
  },

  // General API - default limits
  general: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
    message: "Too many requests. Please slow down.",
    headers: true,
  },

  // Admin operations - strict limits
  admin: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 requests per minute
    message: "Too many admin requests. Please slow down.",
    headers: true,
  },
} as const;

// In-memory store for rate limiting (for production, use Redis)
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  // Clean up expired entries periodically
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, value] of this.store.entries()) {
      if (now > value.resetTime) {
        this.store.delete(key);
      }
    }
  }

  get(key: string): { count: number; resetTime: number } | undefined {
    const entry = this.store.get(key);
    if (entry && Date.now() > entry.resetTime) {
      this.store.delete(key);
      return undefined;
    }
    return entry;
  }

  set(key: string, value: { count: number; resetTime: number }): void {
    this.store.set(key, value);
  }

  increment(key: string, windowMs: number): { count: number; resetTime: number } {
    const now = Date.now();
    const resetTime = now + windowMs;
    const existing = this.get(key);

    if (!existing) {
      const newEntry = { count: 1, resetTime };
      this.set(key, newEntry);
      return newEntry;
    }

    existing.count++;
    this.set(key, existing);
    return existing;
  }

  destroy() {
    clearInterval(this.cleanupInterval);
    this.store.clear();
  }
}

// Global store instance
const store = new MemoryStore();

// Default key generator - uses IP address
function defaultKeyGenerator(req: NextRequest): string {
  const forwarded = req.headers.get("x-forwarded-for");
  const ip = forwarded ? forwarded.split(",")[0].trim() : "unknown";
  return `rate_limit:${ip}`;
}

// Rate limiting middleware
export function createRateLimit(config: RateLimitConfig) {
  return async function rateLimit(req: NextRequest): Promise<NextResponse | null> {
    const keyGenerator = config.keyGenerator || defaultKeyGenerator;
    const key = keyGenerator(req);

    try {
      const result = store.increment(key, config.windowMs);
      const isLimited = result.count > config.maxRequests;

      // Log rate limiting events
      if (isLimited) {
        logger.warn("api", "Rate limit exceeded", {
          key,
          count: result.count,
          limit: config.maxRequests,
          resetTime: new Date(result.resetTime).toISOString(),
          path: req.nextUrl.pathname,
          method: req.method,
        });
      }

      // Create response with rate limit headers
      if (config.headers) {
        const headers = new Headers();
        headers.set("X-RateLimit-Limit", config.maxRequests.toString());
        headers.set("X-RateLimit-Remaining", Math.max(0, config.maxRequests - result.count).toString());
        headers.set("X-RateLimit-Reset", Math.ceil(result.resetTime / 1000).toString());

        if (isLimited) {
          const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
          headers.set("Retry-After", retryAfter.toString());

          return NextResponse.json(
            {
              error: config.message || "Too many requests",
              code: "RATE_LIMIT_EXCEEDED",
              retryAfter,
            },
            { status: 429, headers }
          );
        }

        // Return headers for successful requests (will be merged with actual response)
        return new NextResponse(null, { headers });
      }

      if (isLimited) {
        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
        return NextResponse.json(
          {
            error: config.message || "Too many requests",
            code: "RATE_LIMIT_EXCEEDED",
            retryAfter,
          },
          { status: 429 }
        );
      }

      return null; // No rate limiting applied
    } catch (error) {
      logger.error("api", "Rate limiting error", {
        error: error instanceof Error ? error.message : String(error),
        key,
        path: req.nextUrl.pathname,
      });

      // On error, allow the request to proceed
      return null;
    }
  };
}

// Convenience functions for common rate limits
export const authRateLimit = createRateLimit(rateLimitConfigs.auth);
export const passwordResetRateLimit = createRateLimit(rateLimitConfigs.passwordReset);
export const emailVerificationRateLimit = createRateLimit(rateLimitConfigs.emailVerification);
export const uploadRateLimit = createRateLimit(rateLimitConfigs.upload);
export const certificatesRateLimit = createRateLimit(rateLimitConfigs.certificates);
export const generalRateLimit = createRateLimit(rateLimitConfigs.general);
export const adminRateLimit = createRateLimit(rateLimitConfigs.admin);

// Helper to determine rate limit type based on path
export function getRateLimitForPath(pathname: string): ReturnType<typeof createRateLimit> {
  if (pathname.includes("/auth/forgot-password") || pathname.includes("/auth/reset-password")) {
    return passwordResetRateLimit;
  }

  if (pathname.includes("/auth/resend-verification") || pathname.includes("/auth/verify-email")) {
    return emailVerificationRateLimit;
  }

  if (pathname.includes("/auth/")) {
    return authRateLimit;
  }

  if (pathname.includes("/uploadthing") || pathname.includes("/upload")) {
    return uploadRateLimit;
  }

  if (pathname.includes("/certificates")) {
    return certificatesRateLimit;
  }

  if (pathname.includes("/admin")) {
    return adminRateLimit;
  }

  return generalRateLimit;
}

// Wrapper function to apply rate limiting to API route handlers
export function withRateLimit<R>(
  handler: (req: NextRequest, context?: any) => Promise<R>,
  rateLimitFn?: ReturnType<typeof createRateLimit>
) {
  return async function rateLimitedHandler(req: NextRequest, context?: any): Promise<R | NextResponse> {
    // Determine rate limit function
    const rateLimit = rateLimitFn || getRateLimitForPath(req.nextUrl.pathname);

    // Apply rate limiting
    const rateLimitResponse = await rateLimit(req);
    if (rateLimitResponse && rateLimitResponse.status === 429) {
      return rateLimitResponse;
    }

    // Call the original handler
    const result = await handler(req, context);

    // Merge rate limit headers if present
    if (rateLimitResponse && result instanceof NextResponse) {
      for (const [key, value] of rateLimitResponse.headers.entries()) {
        if (key.startsWith("X-RateLimit") || key === "Retry-After") {
          result.headers.set(key, value);
        }
      }
    }

    return result;
  };
}
