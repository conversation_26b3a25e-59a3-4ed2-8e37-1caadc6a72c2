import { logger } from "@/lib/logger"
import { NextRequest, NextResponse } from "next/server"

export interface ApiDebugOptions {
  logRequests?: boolean
  logResponses?: boolean
  logHeaders?: boolean
  logBody?: boolean
  logTiming?: boolean
  excludePaths?: string[]
  maxBodyLength?: number
}

const defaultOptions: ApiDebugOptions = {
  logRequests: true,
  logResponses: true,
  logHeaders: process.env.NODE_ENV === "development",
  logBody: process.env.NODE_ENV === "development",
  logTiming: true,
  excludePaths: ["/api/health", "/api/_next"],
  maxBodyLength: 1000,
}

export function createApiDebugMiddleware(options: ApiDebugOptions = {}) {
  const config = { ...defaultOptions, ...options }

  return function apiDebugMiddleware(
    handler: (req: NextRequest) => Promise<NextResponse> | NextResponse
  ) {
    return async function wrappedHandler(req: NextRequest): Promise<NextResponse> {
      const startTime = Date.now()
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      const { pathname } = new URL(req.url)

      // Skip logging for excluded paths
      if (config.excludePaths?.some(path => pathname.startsWith(path))) {
        return handler(req)
      }

      // Log request
      if (config.logRequests) {
        const requestData: any = {
          requestId,
          method: req.method,
          url: req.url,
          pathname,
          userAgent: req.headers.get("user-agent"),
        }

        if (config.logHeaders) {
          requestData.headers = Object.fromEntries(req.headers.entries())
        }

        if (config.logBody && req.method !== "GET") {
          try {
            const body = await req.text()
            if (body && body.length <= (config.maxBodyLength || 1000)) {
              requestData.body = body
            } else if (body) {
              requestData.body = `[Body too large: ${body.length} chars]`
            }
            // Recreate request with the body for the handler
            req = new NextRequest(req.url, {
              method: req.method,
              headers: req.headers,
              body: body || undefined,
            })
          } catch (error) {
            requestData.bodyError = "Failed to read request body"
          }
        }

        logger.info("api", `API Request: ${req.method} ${pathname}`, requestData)
      }

      let response: NextResponse
      let error: Error | null = null

      try {
        response = await handler(req)
      } catch (err) {
        error = err instanceof Error ? err : new Error(String(err))
        logger.error("api", `API Error: ${req.method} ${pathname}`, {
          requestId,
          error: error.message,
          stack: error.stack,
        })

        // Return a proper error response
        response = NextResponse.json(
          { error: "Internal Server Error", requestId },
          { status: 500 }
        )
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      // Log response
      if (config.logResponses) {
        const responseData: any = {
          requestId,
          status: response.status,
          statusText: response.statusText,
        }

        if (config.logTiming) {
          responseData.duration = `${duration}ms`
        }

        if (config.logHeaders) {
          responseData.headers = Object.fromEntries(response.headers.entries())
        }

        if (config.logBody && response.body) {
          try {
            const responseClone = response.clone()
            const body = await responseClone.text()
            if (body && body.length <= (config.maxBodyLength || 1000)) {
              responseData.body = body
            } else if (body) {
              responseData.body = `[Body too large: ${body.length} chars]`
            }
          } catch (error) {
            responseData.bodyError = "Failed to read response body"
          }
        }

        const logLevel = response.status >= 400 ? "error" : "info"
        logger[logLevel]("api", `API Response: ${req.method} ${pathname}`, responseData)
      }

      // Add debug headers in development
      if (process.env.NODE_ENV === "development") {
        response.headers.set("X-Debug-Request-ID", requestId)
        response.headers.set("X-Debug-Duration", `${duration}ms`)
        if (error) {
          response.headers.set("X-Debug-Error", error.message)
        }
      }

      return response
    }
  }
}

// Utility function to wrap API route handlers
export function withApiDebug(
  handler: (req: NextRequest) => Promise<NextResponse> | NextResponse,
  options?: ApiDebugOptions
) {
  const middleware = createApiDebugMiddleware(options)
  return middleware(handler)
}

// Simple request correlation for debugging
export class RequestCorrelation {
  private static correlations: Map<string, any> = new Map()

  static setContext(requestId: string, context: any) {
    this.correlations.set(requestId, {
      ...context,
      timestamp: Date.now(),
    })
  }

  static getContext(requestId: string) {
    return this.correlations.get(requestId)
  }

  static cleanup(maxAge: number = 300000) { // 5 minutes
    const now = Date.now()
    for (const [id, context] of this.correlations.entries()) {
      if (now - context.timestamp > maxAge) {
        this.correlations.delete(id)
      }
    }
  }
}
