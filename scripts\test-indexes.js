// Test script to check database indexes
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testIndexes() {
  try {
    if (!process.env.DATABASE_URL) {
      console.error('DATABASE_URL not found');
      process.exit(1);
    }

    const sql = neon(process.env.DATABASE_URL);
    
    console.log('Testing database connection...');
    const result = await sql`SELECT 1 as test`;
    console.log('✅ Database connected');
    
    // Test if indexes exist
    const indexes = await sql`
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND indexname LIKE '%_idx'
      ORDER BY tablename, indexname
    `;
    
    console.log('📊 Current performance indexes:');
    if (indexes.length === 0) {
      console.log('  No performance indexes found');
    } else {
      indexes.forEach(idx => {
        console.log(`  - ${idx.tablename}.${idx.indexname}`);
      });
    }
    
    // Check table names to understand schema
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('\n📋 Available tables:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

testIndexes();
