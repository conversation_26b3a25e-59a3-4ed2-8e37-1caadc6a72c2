import {
  createCleanupJob,
  getUsersEligibleForHardDeletion,
  performHardDeletion,
  updateCleanupJob
} from "@/lib/db"
import { headers } from "next/headers"
import { NextResponse } from "next/server"

/**
 * Run account cleanup job (hard deletion of accounts soft-deleted 30+ days ago)
 * POST /api/admin/cleanup
 *
 * This endpoint should be called by a cron job or scheduled task
 * In production, you might want to add authentication for admin-only access
 */
export async function POST(req: Request) {
  try {
    // Optional: Add admin authentication here
    // const session = await getServerSession()
    // if (!session || session.role !== 'system_admin') {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    // }

    // Check for authorization header (for cron job authentication)
    const headersList = await headers()
    const authHeader = headersList.get("authorization")
    const cronSecret = process.env.CRON_SECRET || "your-secret-key"

    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Create cleanup job record
    const jobResult = await createCleanupJob({
      jobType: "account_deletion",
      scheduledFor: new Date(),
      metadata: { triggeredBy: "cron" }
    })

    if (!jobResult.success) {
      throw new Error("Failed to create cleanup job record")
    }

    const jobId = jobResult.jobId!

    try {
      // Update job status to running
      await updateCleanupJob(jobId, {
        status: "running",
        startedAt: new Date()
      })

      // Get users eligible for hard deletion
      const eligibleUsers = await getUsersEligibleForHardDeletion()

      console.log(`Found ${eligibleUsers.length} users eligible for hard deletion`)

      let processedCount = 0
      let deletedCount = 0
      const errors: string[] = []

      // Process each eligible user
      for (const user of eligibleUsers) {
        try {
          console.log(`Processing hard deletion for user ${user.id} (${user.email})`)

          const result = await performHardDeletion(user.id)

          if (result.success) {
            deletedCount++
            console.log(`Successfully deleted user ${user.id}: ${result.deletedData.certificates} certificates, ${result.deletedData.files} files`)
          }

          processedCount++
        } catch (userError) {
          const errorMsg = `Failed to delete user ${user.id}: ${userError instanceof Error ? userError.message : 'Unknown error'}`
          console.error(errorMsg)
          errors.push(errorMsg)
          processedCount++
        }
      }

      // Update job status to completed
      await updateCleanupJob(jobId, {
        status: errors.length > 0 ? "completed" : "completed",
        completedAt: new Date(),
        recordsProcessed: processedCount,
        recordsDeleted: deletedCount,
        errors: errors.length > 0 ? errors : undefined
      })

      return NextResponse.json({
        success: true,
        message: `Cleanup job completed successfully`,
        jobId,
        results: {
          usersProcessed: processedCount,
          usersDeleted: deletedCount,
          errors: errors.length,
          errorDetails: errors.length > 0 ? errors : undefined
        }
      })

    } catch (jobError) {
      // Update job status to failed
      await updateCleanupJob(jobId, {
        status: "failed",
        completedAt: new Date(),
        errors: [jobError instanceof Error ? jobError.message : 'Unknown error']
      })

      throw jobError
    }

  } catch (error) {
    console.error("Cleanup job error:", error)
    return NextResponse.json({
      error: "Failed to run cleanup job",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * Get cleanup job status and history
 * GET /api/admin/cleanup
 */
export async function GET() {
  try {
    // Optional: Add admin authentication here
    const headersList = await headers()
    const authHeader = headersList.get("authorization")
    const cronSecret = process.env.CRON_SECRET || "your-secret-key"

    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { db, cleanupJobs } = await import("@/lib/db")
    const { desc, eq } = await import("drizzle-orm")

    // Get recent cleanup jobs
    const recentJobs = await db
      .select()
      .from(cleanupJobs)
      .where(eq(cleanupJobs.jobType, "account_deletion"))
      .orderBy(desc(cleanupJobs.createdAt))
      .limit(10)

    // Get users eligible for deletion
    const eligibleUsers = await getUsersEligibleForHardDeletion()

    return NextResponse.json({
      success: true,
      eligibleUsers: eligibleUsers.length,
      recentJobs: recentJobs.map(job => ({
        id: job.id,
        status: job.status,
        scheduledFor: job.scheduledFor,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        recordsProcessed: job.recordsProcessed,
        recordsDeleted: job.recordsDeleted,
        errors: job.errors ? JSON.parse(job.errors) : null,
        createdAt: job.createdAt
      }))
    })

  } catch (error) {
    console.error("Get cleanup status error:", error)
    return NextResponse.json({
      error: "Failed to get cleanup status"
    }, { status: 500 })
  }
}
