import { AdminSidebar } from "@/components/admin-sidebar";
import { UserRole } from "@/lib/auth-config";
import { getServerSession } from "@/lib/session";
import { redirect } from "next/navigation";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();

  // Check if user is authenticated
  if (!session) {
    redirect("/login?callbackUrl=/admin");
  }

  // Check if user has admin privileges
  if (session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        {/* Admin Sidebar */}
        <AdminSidebar
          session={{
            id: session.userId,
            name: session.name,
            email: session.email,
            role: session.role,
          }}
        />

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
