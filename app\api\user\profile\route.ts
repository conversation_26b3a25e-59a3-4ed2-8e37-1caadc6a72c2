import { NextResponse } from "next/server"
import { getServerSession } from "@/lib/session"
import { getUserByEmail, getUserSocialAccounts } from "@/lib/db"

/**
 * Get current user's profile information
 */
export async function GET() {
  try {
    const session = await getServerSession()
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get full user data from database
    const user = await getUserByEmail(session.email)
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get linked social accounts
    const socialAccounts = await getUserSocialAccounts(user.id)

    // Format social accounts for client
    const linkedAccounts = socialAccounts.map(account => ({
      provider: account.provider,
      connectedAt: account.createdAt,
    }))

    // Check which providers are available
    const availableProviders = {
      google: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
      facebook: !!(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET),
    }

    // Return user profile data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        subscriptionPlan: user.subscriptionPlan,
        emailVerified: user.emailVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        // Additional profile fields (these would be added to the User table in the future)
        phone: null, // Placeholder for future implementation
        position: null, // Placeholder for future implementation
        bio: null, // Placeholder for future implementation
        company: null, // Placeholder for future implementation
        location: null, // Placeholder for future implementation
      },
      linkedAccounts,
      availableProviders
    })

  } catch (error) {
    console.error("Get profile error:", error)
    return NextResponse.json({ error: "Failed to get profile" }, { status: 500 })
  }
}

/**
 * Update current user's profile information
 */
export async function PUT(req: Request) {
  try {
    const session = await getServerSession()
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { name, phone, position, bio, company, location } = await req.json()

    // For now, we'll only update the name field since other fields don't exist in the database yet
    // In the future, these fields would be added to the User table
    
    if (name && name.trim() !== "") {
      // Update user name in database
      // This would require adding an updateUser function to lib/db.ts
      // For now, we'll return success but note that full implementation is needed
      
      return NextResponse.json({
        success: true,
        message: "Profile updated successfully",
        note: "Currently only name updates are supported. Additional fields will be implemented in future updates."
      })
    }

    return NextResponse.json({
      success: true,
      message: "No changes to save"
    })

  } catch (error) {
    console.error("Update profile error:", error)
    return NextResponse.json({ error: "Failed to update profile" }, { status: 500 })
  }
}
