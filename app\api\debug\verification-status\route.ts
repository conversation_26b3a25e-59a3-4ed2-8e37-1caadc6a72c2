import { getUserById } from "@/lib/db"
import { getServerSession } from "@/lib/session"
import { getServerSession as getNextAuthSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { NextResponse } from "next/server"

/**
 * Debug API to check verification status directly from database
 * This helps debug the verification polling mechanism
 */
export async function GET() {
  try {
    // Get session to identify user
    const nextAuthSession = await getNextAuthSession(authOptions)
    const customSession = await getServerSession()
    
    let userId: string | null = null
    let sessionSource = "none"
    
    if (nextAuthSession?.user?.id) {
      userId = nextAuthSession.user.id
      sessionSource = "nextauth"
    } else if (customSession?.userId) {
      userId = customSession.userId
      sessionSource = "custom"
    }
    
    if (!userId) {
      return NextResponse.json({
        error: "No session found",
        sessionSource: "none"
      }, { status: 401 })
    }
    
    // Get fresh user data from database
    const user = await getUserById(userId)
    
    if (!user) {
      return NextResponse.json({
        error: "User not found in database",
        userId,
        sessionSource
      }, { status: 404 })
    }
    
    return NextResponse.json({
      userId: user.id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified,
      emailVerificationToken: user.emailVerificationToken ? "present" : "null",
      emailVerificationExpires: user.emailVerificationExpires,
      sessionSource,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error("Verification status debug error:", error)
    return NextResponse.json({
      error: "Failed to check verification status",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
