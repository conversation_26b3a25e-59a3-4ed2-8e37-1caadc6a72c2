// Edge Runtime compatible database utilities for middleware
// This file contains only the essential database functions needed by middleware
// without importing uploadthing or other Node.js-specific dependencies

import { neon } from '@neondatabase/serverless'

// Initialize database connection
const sql = neon(process.env.DATABASE_URL!)

export interface User {
  id: string
  email: string
  name: string
  role: string
  subscriptionPlan: string
  emailVerified: boolean
  deletedAt: Date | null
  deletionRequestedAt: Date | null
  deletionReason: string | null
  deletionToken: string | null
  deletionTokenExpires: Date | null
  lastLoginAt: Date | null
  createdAt: Date
  updatedAt: Date
}

/**
 * Get user by ID - Edge Runtime compatible version
 * Used by middleware for authentication checks
 */
export async function getUserById(userId: string): Promise<User | null> {
  try {
    const result = await sql`
      SELECT 
        id,
        email,
        name,
        role,
        "subscriptionPlan",
        "emailVerified",
        "deletedAt",
        "deletionRequestedAt",
        "deletionReason",
        "deletionToken",
        "deletionTokenExpires",
        "lastLoginAt",
        "createdAt",
        "updatedAt"
      FROM "User" 
      WHERE id = ${userId}
      LIMIT 1
    `
    
    if (result.length === 0) {
      return null
    }

    const user = result[0]
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role || 'individual_user',
      subscriptionPlan: user.subscriptionPlan || 'individual_free',
      emailVerified: user.emailVerified || false,
      deletedAt: user.deletedAt,
      deletionRequestedAt: user.deletionRequestedAt,
      deletionReason: user.deletionReason,
      deletionToken: user.deletionToken,
      deletionTokenExpires: user.deletionTokenExpires,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  } catch (error) {
    console.error('Error fetching user by ID:', error)
    return null
  }
}

/**
 * Get user by email - Edge Runtime compatible version
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await sql`
      SELECT 
        id,
        email,
        name,
        role,
        "subscriptionPlan",
        "emailVerified",
        "deletedAt",
        "deletionRequestedAt",
        "deletionReason",
        "deletionToken",
        "deletionTokenExpires",
        "lastLoginAt",
        "createdAt",
        "updatedAt"
      FROM "User" 
      WHERE email = ${email.toLowerCase()}
      LIMIT 1
    `
    
    if (result.length === 0) {
      return null
    }

    const user = result[0]
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role || 'individual_user',
      subscriptionPlan: user.subscriptionPlan || 'individual_free',
      emailVerified: user.emailVerified || false,
      deletedAt: user.deletedAt,
      deletionRequestedAt: user.deletionRequestedAt,
      deletionReason: user.deletionReason,
      deletionToken: user.deletionToken,
      deletionTokenExpires: user.deletionTokenExpires,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  } catch (error) {
    console.error('Error fetching user by email:', error)
    return null
  }
}
