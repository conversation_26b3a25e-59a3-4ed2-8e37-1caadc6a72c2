# RBAC and Multi-Tenant Implementation Guide

This guide shows how to implement Role-Based Access Control (RBAC) and multi-tenant functionality using the authentication system we've designed.

## User Types and Subscription Plans

### Individual Users (Maritime Professionals)
- **Role**: `INDIVIDUAL_USER`
- **Industries**: Yachting, commercial shipping, offshore, cruise, etc.
- **Subscription Plans**:
  - `individual_free` - Basic certificate storage
  - `individual_basic` - Certificate management with deletion
  - `individual_premium` - Advanced features, analytics, bulk operations
  - `individual_professional` - Full feature set including API access

### Certification Providers
- **Role**: `CERT_PROVIDER`
- **Purpose**: Organizations that issue and manage certificates
- **Subscription Plans**:
  - `provider_starter` - Basic certificate issuance
  - `provider_professional` - User management, analytics
  - `provider_enterprise` - Full feature set, API access

### Yacht Companies
- **Role**: `YACHT_COMPANY`
- **Purpose**: Businesses verifying employee certificates
- **Subscription Plans**:
  - `company_basic` - View employee certificates
  - `company_professional` - Analytics and reporting
  - `company_enterprise` - Advanced reporting
  - `company_fleet` - Full feature set for large fleets

### System Administrators
- **Role**: `SYSTEM_ADMIN`
- **Subscription**: No subscription plans (full access)

## Current State vs Future State

### Current Implementation
- ✅ Simple session-based authentication
- ✅ Basic route protection (authenticated/unauthenticated)
- ✅ Extensible configuration system ready for RBAC
- ✅ Backward-compatible session handling for existing users
- ✅ Role-specific subscription plan architecture

### Future Implementation (When Ready)
- 🔄 Role-based route access
- 🔄 Permission-based feature gating
- 🔄 Subscription plan restrictions per user type
- 🔄 Multi-tenant data isolation

## Implementation Steps

### Step 1: Database Schema Updates

```sql
-- Add role and subscription columns to User table
ALTER TABLE "User" ADD COLUMN "role" TEXT DEFAULT 'individual_user';
ALTER TABLE "User" ADD COLUMN "subscriptionPlan" TEXT DEFAULT 'individual_free';
ALTER TABLE "User" ADD COLUMN "tenantId" TEXT;
ALTER TABLE "User" ADD COLUMN "tenantRole" TEXT;

-- Create Tenant table for multi-tenant support
CREATE TABLE "Tenant" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "type" TEXT NOT NULL, -- 'cert_provider' | 'yacht_company'
  "subscriptionPlan" TEXT DEFAULT 'basic',
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create UserPermissions table for granular permissions
CREATE TABLE "UserPermission" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "permission" TEXT NOT NULL,
  "grantedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("userId") REFERENCES "User"("id")
);
```

### Step 2: Update Session Creation

```typescript
// In app/api/login/route.ts
import { createSession } from "@/lib/session"
import { UserRole, SubscriptionPlan } from "@/lib/auth-config"

// After successful login
await createSession({
  id: user.id,
  email: user.email,
  name: user.name,
  role: user.role as UserRole,
  subscriptionPlan: user.subscriptionPlan as SubscriptionPlan,
  tenantId: user.tenantId,
  tenantRole: user.tenantRole
})
```

### Step 3: Enable RBAC in Middleware

```typescript
// In middleware.ts - uncomment and implement
import { validateRouteAccess } from "@/lib/session"

// Replace the basic auth check with:
if (sessionContext && !validateRouteAccess(sessionContext, routeConfig)) {
  return NextResponse.redirect(new URL("/unauthorized", request.url))
}
```

### Step 4: Component-Level Permission Checks

```tsx
// Example: Certificate management component
import { usePermissions, useRole } from "@/hooks/use-session"
import { Permission } from "@/lib/auth-config"

function CertificateActions({ certificateId }: { certificateId: string }) {
  const { hasPermission } = usePermissions()
  const { isIndividualUser, isCertProvider, isYachtCompany } = useRole()

  return (
    <div>
      {hasPermission(Permission.CERTIFICATES_READ) && (
        <ViewButton certificateId={certificateId} />
      )}

      {hasPermission(Permission.CERTIFICATES_WRITE) && (
        <EditButton certificateId={certificateId} />
      )}

      {hasPermission(Permission.CERTIFICATES_DELETE) && (
        <DeleteButton certificateId={certificateId} />
      )}

      {hasPermission(Permission.CERTIFICATES_BULK_OPERATIONS) && (
        <BulkActionsMenu />
      )}

      {isCertProvider && (
        <IssueNewCertificateButton />
      )}

      {isYachtCompany && (
        <ViewEmployeeCertificatesButton />
      )}

      {isIndividualUser && (
        <PersonalCertificateUploadButton />
      )}
    </div>
  )
}
```

### Step 5: API Route Protection

```typescript
// Example: Protected API route
import { getServerSession, hasPermission } from "@/lib/session"
import { Permission } from "@/lib/auth-config"

export async function DELETE(request: Request) {
  const session = await getServerSession()

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  if (!hasPermission(session, Permission.CERTIFICATES_DELETE)) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 })
  }

  // Proceed with deletion
}
```

### Step 6: Multi-Tenant Data Isolation

```typescript
// Example: Tenant-aware database queries
import { getServerSession } from "@/lib/session"

export async function getCertificates() {
  const session = await getServerSession()

  if (!session) throw new Error("Unauthorized")

  // For individual users (yachties)
  if (session.role === UserRole.YACHTIE) {
    return db.select().from(certificates)
      .where(eq(certificates.userId, session.userId))
  }

  // For certification providers
  if (session.role === UserRole.CERT_PROVIDER && session.tenantId) {
    return db.select().from(certificates)
      .where(eq(certificates.issuerId, session.tenantId))
  }

  // For yacht companies
  if (session.role === UserRole.YACHT_COMPANY && session.tenantId) {
    return db.select().from(certificates)
      .innerJoin(employees, eq(certificates.userId, employees.userId))
      .where(eq(employees.companyId, session.tenantId))
  }

  throw new Error("Forbidden")
}
```

## Route Examples for Different User Types

### Individual User Routes (Maritime Professionals)
- `/dashboard` - Personal certificate overview
- `/certificates` - Manage personal certificates
- `/profile` - Personal profile settings
- `/analytics` - Personal certificate analytics (premium plans)

### Certification Provider Routes
- `/admin/provider/dashboard` - Provider dashboard
- `/admin/provider/certificates` - Manage issued certificates
- `/admin/provider/users` - Manage certified users
- `/admin/provider/analytics` - Certification analytics

### Yacht Company Routes
- `/admin/company/dashboard` - Company dashboard
- `/admin/company/employees` - View employee certificates
- `/admin/company/compliance` - Compliance reporting
- `/admin/company/analytics` - Employee certification analytics

### System Admin Routes
- `/admin/system/dashboard` - System overview
- `/admin/system/tenants` - Manage all tenants
- `/admin/system/users` - Manage all users
- `/admin/system/analytics` - Platform analytics

## Migration Strategy

1. **Phase 1**: Update database schema with new columns (default values for existing users)
2. **Phase 2**: Update session creation to include role/plan data
3. **Phase 3**: Enable RBAC validation in middleware
4. **Phase 4**: Add component-level permission checks
5. **Phase 5**: Implement tenant-specific routes and data isolation
6. **Phase 6**: Add subscription plan enforcement

## Testing RBAC Implementation

```typescript
// Example test cases
describe('RBAC System', () => {
  it('should allow individual users to access their own certificates', async () => {
    const session = createMockSession({ role: UserRole.INDIVIDUAL_USER })
    const access = validateRouteAccess(session, {
      requiresAuth: true,
      requiredPermissions: [Permission.CERTIFICATES_READ]
    })
    expect(access).toBe(true)
  })

  it('should deny access to admin routes for regular users', async () => {
    const session = createMockSession({ role: UserRole.INDIVIDUAL_USER })
    const access = validateRouteAccess(session, {
      requiresAuth: true,
      allowedRoles: [UserRole.SYSTEM_ADMIN]
    })
    expect(access).toBe(false)
  })

  it('should handle legacy role mapping for existing sessions', async () => {
    // Test that existing sessions with legacy roles are migrated
    const legacySessionData = { user: { role: 'yachtie', id: '123', email: '<EMAIL>', name: 'Test' } }
    const migratedSession = migrateSession(legacySessionData)
    expect(migratedSession.user.role).toBe(UserRole.INDIVIDUAL_USER)
  })
})
```

This architecture provides a clear path from your current simple authentication to a full RBAC and multi-tenant system while maintaining backward compatibility.
