"use client";

import type React from "react";

import { Anchor } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  formatVerificationMessage,
  getOAuthProviders,
  handleAuthResponse,
  handleOAuthLogin,
} from "@/lib/auth-utils";
import { logger } from "@/lib/logger";

export default function LoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSSOLoading, setIsSSOLoading] = useState<string | null>(null);
  const [error, setError] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [verificationMessage, setVerificationMessage] = useState("");

  // Check which OAuth providers are available
  const { isGoogleEnabled, isFacebookEnabled, hasOAuthProviders } =
    getOAuthProviders();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setVerificationMessage("");

    const endTimer = logger.authStart("user-login", email);
    logger.info("auth", "Login attempt started", {
      email: logger.isLoggingEnabled()
        ? email.substring(0, 3) + "***"
        : undefined,
    });

    try {
      // Use our custom login API endpoint
      const apiEndTimer = logger.apiRequest("POST", "/api/login");

      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
        }),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/login", response.status);

      // Use enhanced error handling
      const result = await handleAuthResponse(response);

      if (result.success) {
        logger.authSuccess(
          "user-login",
          result.data?.user?.id || "unknown-user"
        );

        // Check for callbackUrl in search params
        const searchParams = new URLSearchParams(window.location.search);
        const callbackUrl = searchParams.get("callbackUrl");

        let redirectPath = result.data?.redirectUrl || "/dashboard"; // Use API-provided redirect or default

        // If there's a valid callbackUrl, use it (for admin access and other protected routes)
        if (callbackUrl && callbackUrl.startsWith("/")) {
          redirectPath = decodeURIComponent(callbackUrl);
          logger.info("auth", "Login successful, redirecting to callback URL", {
            userId: result.data?.user?.id,
            callbackUrl: redirectPath,
          });
        } else {
          logger.info(
            "auth",
            "Login successful, redirecting based on user role",
            {
              userId: result.data?.user?.id,
              userRole: result.data?.user?.role,
              redirectPath,
            }
          );
        }

        // Add small delay to ensure session is properly set
        setTimeout(() => {
          logger.navigationStart("/login", redirectPath, result.data?.user?.id);
          router.push(redirectPath);
        }, 100);
      } else {
        // Handle email verification required
        if (response.status === 403 && result.data?.requiresVerification) {
          logger.info("auth", "Login blocked - email verification required", {
            email: email.substring(0, 3) + "***",
          });

          setVerificationMessage(
            formatVerificationMessage(result.data?.userEmail || email, false)
          );

          // Redirect after showing the message (reduced delay for better UX)
          setTimeout(() => {
            router.push("/verification-pending");
          }, 2000);
          return;
        }

        logger.authFailure(
          "user-login",
          result.error || "Invalid credentials",
          email
        );
        setError(result.error || "Invalid email or password");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("auth", "Login request failed", {
        error: errorMessage,
        email: email.substring(0, 3) + "***",
      });
      setError("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  const handleSSOLogin = async (provider: string) => {
    await handleOAuthLogin(provider, setIsSSOLoading, setError, "/dashboard");
  };

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <Anchor className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            Login to Sealog
          </CardTitle>
          <CardDescription className="text-center">
            Enter your email and password to access your account
          </CardDescription>
        </CardHeader>

        {/* SSO Login Options - Only show if OAuth providers are available */}
        {hasOAuthProviders && (
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {isGoogleEnabled && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSSOLogin("google")}
                  disabled={isSSOLoading === "google"}
                >
                  {isSSOLoading === "google"
                    ? "Connecting..."
                    : "Continue with Google"}
                </Button>
              )}
              {isFacebookEnabled && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSSOLogin("facebook")}
                  disabled={isSSOLoading === "facebook"}
                >
                  {isSSOLoading === "facebook"
                    ? "Connecting..."
                    : "Continue with Facebook"}
                </Button>
              )}
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with email
                </span>
              </div>
            </div>
          </CardContent>
        )}

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4 pt-0">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            {verificationMessage && (
              <Alert variant="default" className="border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  {verificationMessage}
                </AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link
                  href={`/forgot-password${
                    email ? `?email=${encodeURIComponent(email)}` : ""
                  }`}
                  className="text-sm text-primary hover:underline"
                >
                  Forgot Password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? "Logging in..." : "Login to Sealog"}
            </Button>
            <div className="text-center text-sm">
              New to Sealog?{" "}
              <Link href="/signup" className="text-primary hover:underline">
                Sign Up
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
