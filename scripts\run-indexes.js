// Script to run performance indexes
const { neon } = require("@neondatabase/serverless");
const fs = require("fs");
const path = require("path");

async function runIndexes() {
  try {
    // Load environment variables
    require("dotenv").config({ path: ".env.local" });

    if (!process.env.DATABASE_URL) {
      console.error("DATABASE_URL not found in environment variables");
      process.exit(1);
    }

    const sql = neon(process.env.DATABASE_URL);

    // Read the SQL file
    const sqlFile = path.join(__dirname, "add-performance-indexes.sql");
    const sqlContent = fs.readFileSync(sqlFile, "utf8");

    // Split by semicolon and execute each statement
    const statements = sqlContent
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

    console.log(`Executing ${statements.length} SQL statements...`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.toLowerCase().includes("commit")) {
        continue; // Skip COMMIT statements for serverless
      }

      try {
        console.log(
          `[${i + 1}/${statements.length}] Executing: ${statement.substring(
            0,
            50
          )}...`
        );
        await sql.unsafe(statement);
        console.log(`✅ Success`);
      } catch (error) {
        if (error.message.includes("already exists")) {
          console.log(`⚠️  Index already exists, skipping`);
        } else {
          console.error(`❌ Error: ${error.message}`);
        }
      }
    }

    console.log("\n🎉 Performance indexes setup completed!");
  } catch (error) {
    console.error("Failed to run indexes:", error);
    process.exit(1);
  }
}

runIndexes();
