-- Migration script for Account Deletion System
-- This script adds the necessary fields and tables for the account deletion functionality
-- Run this against your PostgreSQL database

-- 1. Add account deletion fields to User table
ALTER TABLE "User" 
ADD COLUMN IF NOT EXISTS "deletedAt" TIMESTAMP,
ADD COLUMN IF NOT EXISTS "deletionRequestedAt" TIMESTAMP,
ADD COLUMN IF NOT EXISTS "deletionReason" TEXT,
ADD COLUMN IF NOT EXISTS "deletionToken" TEXT,
ADD COLUMN IF NOT EXISTS "deletionTokenExpires" TIMESTAMP;

-- 2. Create AccountDeletionAudit table
CREATE TABLE IF NOT EXISTS "AccountDeletionAudit" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "userEmail" TEXT NOT NULL,
  "userName" TEXT NOT NULL,
  "userRole" TEXT NOT NULL,
  "deletionType" TEXT NOT NULL,
  "deletionReason" TEXT,
  "initiatedBy" TEXT NOT NULL,
  "initiatorId" TEXT,
  "dataRetained" TEXT,
  "dataDeleted" TEXT,
  "certificateCount" INTEGER DEFAULT 0 NOT NULL,
  "fileCount" INTEGER DEFAULT 0 NOT NULL,
  "ipAddress" TEXT,
  "userAgent" TEXT,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 3. Create CleanupJob table
CREATE TABLE IF NOT EXISTS "CleanupJob" (
  "id" TEXT PRIMARY KEY,
  "jobType" TEXT NOT NULL,
  "status" TEXT NOT NULL,
  "scheduledFor" TIMESTAMP NOT NULL,
  "startedAt" TIMESTAMP,
  "completedAt" TIMESTAMP,
  "recordsProcessed" INTEGER DEFAULT 0 NOT NULL,
  "recordsDeleted" INTEGER DEFAULT 0 NOT NULL,
  "errors" TEXT,
  "metadata" TEXT,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS "account_deletion_audit_user_id_idx" ON "AccountDeletionAudit"("userId");
CREATE INDEX IF NOT EXISTS "account_deletion_audit_type_idx" ON "AccountDeletionAudit"("deletionType");
CREATE INDEX IF NOT EXISTS "account_deletion_audit_created_at_idx" ON "AccountDeletionAudit"("createdAt");

CREATE INDEX IF NOT EXISTS "cleanup_job_type_idx" ON "CleanupJob"("jobType");
CREATE INDEX IF NOT EXISTS "cleanup_job_status_idx" ON "CleanupJob"("status");
CREATE INDEX IF NOT EXISTS "cleanup_job_scheduled_for_idx" ON "CleanupJob"("scheduledFor");

-- 5. Create indexes on User table for deletion queries
CREATE INDEX IF NOT EXISTS "user_deleted_at_idx" ON "User"("deletedAt");
CREATE INDEX IF NOT EXISTS "user_deletion_requested_at_idx" ON "User"("deletionRequestedAt");
CREATE INDEX IF NOT EXISTS "user_deletion_token_idx" ON "User"("deletionToken");

-- 6. Add constraints and validation
-- Ensure deletion types are valid
ALTER TABLE "AccountDeletionAudit" 
ADD CONSTRAINT IF NOT EXISTS "check_deletion_type" 
CHECK ("deletionType" IN ('soft', 'hard', 'recovered'));

-- Ensure job types are valid
ALTER TABLE "CleanupJob" 
ADD CONSTRAINT IF NOT EXISTS "check_job_type" 
CHECK ("jobType" IN ('account_deletion', 'file_cleanup', 'data_cleanup'));

-- Ensure job status is valid
ALTER TABLE "CleanupJob" 
ADD CONSTRAINT IF NOT EXISTS "check_job_status" 
CHECK ("status" IN ('pending', 'running', 'completed', 'failed'));

-- Ensure initiatedBy is valid
ALTER TABLE "AccountDeletionAudit" 
ADD CONSTRAINT IF NOT EXISTS "check_initiated_by" 
CHECK ("initiatedBy" IN ('user', 'admin', 'system'));

-- 7. Create a function to update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Create trigger for CleanupJob updatedAt
DROP TRIGGER IF EXISTS update_cleanup_job_updated_at ON "CleanupJob";
CREATE TRIGGER update_cleanup_job_updated_at
    BEFORE UPDATE ON "CleanupJob"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 9. Create a view for easy querying of deletion statistics
CREATE OR REPLACE VIEW "DeletionStatistics" AS
SELECT 
    DATE_TRUNC('day', "createdAt") as "date",
    "deletionType",
    COUNT(*) as "count",
    SUM("certificateCount") as "totalCertificates",
    SUM("fileCount") as "totalFiles"
FROM "AccountDeletionAudit"
GROUP BY DATE_TRUNC('day', "createdAt"), "deletionType"
ORDER BY "date" DESC;

-- 10. Create a view for users eligible for hard deletion
CREATE OR REPLACE VIEW "UsersEligibleForHardDeletion" AS
SELECT 
    "id",
    "email",
    "name",
    "role",
    "deletedAt",
    EXTRACT(DAYS FROM (CURRENT_TIMESTAMP - "deletedAt")) as "daysSinceDeletion"
FROM "User"
WHERE "deletedAt" IS NOT NULL 
  AND "deletedAt" <= CURRENT_TIMESTAMP - INTERVAL '30 days';

-- 11. Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON "AccountDeletionAudit" TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON "CleanupJob" TO your_app_user;
-- GRANT SELECT ON "DeletionStatistics" TO your_app_user;
-- GRANT SELECT ON "UsersEligibleForHardDeletion" TO your_app_user;

-- 12. Insert initial cleanup job for testing (optional)
-- INSERT INTO "CleanupJob" (
--   "id",
--   "jobType",
--   "status",
--   "scheduledFor",
--   "metadata",
--   "updatedAt"
-- ) VALUES (
--   'initial-cleanup-job',
--   'account_deletion',
--   'pending',
--   CURRENT_TIMESTAMP + INTERVAL '1 day',
--   '{"description": "Initial cleanup job for testing"}',
--   CURRENT_TIMESTAMP
-- );

-- Migration completed successfully
-- Remember to:
-- 1. Update your environment variables with CRON_SECRET
-- 2. Set up the cron job to run scripts/cleanup-deleted-accounts.js
-- 3. Test the account deletion flow in a development environment
-- 4. Configure email templates and SMTP settings
-- 5. Review and adjust the 30-day retention period if needed
