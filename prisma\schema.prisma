generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String   @id @default(cuid())
  name             String
  email            String   @unique
  password         String?  // Optional for SSO users
  role             String   @default("individual_user")
  subscriptionPlan String   @default("individual_free")
  tenantId         String?
  tenantRole       String?
  emailVerified    Boolean  @default(false)
  emailVerificationToken String?
  emailVerificationExpires DateTime?
  twoFactorEnabled Boolean  @default(false)
  twoFactorSecret  String?
  lastLoginAt      DateTime?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  certificates     Certificate[]
  socialAccounts   SocialAccount[]
  emailVerifications EmailVerification[]
  passwordResets   PasswordReset[]
}

model SocialAccount {
  id                String   @id @default(cuid())
  userId            String
  provider          String   // "google", "facebook", "apple"
  providerAccountId String
  accessToken       String?
  refreshToken      String?
  expiresAt         DateTime?
  tokenType         String?
  scope             String?
  idToken           String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model EmailVerification {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model PasswordReset {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Certificate {
  id                String   @id @default(cuid())
  name              String
  issuingAuthority  String
  certificateNumber String
  dateIssued        DateTime
  expiryDate        DateTime?
  documentUrl       String?
  notes             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
