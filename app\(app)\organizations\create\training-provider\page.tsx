"use client";

import { <PERSON><PERSON><PERSON><PERSON>, GraduationCap } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { handleApiError } from "@/lib/auth-utils";

export default function CreateTrainingProviderPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    location: "",
    accreditations: "",
    specializations: "",
    website: "",
    contactPhone: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    // Validate required fields
    if (!formData.name.trim()) {
      setError("Training provider name is required");
      setIsLoading(false);
      return;
    }

    try {
      const requestBody = {
        name: formData.name.trim(),
        type: "cert_provider",
        description: formData.description.trim() || undefined,
        website: formData.website.trim() || undefined,
        providerDetails: {
          location: formData.location.trim() || undefined,
          accreditations: formData.accreditations.trim() || undefined,
          specializations: formData.specializations.trim() || undefined,
          contactPhone: formData.contactPhone.trim() || undefined,
        },
      };

      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Something went wrong");
      }

      setSuccessMessage(
        "Training provider created successfully! You can now start creating courses and managing students."
      );

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/organizations/new">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization Types
          </Link>
        </Button>
      </div>

      <div className="max-w-3xl mx-auto">
        {/* Page Header */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <GraduationCap className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Create Training Provider
          </h1>
          <p className="text-muted-foreground text-lg">
            Set up your maritime training and certification organization
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">
              Training Provider Information
            </CardTitle>
            <CardDescription className="text-center">
              Provide details about your training organization and courses
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {successMessage && (
                <Alert
                  variant="default"
                  className="border-green-200 bg-green-50"
                >
                  <AlertDescription className="text-green-800">
                    {successMessage}
                  </AlertDescription>
                </Alert>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="name">Training Provider Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Enter your training provider name"
                    required
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Brief description of your training organization, courses offered, and expertise..."
                    rows={3}
                    value={formData.description}
                    onChange={handleChange}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      name="location"
                      placeholder="e.g., Southampton, UK"
                      value={formData.location}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input
                      id="contactPhone"
                      name="contactPhone"
                      type="tel"
                      placeholder="e.g., +44 20 1234 5678"
                      value={formData.contactPhone}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>

              {/* Training Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Training Details</h3>

                <div className="space-y-2">
                  <Label htmlFor="accreditations">
                    Accreditations & Certifications
                  </Label>
                  <Textarea
                    id="accreditations"
                    name="accreditations"
                    placeholder="List your accrediting bodies, certifications, and approvals (e.g., MCA, STCW, etc.)"
                    rows={2}
                    value={formData.accreditations}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="specializations">
                    Course Specializations
                  </Label>
                  <Textarea
                    id="specializations"
                    name="specializations"
                    placeholder="Describe your course specializations and areas of expertise (e.g., STCW, Yacht training, Engineering, etc.)"
                    rows={2}
                    value={formData.specializations}
                    onChange={handleChange}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="website">Website (Optional)</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    placeholder="https://www.trainingprovider.com"
                    value={formData.website}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <Alert variant="default" className="border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  Your training provider will be created immediately. You can
                  add course catalogs, facility details, and instructor profiles
                  after creation to attract more students.
                </AlertDescription>
              </Alert>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                className="w-full"
                type="submit"
                disabled={isLoading || !!successMessage}
              >
                {isLoading
                  ? "Creating Training Provider..."
                  : successMessage
                  ? "Training Provider Created!"
                  : "Create Training Provider"}
              </Button>

              <div className="text-center text-sm">
                <Link
                  href="/organizations/new"
                  className="text-muted-foreground hover:text-primary"
                >
                  Cancel and choose different type
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
