import { NextRequest, NextResponse } from "next/server";
import { validateEnvironment, getEnvironmentHealth, generateEnvironmentTemplate } from "@/lib/environment-validation";
import { handleApiError, AuthenticationError, AuthorizationError } from "@/lib/api-error-handler";
import { getServerSession } from "next-auth";
import { logger } from "@/lib/logger";

/**
 * Get environment validation status (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      throw new AuthenticationError();
    }
    
    // Check if user is admin (for now, just check if user exists - implement proper RBAC later)
    if (!session.user) {
      throw new AuthorizationError("Admin access required");
    }
    
    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action") || "validate";
    
    logger.info("admin", "Environment data requested", {
      userId: session.user.id,
      action,
    });
    
    let data;
    
    switch (action) {
      case "health":
        data = getEnvironmentHealth();
        break;
        
      case "template":
        const template = generateEnvironmentTemplate();
        return new NextResponse(template, {
          headers: {
            "Content-Type": "text/plain",
            "Content-Disposition": "attachment; filename=.env.template",
          },
        });
        
      case "validate":
      default:
        data = validateEnvironment();
        break;
    }
    
    return NextResponse.json({
      success: true,
      action,
      timestamp: new Date().toISOString(),
      data,
    });
    
  } catch (error) {
    return handleApiError(error, "admin-environment");
  }
}
