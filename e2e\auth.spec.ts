import { expect, test } from '@playwright/test';

test.describe('Authentication Flow - Core Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication
    await page.context().clearCookies();
    await page.context().clearPermissions();
  });

  test('should complete full authentication flow from landing page to dashboard', async ({ page }) => {
    // Start from landing page (correct user flow)
    await page.goto('/');

    // Should show landing page
    await expect(page.locator('h1')).toContainText('Sealog');

    // Check if we're on mobile (mobile menu button visible) or desktop
    const mobileMenuButton = page.locator('button').filter({ has: page.locator('svg') }).filter({ hasText: /toggle menu/i });
    const isMobile = await mobileMenuButton.isVisible();

    if (isMobile) {
      // Mobile: open menu first
      await mobileMenuButton.click();
      await page.waitForTimeout(1000); // Wait for menu animation

      // Now click the login link in the mobile menu
      await page.locator('nav a[href="/login"]').click();
    } else {
      // Desktop: click the login button directly
      await page.locator('a[href="/login"]').first().click();
    }
    await expect(page).toHaveURL('/login');

    // Check login form is present
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'AutoTest123!');
    await page.click('button[type="submit"]');

    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard', { timeout: 15000 });

    // Should show authenticated user content
    await expect(page.locator('text=Auto Test User')).toBeVisible();
    console.log('✅ Complete authentication flow successful');
  });

  test('should redirect unauthenticated users from protected routes', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/dashboard');

    // Should redirect to login (with callback URL)
    await expect(page).toHaveURL(/\/login/);

    // Try certificates page
    await page.goto('/certificates');
    await expect(page).toHaveURL(/\/login/);

    console.log('✅ Protected route redirection working');
  });

  test('should handle signup flow', async ({ page }) => {
    await page.goto('/signup');

    // Check signup form is present
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    console.log('✅ Signup page loads correctly');
  });

  test('should reject invalid login credentials with proper error messages', async ({ page }) => {
    await page.goto('/login');

    // Test 1: Invalid email format
    await page.fill('input[type="email"]', 'invalid-email');
    await page.fill('input[type="password"]', 'somepassword');
    await page.click('button[type="submit"]');

    // Browser validation should prevent submission
    const emailField = page.locator('input[type="email"]');
    const isEmailInvalid = await emailField.evaluate((el: HTMLInputElement) => !el.validity.valid);
    if (isEmailInvalid) {
      console.log('✅ Invalid email format rejected by browser validation');
    }

    // Test 2: Valid email format but non-existent account
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');

    // Wait for API response
    await page.waitForTimeout(3000);

    // Should still be on login page (not redirected)
    await expect(page).toHaveURL(/\/login/);

    // Check for specific error message (exclude Next.js route announcer)
    const errorAlert = page.locator('[role="alert"]').filter({ hasText: /Invalid|error|failed/i }).first();
    await expect(errorAlert).toBeVisible();

    // Verify error message content
    const errorText = await errorAlert.textContent();
    expect(errorText).toContain('Invalid');
    console.log('✅ Proper error message displayed for invalid credentials:', errorText);

    // Test 3: Empty fields
    await page.fill('input[type="email"]', '');
    await page.fill('input[type="password"]', '');
    await page.click('button[type="submit"]');

    // Browser validation should prevent submission
    const isEmailRequired = await emailField.evaluate((el: HTMLInputElement) => !el.validity.valid);
    if (isEmailRequired) {
      console.log('✅ Empty email field rejected by browser validation');
    }

    console.log('✅ Comprehensive login validation working correctly');
  });

  test('should validate signup form with comprehensive error handling', async ({ page }) => {
    await page.goto('/signup');

    // Test 1: Empty form submission
    await page.click('button[type="submit"]');

    // Browser validation should prevent submission
    const nameField = page.locator('input[name="name"]');
    const isNameRequired = await nameField.evaluate((el: HTMLInputElement) => !el.validity.valid);
    if (isNameRequired) {
      console.log('✅ Empty name field rejected by browser validation');
    }

    // Test 2: Invalid email format
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[type="email"]', 'invalid-email');
    await page.fill('input[name="password"]', 'ValidPassword123!');
    await page.fill('input[name="confirmPassword"]', 'ValidPassword123!');
    await page.click('button[type="submit"]');

    const emailField = page.locator('input[type="email"]');
    const isEmailInvalid = await emailField.evaluate((el: HTMLInputElement) => !el.validity.valid);
    if (isEmailInvalid) {
      console.log('✅ Invalid email format rejected by browser validation');
    }

    // Test 3: Password mismatch
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'StrongPassword123!');
    await page.fill('input[name="confirmPassword"]', 'DifferentPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);

    // Check for mismatch error
    const mismatchError = page.locator('[role="alert"]').filter({ hasText: 'Passwords do not match' });
    await expect(mismatchError).toBeVisible();
    console.log('✅ Password mismatch error displayed correctly');

    // Test 4: Existing email (using autotest email)
    await page.fill('input[name="password"]', 'ValidPassword123!');
    await page.fill('input[name="confirmPassword"]', 'ValidPassword123!');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);

    // Should show error about existing account
    const existingAccountError = page.locator('[role="alert"]').filter({ hasText: /account|email|exists/i }).first();
    await expect(existingAccountError).toBeVisible();
    const errorText = await existingAccountError.textContent();
    console.log('✅ Existing account error displayed:', errorText);

    // Test 5: Valid form (but don't actually create account)
    await page.fill('input[type="email"]', '<EMAIL>');
    // Note: We won't submit this to avoid creating test accounts

    console.log('✅ Comprehensive signup validation working correctly');
  });

  test('should handle email verification flow correctly', async ({ page }) => {
    // Test the verification-pending page directly
    await page.goto('/verification-pending');

    // Should redirect to login if no session, or show verification page if session exists
    // Wait for either login redirect or verification page to load
    await page.waitForTimeout(2000);

    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      console.log('✅ Verification page redirects to login when no session');
    } else if (currentUrl.includes('/verification-pending')) {
      // Should show verification pending page
      await expect(page.locator('h1, h2')).toContainText(/check.*email|verify|verification/i);
      console.log('✅ Email verification page loads correctly');

      // Test resend verification functionality if available
      const resendButton = page.locator('button').filter({ hasText: /resend/i });
      if (await resendButton.isVisible()) {
        console.log('✅ Resend verification button is available');
      }
    }

    console.log('✅ Email verification flow accessible');
  });

  test('should handle authentication edge cases', async ({ page }) => {
    // Test 1: Direct access to protected routes
    await page.goto('/dashboard');
    await expect(page).toHaveURL(/\/login/);
    console.log('✅ Protected route redirects to login');

    // Test 2: Login page when already authenticated
    // First login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'AutoTest123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 });

    // Then try to access login page
    await page.goto('/login');
    // Should redirect to dashboard since already authenticated
    await expect(page).toHaveURL('/dashboard');
    console.log('✅ Login page redirects authenticated users to dashboard');

    // Test 3: Logout functionality (if available)
    const logoutButton = page.locator('button, a').filter({ hasText: /logout|sign out/i });
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      await page.waitForTimeout(1000);
      // Should redirect to login or landing page
      const currentUrl = page.url();
      expect(currentUrl).toMatch(/\/(login|$)/);
      console.log('✅ Logout functionality working');
    }

    console.log('✅ Authentication edge cases handled correctly');
  });
});
