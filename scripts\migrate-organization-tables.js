#!/usr/bin/env node

/**
 * Database migration script to create Organization and OrganizationMembership tables
 * for Phase 5 Multi-Tenant RBAC implementation
 */

const { neon } = require("@neondatabase/serverless");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config({ path: ".env.local" });

async function migrateOrganizationTables() {
  console.log("🚀 Starting Organization tables migration...");
  console.log("📅 Date:", new Date().toISOString());

  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  const sql = neon(process.env.DATABASE_URL);

  try {
    // Create Organization table
    console.log("📝 Creating Organization table...");
    await sql`
      CREATE TABLE IF NOT EXISTS "Organization" (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL, -- 'yacht_company' | 'cert_provider'
        status TEXT NOT NULL DEFAULT 'pending', -- 'pending' | 'verified' | 'suspended'
        "contactEmail" TEXT NOT NULL,
        description TEXT,
        website TEXT,
        "verifiedAt" TIMESTAMP,
        "verifiedBy" TEXT, -- Admin user ID who verified
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    console.log("✅ Created Organization table");

    // Create Organization indexes
    console.log("📝 Creating Organization indexes...");
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_name_idx" ON "Organization"(name)
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_type_idx" ON "Organization"(type)
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_status_idx" ON "Organization"(status)
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_created_at_idx" ON "Organization"("createdAt")
    `;
    console.log("✅ Created Organization indexes");

    // Create OrganizationMembership table
    console.log("📝 Creating OrganizationMembership table...");
    await sql`
      CREATE TABLE IF NOT EXISTS "OrganizationMembership" (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        "organizationId" TEXT NOT NULL REFERENCES "Organization"(id) ON DELETE CASCADE,
        role TEXT NOT NULL DEFAULT 'member', -- 'owner' | 'admin' | 'member'
        "joinedAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "invitedBy" TEXT, -- User ID who invited this member
        "invitedAt" TIMESTAMP,
        "acceptedAt" TIMESTAMP,
        status TEXT NOT NULL DEFAULT 'active', -- 'active' | 'suspended' | 'pending'
        UNIQUE("userId", "organizationId")
      )
    `;
    console.log("✅ Created OrganizationMembership table");

    // Create OrganizationMembership indexes
    console.log("📝 Creating OrganizationMembership indexes...");
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_membership_user_idx" ON "OrganizationMembership"("userId")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_membership_org_idx" ON "OrganizationMembership"("organizationId")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_membership_role_idx" ON "OrganizationMembership"(role)
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "organization_membership_status_idx" ON "OrganizationMembership"(status)
    `;
    console.log("✅ Created OrganizationMembership indexes");

    // Add organization-related columns to Certificate table
    console.log("📝 Adding organization columns to Certificate table...");
    await sql`
      ALTER TABLE "Certificate"
      ADD COLUMN IF NOT EXISTS "organizationId" TEXT REFERENCES "Organization"(id) ON DELETE CASCADE
    `;
    await sql`
      ALTER TABLE "Certificate"
      ADD COLUMN IF NOT EXISTS scope TEXT DEFAULT 'personal' NOT NULL
    `;
    console.log("✅ Added organization columns to Certificate table");

    // Create Certificate organization index
    await sql`
      CREATE INDEX IF NOT EXISTS "certificate_organization_idx" ON "Certificate"("organizationId")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "certificate_scope_idx" ON "Certificate"(scope)
    `;
    console.log("✅ Created Certificate organization indexes");

    console.log("🎉 Organization tables migration completed successfully!");
    console.log("");
    console.log("📋 Migration Summary:");
    console.log("   ✅ Created Organization table with indexes");
    console.log("   ✅ Created OrganizationMembership table with indexes");
    console.log("   ✅ Added organization columns to Certificate table");
    console.log("   ✅ Created all necessary indexes for performance");
    console.log("");
    console.log("🔍 Verifying tables...");

    // Verify tables were created
    const tables = await sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('Organization', 'OrganizationMembership')
    `;

    console.log(
      "📋 Created tables:",
      tables.map((t) => t.table_name)
    );

    if (tables.length === 2) {
      console.log("✅ All tables created successfully!");
    } else {
      console.log("⚠️  Some tables may not have been created properly");
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run migration
migrateOrganizationTables();
