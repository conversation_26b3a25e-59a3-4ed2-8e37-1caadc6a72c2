import { NextResponse } from "next/server"
import { cookies } from "next/headers"

/**
 * Clear the logout flag cookie
 * This is called after successful redirect to login page
 */
export async function POST() {
  try {
    const cookieStore = await cookies()
    
    // Clear the logout flag cookie
    cookieStore.delete("logout-flag")
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error clearing logout flag:", error)
    return NextResponse.json({ 
      success: false, 
      error: "Failed to clear logout flag" 
    }, { status: 500 })
  }
}
