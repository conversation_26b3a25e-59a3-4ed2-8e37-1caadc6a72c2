import { UserRole } from "@/lib/auth-config"
import {
  db,
  getUserById,
  organizationMemberships,
  organizations,
  users
} from "@/lib/db"
import { getServerSession } from "@/lib/session"
import { and, count, desc, eq, isNull } from "drizzle-orm"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Validation schemas
const updateUserSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  action: z.literal("update"),
  updates: z.object({
    role: z.enum(["individual_user", "system_admin"]).optional(), // Only valid user roles
    subscriptionPlan: z.string().optional(),
    emailVerified: z.boolean().optional(),
  })
})

const actionSchema = z.discriminatedUnion("action", [
  updateUserSchema
])

async function getUsersWithStats(limit = 50, offset = 0) {
  try {
    const usersData = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        role: users.role,
        subscriptionPlan: users.subscriptionPlan,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
      })
      .from(users)
      .where(isNull(users.deletedAt))
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset)

    // Get organization memberships for each user
    const usersWithOrgs = await Promise.all(
      usersData.map(async (user) => {
        const memberships = await db
          .select({
            organizationName: organizations.name,
            organizationType: organizations.type,
            membershipRole: organizationMemberships.role,
          })
          .from(organizationMemberships)
          .innerJoin(organizations, eq(organizationMemberships.organizationId, organizations.id))
          .where(eq(organizationMemberships.userId, user.id))
          .limit(3) // Limit to first 3 organizations

        return {
          ...user,
          organizations: memberships
        }
      })
    )

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(users)
      .where(isNull(users.deletedAt))

    const totalCount = Number(totalCountResult[0]?.count || 0)

    return {
      users: usersWithOrgs,
      totalCount,
      hasMore: offset + limit < totalCount
    }
  } catch (error) {
    console.error("Error fetching users with stats:", error)
    return {
      users: [],
      totalCount: 0,
      hasMore: false
    }
  }
}

async function getUserStats() {
  try {
    // Get total users count
    const totalUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(isNull(users.deletedAt))

    // Get users by role
    const individualUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          eq(users.role, "individual_user"),
          isNull(users.deletedAt)
        )
      )

    const businessUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          eq(users.role, "cert_provider"),
          isNull(users.deletedAt)
        )
      )

    const yachtCompanyUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          eq(users.role, "yacht_company"),
          isNull(users.deletedAt)
        )
      )

    const unverifiedUsersResult = await db
      .select({ count: count() })
      .from(users)
      .where(
        and(
          eq(users.emailVerified, false),
          isNull(users.deletedAt)
        )
      )

    return {
      total: Number(totalUsersResult[0]?.count || 0),
      individual: Number(individualUsersResult[0]?.count || 0),
      business: Number(businessUsersResult[0]?.count || 0) + Number(yachtCompanyUsersResult[0]?.count || 0),
      unverified: Number(unverifiedUsersResult[0]?.count || 0),
    }
  } catch (error) {
    console.error("Error fetching user stats:", error)
    return {
      total: 0,
      individual: 0,
      business: 0,
      unverified: 0,
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 50
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0
    const includeStats = searchParams.get("includeStats") === "true"

    const usersData = await getUsersWithStats(limit, offset)

    let stats = null
    if (includeStats) {
      stats = await getUserStats()
    }

    return NextResponse.json({
      success: true,
      ...usersData,
      stats,
      pagination: {
        limit,
        offset,
        total: usersData.totalCount,
        hasMore: usersData.hasMore
      }
    })

  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = actionSchema.parse(body)

    switch (validatedData.action) {
      case "update": {
        // Verify the user exists
        const user = await getUserById(validatedData.userId)
        if (!user) {
          return NextResponse.json(
            { error: "User not found" },
            { status: 404 }
          )
        }

        if (user.deletedAt) {
          return NextResponse.json(
            { error: "Cannot update deleted user" },
            { status: 400 }
          )
        }

        // Update the user
        await db
          .update(users)
          .set({
            ...validatedData.updates,
            updatedAt: new Date(),
          })
          .where(eq(users.id, validatedData.userId))

        return NextResponse.json({
          success: true,
          message: "User updated successfully"
        })
      }

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: error.errors
        },
        { status: 400 }
      )
    }

    console.error("Error processing user action:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
