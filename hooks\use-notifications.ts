/**
 * Notification State Management Hook
 *
 * This hook provides state management for notifications including:
 * - Fetching notifications from the API
 * - Real-time notification count updates
 * - Mark as read/unread functionality
 * - Notification deletion
 * - Auto-refresh capabilities
 */

import { useState, useEffect, useCallback } from "react"

export interface Notification {
  id: string
  userId: string
  type: 'certificate_expiry' | 'system' | 'reminder'
  title: string
  message: string
  read: boolean
  actionUrl?: string
  metadata?: any
  createdAt: string
  expiresAt?: string
}

interface UseNotificationsOptions {
  includeRead?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseNotificationsReturn {
  notifications: Notification[]
  unreadCount: number
  isLoading: boolean
  error: string | null
  markAsRead: (notificationId: string) => Promise<boolean>
  markAllAsRead: () => Promise<boolean>
  deleteNotification: (notificationId: string) => Promise<boolean>
  refresh: () => Promise<void>
}

export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const {
    includeRead = true,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options

  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    try {
      setError(null)
      
      const response = await fetch(`/api/notifications?includeRead=${includeRead}`, {
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.success) {
        setNotifications(data.notifications || [])
        
        // Calculate unread count
        const unread = (data.notifications || []).filter((n: Notification) => !n.read).length
        setUnreadCount(unread)
      } else {
        throw new Error(data.error || "Failed to fetch notifications")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error"
      setError(errorMessage)
      console.error("Error fetching notifications:", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [includeRead])

  // Fetch unread count only
  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await fetch("/api/notifications?countOnly=true", {
        credentials: "include",
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUnreadCount(data.count || 0)
        }
      }
    } catch (err) {
      console.error("Error fetching unread count:", err)
    }
  }, [])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const response = await fetch("/api/notifications", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          action: "mark_read",
          notificationId,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Update local state
          setNotifications(prev => 
            prev.map(n => 
              n.id === notificationId ? { ...n, read: true } : n
            )
          )
          setUnreadCount(prev => Math.max(0, prev - 1))
          return true
        }
      }
      return false
    } catch (err) {
      console.error("Error marking notification as read:", err)
      return false
    }
  }, [])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch("/api/notifications", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          action: "mark_all_read",
        }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Update local state
          setNotifications(prev => 
            prev.map(n => ({ ...n, read: true }))
          )
          setUnreadCount(0)
          return true
        }
      }
      return false
    } catch (err) {
      console.error("Error marking all notifications as read:", err)
      return false
    }
  }, [])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/notifications?id=${notificationId}`, {
        method: "DELETE",
        credentials: "include",
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Update local state
          const wasUnread = notifications.find(n => n.id === notificationId)?.read === false
          setNotifications(prev => prev.filter(n => n.id !== notificationId))
          if (wasUnread) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
          return true
        }
      }
      return false
    } catch (err) {
      console.error("Error deleting notification:", err)
      return false
    }
  }, [notifications])

  // Refresh notifications
  const refresh = useCallback(async () => {
    setIsLoading(true)
    await fetchNotifications()
  }, [fetchNotifications])

  // Initial fetch
  useEffect(() => {
    fetchNotifications()
  }, [fetchNotifications])

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      if (includeRead) {
        fetchNotifications()
      } else {
        fetchUnreadCount()
      }
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, includeRead, fetchNotifications, fetchUnreadCount])

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
  }
}
