import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const cookieStore = await cookies()

    // Clear the custom session cookie
    cookieStore.delete("session")

    // Clear NextAuth session cookies
    // NextAuth uses these cookie names by default
    cookieStore.delete("next-auth.session-token")
    cookieStore.delete("__Secure-next-auth.session-token") // For HTTPS
    cookieStore.delete("next-auth.csrf-token")
    cookieStore.delete("__Secure-next-auth.csrf-token") // For HTTPS
    cookieStore.delete("next-auth.callback-url")
    cookieStore.delete("__Secure-next-auth.callback-url") // For HTTPS

    // Also clear any potential NextAuth state cookies
    cookieStore.delete("next-auth.pkce.code_verifier")
    cookieStore.delete("__Secure-next-auth.pkce.code_verifier") // For HTTPS

    // Create a response with headers to ensure immediate logout
    const response = NextResponse.json({
      success: true,
      message: "Successfully logged out"
    })

    // Add headers to prevent caching and force immediate logout
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')

    return response
  } catch (error) {
    console.error("Logout error:", error)
    return NextResponse.json({
      success: false,
      error: "Logout failed"
    }, { status: 500 })
  }
}
