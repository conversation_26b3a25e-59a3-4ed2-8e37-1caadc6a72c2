import { NextResponse } from "next/server"
import { getServerSession } from "@/lib/session"
import { unlinkSocialAccount } from "@/lib/db"

/**
 * Unlink a social account from the current user
 */
export async function POST(req: Request) {
  try {
    const session = await getServerSession()
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { provider } = await req.json()

    if (!provider) {
      return NextResponse.json({ error: "Provider is required" }, { status: 400 })
    }

    // Validate provider
    const allowedProviders = ["google", "facebook", "apple"]
    if (!allowedProviders.includes(provider)) {
      return NextResponse.json({ error: "Invalid provider" }, { status: 400 })
    }

    // Unlink the social account
    await unlinkSocialAccount(session.userId, provider)

    return NextResponse.json({ 
      success: true,
      message: `${provider} account unlinked successfully` 
    })

  } catch (error) {
    console.error("Account unlinking error:", error)
    return NextResponse.json({ error: "Failed to unlink account" }, { status: 500 })
  }
}
