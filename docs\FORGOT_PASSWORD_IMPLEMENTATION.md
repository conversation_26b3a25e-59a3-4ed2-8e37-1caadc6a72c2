# Forgot Password Implementation

## Overview

This document describes the comprehensive forgot password feature implementation for the Sealog maritime certification platform. The implementation follows industry-standard security practices and integrates seamlessly with the existing authentication system.

## Features

### Core Functionality
- **Secure Password Reset Flow**: Token-based password reset with 48-hour expiration
- **Email Integration**: Professional HTML emails using Resend service
- **Rate Limiting**: Protection against abuse with 3 attempts per 15 minutes per IP
- **Security Best Practices**: Prevents email enumeration attacks
- **Mobile Responsive**: Consistent UI across all devices
- **OAuth Compatibility**: Works with both credential and OAuth users who have added passwords

### User Experience
- **Intuitive UI**: Follows existing authentication page design patterns
- **Email Pre-population**: Automatically populates email field when navigating from login page
- **Clear Feedback**: Production-ready user feedback at each step
- **Error Handling**: Comprehensive error states with user-friendly messages
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Smart Navigation**: "Forgot Password?" link includes typed email from login form

## Implementation Details

### API Routes

#### `/api/auth/forgot-password` (POST)
- **Purpose**: <PERSON><PERSON> forgot password requests
- **Rate Limiting**: 3 attempts per 15 minutes per IP address
- **Security**: Always returns success to prevent email enumeration
- **Email Validation**: Server-side email format validation
- **Token Generation**: 32-character secure tokens with 48-hour expiry

#### `/api/auth/reset-password` (GET/POST)
- **GET**: Validate reset token and return user email
- **POST**: Process password reset with new password
- **Validation**: Password strength requirements (minimum 8 characters)
- **Security**: Token marked as used after successful reset

### UI Components

#### `/forgot-password` Page
- **Form Validation**: Client-side email validation
- **Loading States**: Proper loading indicators during API calls
- **Success State**: Clear confirmation with option to try again
- **Error Handling**: User-friendly error messages

#### `/reset-password` Page
- **Token Validation**: Automatic token validation on page load
- **Password Input**: Show/hide password functionality
- **Confirmation**: Password confirmation field with matching validation
- **Success Redirect**: Automatic redirect to login after successful reset

### Email Templates

#### Password Reset Email
- **Professional Design**: Branded HTML email template
- **Security Notice**: Clear security information and expiration details
- **Accessibility**: Text fallback for HTML email
- **Call-to-Action**: Prominent reset button with fallback URL

### Database Schema

#### PasswordReset Table
```sql
CREATE TABLE "PasswordReset" (
  id TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
  token TEXT UNIQUE NOT NULL,
  "expiresAt" TIMESTAMP NOT NULL,
  used BOOLEAN DEFAULT false NOT NULL,
  "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL
);
```

### Security Features

#### Rate Limiting
- **Implementation**: In-memory rate limiting (production should use Redis)
- **Limits**: 3 attempts per 15 minutes per IP address
- **Response**: Clear error message with remaining time

#### Token Security
- **Generation**: Cryptographically secure 32-character tokens using nanoid
- **Expiration**: 48-hour expiry (configurable via environment variable)
- **Single Use**: Tokens marked as used after successful password reset
- **Cleanup**: Expired tokens automatically ignored

#### Email Enumeration Prevention
- **Consistent Response**: Always returns success regardless of email existence
- **Logging**: Server-side logging for security monitoring
- **OAuth Handling**: Special handling for OAuth-only users

## Configuration

### Environment Variables

```bash
# Email Configuration
RESEND_API_KEY="your-resend-api-key"
FROM_EMAIL="<EMAIL>"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Token Expiry Configuration
PASSWORD_RESET_TOKEN_EXPIRY_HOURS=48
EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS=48
```

### Route Configuration

The following routes are configured as public (no authentication required):
- `/forgot-password` - Forgot password form
- `/reset-password` - Password reset form with token validation

## Testing

### Manual Testing Checklist

#### Forgot Password Flow
- [ ] Navigate to login page and click "Forgot Password?" link (without email)
- [ ] Verify forgot password page loads with empty email field
- [ ] Navigate to login page, enter email, then click "Forgot Password?" link
- [ ] Verify forgot password page loads with email field pre-populated
- [ ] Submit form with pre-populated email
- [ ] Verify success message appears
- [ ] Check email inbox for password reset email
- [ ] Verify email contains proper branding and reset link
- [ ] Test "try again" functionality preserves email address

#### Reset Password Flow
- [ ] Click reset link from email
- [ ] Verify token validation occurs automatically
- [ ] Enter new password (test validation requirements)
- [ ] Confirm password matches
- [ ] Submit form and verify success message
- [ ] Verify automatic redirect to login page
- [ ] Test login with new password

#### Error Scenarios
- [ ] Test with invalid email format
- [ ] Test with non-existent email (should still show success)
- [ ] Test with expired token
- [ ] Test with invalid token
- [ ] Test rate limiting (3+ attempts in 15 minutes)
- [ ] Test password mismatch
- [ ] Test weak password (less than 8 characters)

#### OAuth User Scenarios
- [ ] Test forgot password for OAuth-only user (no password set)
- [ ] Test forgot password for OAuth user with password added

### Security Testing
- [ ] Verify email enumeration protection
- [ ] Test rate limiting effectiveness
- [ ] Verify token expiration handling
- [ ] Test token reuse prevention
- [ ] Verify proper error message sanitization

## Integration Points

### Existing Systems
- **Authentication**: Integrates with NextAuth.js credential provider
- **Email Service**: Uses existing Resend integration
- **Database**: Uses existing Drizzle ORM setup
- **Middleware**: Leverages existing route protection system
- **UI Components**: Follows existing design system patterns

### Future Enhancements
- **Redis Rate Limiting**: Replace in-memory rate limiting for production
- **Email Templates**: Enhanced email template customization
- **Audit Logging**: Comprehensive security event logging
- **Multi-Factor**: Integration with future 2FA implementation

## Deployment Notes

### Production Considerations
- Configure proper `FROM_EMAIL` domain with Resend
- Set up Redis for distributed rate limiting
- Monitor password reset attempt patterns
- Configure proper CORS and security headers
- Set up email delivery monitoring

### Environment Setup
1. Add required environment variables to production environment
2. Verify Resend API key has proper permissions
3. Test email delivery in production environment
4. Configure monitoring for failed email deliveries
5. Set up alerts for unusual password reset patterns

## Troubleshooting

### Common Issues
- **Emails not sending**: Check Resend API key and FROM_EMAIL configuration
- **Rate limiting too aggressive**: Adjust `MAX_ATTEMPTS` and `RATE_LIMIT_WINDOW`
- **Token expiration**: Verify `PASSWORD_RESET_TOKEN_EXPIRY_HOURS` setting
- **Redirect issues**: Check `NEXT_PUBLIC_APP_URL` configuration

### Debugging
- Enable authentication logging in development
- Check server logs for password reset attempts
- Verify database token creation and cleanup
- Monitor email delivery status in Resend dashboard
