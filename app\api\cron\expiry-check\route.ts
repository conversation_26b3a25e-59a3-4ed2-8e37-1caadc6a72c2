import { AuthenticationError, handleApiError } from "@/lib/api-error-handler"
import { cleanupExpiredNotifications, generateExpiryNotifications } from "@/lib/db/notifications"
import { getAllUsers } from "@/lib/db/users"
import { logger } from "@/lib/logger"
import { NextRequest, NextResponse } from "next/server"

/**
 * Certificate expiry check cron job
 * This endpoint should be called by cron jobs or Vercel cron
 * POST /api/cron/expiry-check
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      logger.warn("cron", "Unauthorized expiry check attempt", {
        authHeader: authHeader ? "[REDACTED]" : "missing",
        ip: request.headers.get("x-forwarded-for") || "unknown",
      })
      throw new AuthenticationError("Invalid cron secret")
    }

    logger.info("cron", "Certificate expiry check started", {
      ip: request.headers.get("x-forwarded-for") || "unknown",
    })

    const startTime = Date.now()

    // Get all active users (not soft deleted)
    const users = await getAllUsers()
    const activeUsers = users.filter(user => !user.deletedAt)

    logger.info("cron", "Processing users for expiry notifications", {
      totalUsers: activeUsers.length
    })

    let totalNotificationsCreated = 0
    let usersProcessed = 0
    let errors = 0
    const errorDetails: string[] = []

    // Process each user
    for (const user of activeUsers) {
      try {
        // Generate expiry notifications for this user
        // Check for certificates expiring in 90, 60, 30, and 7 days
        const result = await generateExpiryNotifications(user.id, [90, 60, 30, 7])

        if (result.success) {
          totalNotificationsCreated += result.created || 0
          usersProcessed++
        } else {
          errors++
          errorDetails.push(`User ${user.id}: ${result.error}`)
        }
      } catch (error) {
        errors++
        const errorMessage = error instanceof Error ? error.message : "Unknown error"
        errorDetails.push(`User ${user.id}: ${errorMessage}`)
        logger.error("cron", "Error processing user for expiry notifications", {
          userId: user.id,
          error: errorMessage
        })
      }
    }

    // Clean up expired notifications
    let cleanupResult: { success: boolean; deletedCount: number } = { success: false, deletedCount: 0 }
    try {
      const result = await cleanupExpiredNotifications()
      if (result.success && 'deletedCount' in result && typeof result.deletedCount === 'number') {
        cleanupResult = { success: result.success, deletedCount: result.deletedCount }
      }
    } catch (error) {
      logger.error("cron", "Error cleaning up expired notifications", {
        error: error instanceof Error ? error.message : "Unknown error"
      })
    }

    const duration = Date.now() - startTime

    const responseData = {
      success: true,
      timestamp: new Date().toISOString(),
      duration,
      stats: {
        totalUsers: activeUsers.length,
        usersProcessed,
        notificationsCreated: totalNotificationsCreated,
        errors,
        expiredNotificationsDeleted: cleanupResult.deletedCount
      },
      errorDetails: errors > 0 ? errorDetails : undefined
    }

    logger.info("cron", "Certificate expiry check completed", responseData.stats)

    return NextResponse.json(responseData)

  } catch (error) {
    logger.error("cron", "Certificate expiry check failed", {
      error: error instanceof Error ? error.message : "Unknown error"
    })
    return handleApiError(error, "cron-expiry-check")
  }
}

/**
 * Get expiry check job status and history
 * GET /api/cron/expiry-check
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      throw new AuthenticationError("Invalid cron secret")
    }

    // For now, return a simple status
    // In the future, we could store job history in the database
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      status: "ready",
      message: "Certificate expiry check endpoint is ready"
    })

  } catch (error) {
    return handleApiError(error, "cron-expiry-check-status")
  }
}
