"use client"

import { DebugDashboard } from "@/components/debug-dashboard"
import { ErrorBoundary } from "@/components/error-boundary"

export default function DebugDashboardPage() {
  // Only show debug dashboard in development or when explicitly enabled
  if (process.env.NODE_ENV === "production" && process.env.NEXT_PUBLIC_ENABLE_DEBUG_DASHBOARD !== "true") {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Debug Dashboard</h1>
          <p className="text-muted-foreground">
            Debug dashboard is not available in production mode.
          </p>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary showDetails={true}>
      <div className="container mx-auto p-6">
        <DebugDashboard />
      </div>
    </ErrorBoundary>
  )
}
