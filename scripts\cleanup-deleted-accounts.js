#!/usr/bin/env node

/**
 * Scheduled cleanup script for permanently deleting accounts that have been soft-deleted for 30+ days
 * 
 * This script should be run as a cron job, for example:
 * - Daily at 2 AM: 0 2 * * *
 * - Weekly on Sunday at 3 AM: 0 3 * * 0
 * 
 * Usage:
 * node scripts/cleanup-deleted-accounts.js
 * 
 * Environment variables required:
 * - DATABASE_URL: PostgreSQL connection string
 * - CRON_SECRET: Secret key for API authentication
 * - NEXT_PUBLIC_APP_URL: Application URL for API calls
 */

const https = require('https');
const http = require('http');

// Configuration
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const CRON_SECRET = process.env.CRON_SECRET || 'your-secret-key';
const API_ENDPOINT = `${APP_URL}/api/admin/cleanup`;

/**
 * Make HTTP request to the cleanup API
 */
function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: data ? JSON.parse(data) : null
          };
          resolve(response);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

/**
 * Run the cleanup job
 */
async function runCleanup() {
  console.log('🧹 Starting account cleanup job...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  console.log(`🔗 API Endpoint: ${API_ENDPOINT}`);
  
  try {
    // Check if required environment variables are set
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }
    
    if (!CRON_SECRET || CRON_SECRET === 'your-secret-key') {
      console.warn('⚠️  WARNING: Using default CRON_SECRET. Please set a secure secret in production.');
    }
    
    // Make request to cleanup API
    const response = await makeRequest(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CRON_SECRET}`,
        'User-Agent': 'Sealog-Cleanup-Script/1.0'
      }
    });
    
    console.log(`📊 API Response Status: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      const result = response.body;
      console.log('✅ Cleanup job completed successfully!');
      console.log(`📈 Results:`);
      console.log(`   - Users processed: ${result.results?.usersProcessed || 0}`);
      console.log(`   - Users deleted: ${result.results?.usersDeleted || 0}`);
      console.log(`   - Errors: ${result.results?.errors || 0}`);
      
      if (result.results?.errorDetails && result.results.errorDetails.length > 0) {
        console.log('❌ Error details:');
        result.results.errorDetails.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
      }
      
      console.log(`🆔 Job ID: ${result.jobId}`);
      
      // Exit with success
      process.exit(0);
      
    } else if (response.statusCode === 401) {
      console.error('❌ Authentication failed. Check CRON_SECRET environment variable.');
      process.exit(1);
      
    } else {
      console.error(`❌ Cleanup job failed with status ${response.statusCode}`);
      console.error('Response:', response.body);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Cleanup job failed with error:');
    console.error(error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure your application is running and accessible at:', APP_URL);
    }
    
    process.exit(1);
  }
}

/**
 * Get cleanup status (for monitoring)
 */
async function getCleanupStatus() {
  console.log('📊 Getting cleanup status...');
  
  try {
    const response = await makeRequest(API_ENDPOINT, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CRON_SECRET}`,
        'User-Agent': 'Sealog-Cleanup-Script/1.0'
      }
    });
    
    if (response.statusCode === 200) {
      const result = response.body;
      console.log('📈 Cleanup Status:');
      console.log(`   - Users eligible for deletion: ${result.eligibleUsers || 0}`);
      console.log(`   - Recent jobs: ${result.recentJobs?.length || 0}`);
      
      if (result.recentJobs && result.recentJobs.length > 0) {
        console.log('\n📋 Recent cleanup jobs:');
        result.recentJobs.slice(0, 5).forEach((job, index) => {
          const date = new Date(job.createdAt).toLocaleDateString();
          const time = new Date(job.createdAt).toLocaleTimeString();
          console.log(`   ${index + 1}. ${job.status} - ${date} ${time} (${job.recordsDeleted || 0} deleted)`);
        });
      }
      
      process.exit(0);
    } else {
      console.error(`❌ Failed to get status: ${response.statusCode}`);
      console.error('Response:', response.body);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Failed to get cleanup status:');
    console.error(error.message);
    process.exit(1);
  }
}

// Main execution
const command = process.argv[2];

if (command === 'status') {
  getCleanupStatus();
} else if (command === 'run' || !command) {
  runCleanup();
} else {
  console.log('Usage:');
  console.log('  node scripts/cleanup-deleted-accounts.js [run]  - Run cleanup job');
  console.log('  node scripts/cleanup-deleted-accounts.js status - Get cleanup status');
  process.exit(1);
}
