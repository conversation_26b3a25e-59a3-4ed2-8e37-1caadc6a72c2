{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "debug.console.fontSize": 14, "debug.console.lineHeight": 22, "debug.inlineValues": "on", "debug.showBreakpointsInOverviewRuler": true, "debug.terminal.clearBeforeReusing": true, "javascript.preferences.includePackageJsonAutoImports": "on", "javascript.suggest.autoImports": true, "javascript.updateImportsOnFileMove.enabled": "always", "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/.next": true, "**/dist": true, "**/build": true, "**/.vercel": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.next/**": true, "**/dist/**": true, "**/build/**": true}, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}