#!/usr/bin/env node

/**
 * Validation script to ensure the authentication system is properly configured
 * and all legacy "yachtie" references have been removed
 */

const fs = require('fs');
const path = require('path');

// Files to check for legacy references
const filesToCheck = [
  'lib/auth-config.ts',
  'lib/session.ts', 
  'lib/auth-migration.ts',
  'middleware.ts',
  'app/api/register/route.ts',
  'app/api/login/route.ts',
  'lib/db.ts',
  'prisma/schema.prisma',
  'hooks/use-session.ts',
  'docs/RBAC_IMPLEMENTATION_GUIDE.md'
];

// Legacy terms that should not appear in code (except in migration utilities)
const legacyTerms = [
  'yachtie',
  'YACHTIE',
  '"yachtie"',
  "'yachtie'",
  'role.*=.*yachtie',
  'UserRole.YACHTIE'
];

// Required terms that should appear
const requiredTerms = [
  'INDIVIDUAL_USER',
  'individual_user',
  'individual_free'
];

function checkFile(filePath) {
  console.log(`\n📁 Checking ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  File not found: ${filePath}`);
    return { warnings: 1, errors: 0 };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  let warnings = 0;
  let errors = 0;

  // Check for legacy terms (except in migration files)
  const isMigrationFile = filePath.includes('migration') || filePath.includes('RBAC_IMPLEMENTATION_GUIDE');
  
  if (!isMigrationFile) {
    legacyTerms.forEach(term => {
      const regex = new RegExp(term, 'gi');
      const matches = content.match(regex);
      if (matches) {
        console.log(`   ❌ Found legacy term "${term}": ${matches.length} occurrences`);
        errors += matches.length;
      }
    });
  }

  // Check for required terms in key files
  if (filePath.includes('auth-config') || filePath.includes('register') || filePath.includes('db.ts')) {
    requiredTerms.forEach(term => {
      if (!content.includes(term)) {
        console.log(`   ⚠️  Missing required term: ${term}`);
        warnings++;
      }
    });
  }

  if (errors === 0 && warnings === 0) {
    console.log(`   ✅ Clean - no issues found`);
  }

  return { warnings, errors };
}

function validateDatabaseSchema() {
  console.log(`\n🗄️  Validating database schema...`);
  
  const dbFile = 'lib/db.ts';
  const prismaFile = 'prisma/schema.prisma';
  
  let errors = 0;
  let warnings = 0;

  // Check Drizzle schema
  if (fs.existsSync(dbFile)) {
    const content = fs.readFileSync(dbFile, 'utf8');
    
    if (!content.includes('role: text("role").default("individual_user")')) {
      console.log(`   ❌ Missing role column with correct default in ${dbFile}`);
      errors++;
    }
    
    if (!content.includes('subscriptionPlan: text("subscriptionPlan").default("individual_free")')) {
      console.log(`   ❌ Missing subscriptionPlan column with correct default in ${dbFile}`);
      errors++;
    }
    
    if (content.includes('role: text("role").default("yachtie")')) {
      console.log(`   ❌ Found legacy default role in ${dbFile}`);
      errors++;
    }
  }

  // Check Prisma schema
  if (fs.existsSync(prismaFile)) {
    const content = fs.readFileSync(prismaFile, 'utf8');
    
    if (!content.includes('role             String   @default("individual_user")')) {
      console.log(`   ❌ Missing role field with correct default in ${prismaFile}`);
      errors++;
    }
    
    if (!content.includes('subscriptionPlan String   @default("individual_free")')) {
      console.log(`   ❌ Missing subscriptionPlan field with correct default in ${prismaFile}`);
      errors++;
    }
  }

  if (errors === 0) {
    console.log(`   ✅ Database schema looks good`);
  }

  return { warnings, errors };
}

function validateRegistrationFlow() {
  console.log(`\n🔐 Validating registration flow...`);
  
  const registerFile = 'app/api/register/route.ts';
  let errors = 0;
  
  if (fs.existsSync(registerFile)) {
    const content = fs.readFileSync(registerFile, 'utf8');
    
    if (!content.includes('UserRole.INDIVIDUAL_USER')) {
      console.log(`   ❌ Registration doesn't assign INDIVIDUAL_USER role`);
      errors++;
    }
    
    if (!content.includes('IndividualSubscriptionPlan.FREE')) {
      console.log(`   ❌ Registration doesn't assign individual_free subscription plan`);
      errors++;
    }
    
    if (content.includes('"role", "subscriptionPlan"') && content.includes('INSERT INTO "User"')) {
      console.log(`   ✅ Registration properly assigns role and subscription plan`);
    } else {
      console.log(`   ❌ Registration SQL doesn't include role and subscriptionPlan columns`);
      errors++;
    }
  }

  return { warnings: 0, errors };
}

async function main() {
  console.log("🔍 Validating Authentication System");
  console.log("=====================================");

  let totalWarnings = 0;
  let totalErrors = 0;

  // Check individual files
  for (const file of filesToCheck) {
    const result = checkFile(file);
    totalWarnings += result.warnings;
    totalErrors += result.errors;
  }

  // Validate database schema
  const schemaResult = validateDatabaseSchema();
  totalWarnings += schemaResult.warnings;
  totalErrors += schemaResult.errors;

  // Validate registration flow
  const registrationResult = validateRegistrationFlow();
  totalWarnings += registrationResult.warnings;
  totalErrors += registrationResult.errors;

  // Summary
  console.log(`\n📊 Validation Summary`);
  console.log("=====================");
  console.log(`Warnings: ${totalWarnings}`);
  console.log(`Errors: ${totalErrors}`);

  if (totalErrors === 0 && totalWarnings === 0) {
    console.log(`\n🎉 All checks passed! The authentication system is clean.`);
    console.log(`✅ No legacy "yachtie" references found`);
    console.log(`✅ New users will be assigned INDIVIDUAL_USER role`);
    console.log(`✅ Database schema includes role and subscription plan columns`);
    console.log(`✅ Registration flow properly assigns roles`);
  } else {
    console.log(`\n⚠️  Issues found that need attention.`);
    if (totalErrors > 0) {
      console.log(`❌ ${totalErrors} errors must be fixed`);
      process.exit(1);
    }
  }
}

// Run validation
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkFile, validateDatabaseSchema, validateRegistrationFlow };
