import { neon } from "@neondatabase/serverless";
import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/neon-http";
import { boolean, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { nanoid } from "nanoid";
import { NextRequest, NextResponse } from "next/server";

// Define schema tables for seeding
const users = pgTable("User", {
  id: text("id").primaryKey(),
  email: text("email").notNull().unique(),
});

const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  issuingAuthority: text("issuingAuthority").notNull(),
  certificateNumber: text("certificateNumber").notNull(),
  dateIssued: timestamp("dateIssued").notNull(),
  expiryDate: timestamp("expiryDate"),
  notes: text("notes"),
  isFavorite: boolean("isFavorite").default(false).notNull(),
  userId: text("userId").notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

const notifications = pgTable("Notification", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull(),
  type: text("type").notNull(),
  title: text("title").notNull(),
  message: text("message").notNull(),
  read: boolean("read").default(false).notNull(),
  actionUrl: text("actionUrl"),
  metadata: text("metadata"),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  expiresAt: timestamp("expiresAt"),
});

/**
 * Seed notification test data for E2E testing
 * POST /api/admin/seed-notifications
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development/test environments
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Notification test data seeding is disabled in production" },
        { status: 403 }
      );
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: "DATABASE_URL environment variable is required" },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action !== 'seed_test_data') {
      return NextResponse.json(
        { error: "Invalid action. Use 'seed_test_data'" },
        { status: 400 }
      );
    }

    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    const testEmail = "<EMAIL>";

    // Get the test user
    const testUser = await db
      .select()
      .from(users)
      .where(eq(users.email, testEmail))
      .limit(1);

    if (testUser.length === 0) {
      return NextResponse.json(
        { error: "Test user not found. Please run 'pnpm seed:test-user' first" },
        { status: 404 }
      );
    }

    const testUserId = testUser[0].id;

    // Clear existing notifications for test user
    await db.delete(notifications).where(eq(notifications.userId, testUserId));

    // Clear existing test certificates
    await db.delete(certificates).where(eq(certificates.userId, testUserId));

    // Create certificates with different expiry dates to trigger notifications
    const today = new Date();
    const testCertificates = [
      {
        id: nanoid(),
        name: "Certificate Expiring Today",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-TODAY-001",
        dateIssued: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000),
        expiryDate: new Date(today.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
        notes: "This certificate expires today - should trigger notification",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Expiring in 7 Days",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-7D-001",
        dateIssued: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000),
        expiryDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000),
        notes: "This certificate will expire in 7 days - should trigger notification",
        isFavorite: true,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "STCW Basic Safety Training",
        issuingAuthority: "Maritime Training Institute",
        certificateNumber: "STCW-TEST-001",
        dateIssued: new Date(today.getTime() - 200 * 24 * 60 * 60 * 1000),
        expiryDate: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000),
        notes: "STCW certificate for search testing",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Valid for 2 Years",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-VALID-001",
        dateIssued: new Date(),
        expiryDate: new Date(today.getTime() + 2 * 365 * 24 * 60 * 60 * 1000),
        notes: "This certificate is valid for a long time - should not trigger notifications",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    // Insert test certificates
    await db.insert(certificates).values(testCertificates);

    // Create sample notifications to test the UI
    const sampleNotifications = [
      {
        id: nanoid(),
        userId: testUserId,
        type: "certificate_expiry",
        title: "Certificate Expiring Soon",
        message: "Your Certificate Expiring in 7 Days will expire in 7 days. Please renew it to avoid any issues.",
        read: false,
        actionUrl: "/certificates",
        metadata: JSON.stringify({
          certificateId: testCertificates[1].id,
          certificateName: testCertificates[1].name,
          expiryDate: testCertificates[1].expiryDate.toISOString(),
          daysUntilExpiry: 7,
        }),
        createdAt: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
        expiresAt: null,
      },
      {
        id: nanoid(),
        userId: testUserId,
        type: "certificate_expiry",
        title: "Certificate Expires Today",
        message: "Your Certificate Expiring Today expires today. Immediate action required!",
        read: false,
        actionUrl: "/certificates",
        metadata: JSON.stringify({
          certificateId: testCertificates[0].id,
          certificateName: testCertificates[0].name,
          expiryDate: testCertificates[0].expiryDate.toISOString(),
          daysUntilExpiry: 0,
        }),
        createdAt: new Date(today.getTime() - 1 * 60 * 60 * 1000), // 1 hour ago
        expiresAt: null,
      },
      {
        id: nanoid(),
        userId: testUserId,
        type: "system",
        title: "Welcome to Sealog",
        message: "Welcome to your maritime certificate management platform. Start by uploading your certificates.",
        read: true,
        actionUrl: "/certificates/new",
        metadata: JSON.stringify({ type: "welcome" }),
        createdAt: new Date(today.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
        expiresAt: null,
      },
    ];

    // Insert sample notifications
    await db.insert(notifications).values(sampleNotifications);

    return NextResponse.json({
      success: true,
      message: "Notification test data seeded successfully",
      data: {
        certificatesCreated: testCertificates.length,
        notificationsCreated: sampleNotifications.length,
        testUserId,
      },
    });

  } catch (error) {
    console.error("Error seeding notification test data:", error);
    return NextResponse.json(
      { error: "Failed to seed notification test data", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * Get seeded test data status
 * GET /api/admin/seed-notifications
 */
export async function GET() {
  try {
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Test data endpoints are disabled in production" },
        { status: 403 }
      );
    }

    const sql = neon(process.env.DATABASE_URL!);
    const db = drizzle(sql);

    const testEmail = "<EMAIL>";

    // Get test user
    const testUser = await db
      .select()
      .from(users)
      .where(eq(users.email, testEmail))
      .limit(1);

    if (testUser.length === 0) {
      return NextResponse.json({
        success: false,
        message: "Test user not found",
      });
    }

    const testUserId = testUser[0].id;

    // Count certificates and notifications
    const certificateCount = await db
      .select()
      .from(certificates)
      .where(eq(certificates.userId, testUserId));

    const notificationCount = await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, testUserId));

    return NextResponse.json({
      success: true,
      data: {
        testUserId,
        certificatesCount: certificateCount.length,
        notificationsCount: notificationCount.length,
        lastSeeded: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error("Error getting test data status:", error);
    return NextResponse.json(
      { error: "Failed to get test data status", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
