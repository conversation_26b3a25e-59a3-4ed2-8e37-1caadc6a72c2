import { logger } from "@/lib/logger";

interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
}

export async function withEmailRetry<T>(
  emailFunction: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffMultiplier = 2
  } = options;

  let lastError: Error = new Error('Email function failed');

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await emailFunction();

      if (attempt > 1) {
        logger.info('email', `Email sent successfully on attempt ${attempt}`);
      }

      return result;
    } catch (error) {
      lastError = error as Error;

      logger.warn('email', `Email attempt ${attempt} failed`, {
        error: lastError.message,
        attempt,
        maxRetries
      });

      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt - 1), maxDelay);

      logger.debug('email', `Retrying email in ${delay}ms`, { attempt, delay });

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  logger.error('email', `Email failed after ${maxRetries} attempts`, {
    error: lastError.message,
    maxRetries
  });

  throw lastError;
}

// Wrapper for common email operations
export async function sendEmailWithRetry(
  emailFunction: () => Promise<any>,
  context: string = 'unknown'
): Promise<{ success: boolean; error?: string }> {
  try {
    await withEmailRetry(emailFunction, {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 5000
    });

    logger.info('email', `Email sent successfully: ${context}`);
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('email', `Email failed permanently: ${context}`, { error: errorMessage });
    return { success: false, error: errorMessage };
  }
}

// Specialized retry function for critical emails (account deletion, password reset, etc.)
export async function sendCriticalEmailWithRetry(
  emailFunction: () => Promise<any>,
  context: string = 'critical'
): Promise<{ success: boolean; error?: string }> {
  try {
    await withEmailRetry(emailFunction, {
      maxRetries: 5, // More retries for critical emails
      baseDelay: 2000, // Longer initial delay
      maxDelay: 15000, // Higher max delay
      backoffMultiplier: 1.5 // Gentler backoff
    });

    logger.info('email', `Critical email sent successfully: ${context}`);
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('email', `Critical email failed permanently: ${context}`, { error: errorMessage });
    return { success: false, error: errorMessage };
  }
}
