import { getPasswordReset, getUserById, markPasswordResetAsUsed } from "@/lib/db"
import { hash } from "bcrypt"
import { eq } from "drizzle-orm"
import { NextResponse } from "next/server"
import { db, users } from "@/lib/db"

/**
 * Validate password reset token (GET request)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: "Reset token is required"
      }, { status: 400 })
    }

    // Get password reset record
    const passwordReset = await getPasswordReset(token)

    if (!passwordReset) {
      return NextResponse.json({
        success: false,
        error: "Invalid or expired reset token"
      }, { status: 400 })
    }

    // Check if token has expired
    const now = new Date()
    if (passwordReset.expiresAt < now) {
      return NextResponse.json({
        success: false,
        error: "Reset token has expired"
      }, { status: 400 })
    }

    // Get user to verify they exist
    const user = await getUserById(passwordReset.userId)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: "User not found"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: "Valid reset token",
      userEmail: user.email
    })

  } catch (error) {
    console.error("Reset token validation error:", error)
    return NextResponse.json({
      success: false,
      error: "Something went wrong. Please try again."
    }, { status: 500 })
  }
}

/**
 * Reset password with token (POST request)
 */
export async function POST(request: Request) {
  try {
    const { token, password, confirmPassword } = await request.json()

    if (!token || !password || !confirmPassword) {
      return NextResponse.json({
        success: false,
        error: "All fields are required"
      }, { status: 400 })
    }

    // Validate password match
    if (password !== confirmPassword) {
      return NextResponse.json({
        success: false,
        error: "Passwords do not match"
      }, { status: 400 })
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json({
        success: false,
        error: "Password must be at least 8 characters long"
      }, { status: 400 })
    }

    // Get password reset record
    const passwordReset = await getPasswordReset(token)

    if (!passwordReset) {
      return NextResponse.json({
        success: false,
        error: "Invalid or expired reset token"
      }, { status: 400 })
    }

    // Check if token has expired
    const now = new Date()
    if (passwordReset.expiresAt < now) {
      return NextResponse.json({
        success: false,
        error: "Reset token has expired"
      }, { status: 400 })
    }

    // Get user to verify they exist
    const user = await getUserById(passwordReset.userId)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: "User not found"
      }, { status: 404 })
    }

    // Hash the new password
    const hashedPassword = await hash(password, 12)

    // Update user password
    await db
      .update(users)
      .set({
        password: hashedPassword,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id))

    // Mark password reset as used
    await markPasswordResetAsUsed(token)

    return NextResponse.json({
      success: true,
      message: "Password reset successfully",
      shouldRedirect: true,
      redirectUrl: "/login"
    })

  } catch (error) {
    console.error("Password reset error:", error)
    return NextResponse.json({
      success: false,
      error: "Something went wrong. Please try again."
    }, { status: 500 })
  }
}
