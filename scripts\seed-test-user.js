/**
 * Seed script to create a test user for E2E testing
 * This user should only exist in development/test environments
 */

// Load environment variables
require("dotenv").config({ path: ".env.local" });

const { drizzle } = require("drizzle-orm/neon-http");
const { neon } = require("@neondatabase/serverless");
const bcrypt = require("bcryptjs");
const { eq } = require("drizzle-orm");
const { pgTable, text, timestamp, boolean } = require("drizzle-orm/pg-core");

// Use dynamic import for nanoid
async function getNanoid() {
  const { nanoid } = await import("nanoid");
  return nanoid;
}

// Define schema tables (simplified for seeding)
const users = pgTable("User", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password"),
  role: text("role").default("individual_user").notNull(),
  subscriptionPlan: text("subscriptionPlan")
    .default("individual_free")
    .notNull(),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  issuingAuthority: text("issuingAuthority").notNull(),
  certificateNumber: text("certificateNumber").notNull(),
  dateIssued: timestamp("dateIssued").notNull(),
  expiryDate: timestamp("expiryDate"),
  documentUrl: text("documentUrl"),
  notes: text("notes"),
  isFavorite: boolean("isFavorite").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  userId: text("userId").notNull(),
});

async function seedTestUser() {
  // Only run in development or test environments
  if (process.env.NODE_ENV === "production") {
    console.log("❌ Test user seeding is disabled in production");
    process.exit(1);
  }

  if (!process.env.DATABASE_URL) {
    console.log("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  console.log("🌱 Seeding test user for E2E testing...");

  try {
    const nanoid = await getNanoid();
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    const testEmail = "<EMAIL>";
    const testPassword = "AutoTest123!";

    // Check if test user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, testEmail))
      .limit(1);

    let testUserId;

    if (existingUser.length > 0) {
      console.log("👤 Test user already exists, updating...");
      testUserId = existingUser[0].id;

      // Update password and ensure verified status
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      await db
        .update(users)
        .set({
          password: hashedPassword,
          emailVerified: true,
          updatedAt: new Date(),
        })
        .where(eq(users.id, testUserId));
    } else {
      console.log("👤 Creating new test user...");

      // Create test user
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      testUserId = nanoid();

      await db.insert(users).values({
        id: testUserId,
        name: "Auto Test User",
        email: testEmail,
        password: hashedPassword,
        emailVerified: true, // Pre-verified for testing
        role: "individual_user",
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // Create some test certificates for the user
    console.log("📜 Creating test certificates...");

    // Delete existing test certificates
    await db.delete(certificates).where(eq(certificates.userId, testUserId));

    // Create fresh test certificates
    const testCertificates = [
      {
        id: nanoid(),
        name: "STCW Basic Safety Training",
        issuingAuthority: "Maritime Training Institute",
        certificateNumber: "STCW-2023-001",
        dateIssued: new Date("2023-01-15"),
        expiryDate: new Date("2028-01-15"),
        documentUrl: null,
        notes: "Basic safety training certificate for testing",
        isFavorite: true,
        userId: testUserId,
        createdAt: new Date("2023-01-15"),
        updatedAt: new Date("2023-01-15"),
      },
      {
        id: nanoid(),
        name: "Medical First Aid",
        issuingAuthority: "Red Cross Maritime",
        certificateNumber: "MFA-2023-002",
        dateIssued: new Date("2023-03-10"),
        expiryDate: new Date("2025-03-10"),
        documentUrl: null,
        notes: "Medical first aid certification",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date("2023-03-10"),
        updatedAt: new Date("2023-03-10"),
      },
      {
        id: nanoid(),
        name: "Radio Operator License",
        issuingAuthority: "Maritime Communications Authority",
        certificateNumber: "ROL-2022-003",
        dateIssued: new Date("2022-06-20"),
        expiryDate: new Date("2024-06-20"), // Expired for testing
        documentUrl: null,
        notes: "Radio operator license - expired for testing",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date("2022-06-20"),
        updatedAt: new Date("2022-06-20"),
      },
      {
        id: nanoid(),
        name: "Engine Room Watch Rating",
        issuingAuthority: "Maritime Skills Institute",
        certificateNumber: "ERW-2023-004",
        dateIssued: new Date("2023-08-05"),
        expiryDate: new Date("2025-12-31"), // Expiring soon for testing
        documentUrl: null,
        notes: "Engine room watch rating certificate",
        isFavorite: true,
        userId: testUserId,
        createdAt: new Date("2023-08-05"),
        updatedAt: new Date("2023-08-05"),
      },
      {
        id: nanoid(),
        name: "Deck Hand Certification",
        issuingAuthority: "Yacht Training Academy",
        certificateNumber: "DHC-2024-005",
        dateIssued: new Date("2024-01-10"),
        expiryDate: null, // No expiry for testing
        documentUrl: null,
        notes: "Deck hand certification with no expiry date",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date("2024-01-10"),
        updatedAt: new Date("2024-01-10"),
      },
    ];

    await db.insert(certificates).values(testCertificates);

    console.log("✅ Test user seeded successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: AutoTest123!");
    console.log(`📜 Created ${testCertificates.length} test certificates`);
    console.log("🎯 User is pre-verified and ready for E2E testing");
  } catch (error) {
    console.error("❌ Error seeding test user:", error);
    process.exit(1);
  }
}

// Run the seeding
seedTestUser();
