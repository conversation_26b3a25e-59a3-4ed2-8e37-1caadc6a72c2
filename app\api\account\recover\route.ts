import { recoverAccount } from "@/lib/db"
import { sendAccountRecoveryEmail } from "@/lib/email"
import { headers } from "next/headers"
import { NextResponse } from "next/server"

/**
 * Recover account from deletion
 * POST /api/account/recover
 */
export async function POST(req: Request) {
  try {
    const { userId, recoveryToken, email } = await req.json()

    if (!userId || !recoveryToken) {
      return NextResponse.json({
        error: "User ID and recovery token are required"
      }, { status: 400 })
    }

    // Get request metadata for audit
    const headersList = await headers()
    const ipAddress = headersList.get("x-forwarded-for") ||
      headersList.get("x-real-ip") ||
      "unknown"
    const userAgent = headersList.get("user-agent") || "unknown"

    // Attempt to recover the account
    await recoverAccount(recoveryToken)

    // Send recovery confirmation email if email provided
    if (email) {
      try {
        await sendAccountRecoveryEmail(email, "User", {
          recoveryDate: new Date(),
          ipAddress,
          userAgent
        })
      } catch (emailError) {
        console.error("Failed to send recovery confirmation email:", emailError)
        // Don't fail the recovery if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: "Your account has been successfully recovered. You can now log in normally."
    })

  } catch (error) {
    console.error("Account recovery error:", error)

    if (error instanceof Error) {
      if (error.message.includes("User not found")) {
        return NextResponse.json({
          error: "Invalid recovery link"
        }, { status: 404 })
      }

      if (error.message.includes("No deletion request found")) {
        return NextResponse.json({
          error: "No deletion request found for this account"
        }, { status: 400 })
      }

      if (error.message.includes("already been permanently deleted")) {
        return NextResponse.json({
          error: "This account has already been permanently deleted and cannot be recovered"
        }, { status: 400 })
      }

      if (error.message.includes("Invalid recovery token")) {
        return NextResponse.json({
          error: "Invalid or expired recovery token"
        }, { status: 400 })
      }

      if (error.message.includes("Recovery token has expired")) {
        return NextResponse.json({
          error: "Recovery period has expired. This account cannot be recovered."
        }, { status: 400 })
      }
    }

    return NextResponse.json({
      error: "Failed to recover account"
    }, { status: 500 })
  }
}
