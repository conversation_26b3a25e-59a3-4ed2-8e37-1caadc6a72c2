import { AuthenticationError, handleApiError, ValidationError } from "@/lib/api-error-handler"
import { performSoftDeletion, requestAccountDeletion } from "@/lib/db"
import { sendAccountDeletionEmail } from "@/lib/email"
import { getServerSession } from "@/lib/session"
import { accountDeletionSchema, validateRequestBody } from "@/lib/validation-schemas"
import { headers } from "next/headers"
import { NextResponse } from "next/server"

/**
 * Request account deletion
 * POST /api/account/delete
 */
export async function POST(req: Request) {
  try {
    const session = await getServerSession()

    if (!session) {
      throw new AuthenticationError();
    }

    // Validate request body
    const validatedData = await validateRequestBody(req, accountDeletionSchema);
    const { reason, confirmEmail, immediateDelete } = validatedData;

    // Validate that the user confirmed with their email
    if (confirmEmail !== session.email) {
      throw new ValidationError("Email confirmation does not match your account email");
    }

    // Get request metadata for audit
    const headersList = await headers()
    const ipAddress = headersList.get("x-forwarded-for") ||
      headersList.get("x-real-ip") ||
      "unknown"
    const userAgent = headersList.get("user-agent") || "unknown"

    if (immediateDelete) {
      // Immediate soft deletion (anonymize data immediately)
      await performSoftDeletion(session.userId, ipAddress, userAgent)

      // Send deletion confirmation email
      try {
        await sendAccountDeletionEmail(session.email, session.name, {
          type: "immediate",
          deletionDate: new Date(),
          reason
        })
      } catch (emailError) {
        console.error("Failed to send deletion confirmation email:", emailError)
        // Don't fail the deletion if email fails
      }

      return NextResponse.json({
        success: true,
        message: "Your account has been deleted immediately. All personal data has been anonymized.",
        type: "immediate"
      })
    } else {
      // Request deletion with 30-day recovery period
      const result = await requestAccountDeletion(
        session.userId,
        reason,
        ipAddress,
        userAgent
      )

      // Send deletion request confirmation email with recovery instructions
      try {
        await sendAccountDeletionEmail(session.email, session.name, {
          type: "scheduled",
          deletionDate: result.recoveryDeadline!,
          recoveryToken: result.deletionToken!,
          reason
        })
      } catch (emailError) {
        console.error("Failed to send deletion request email:", emailError)
        // Don't fail the request if email fails
      }

      return NextResponse.json({
        success: true,
        message: "Account deletion has been scheduled. You have 30 days to recover your account if you change your mind.",
        type: "scheduled",
        recoveryExpiresAt: result.recoveryDeadline
        // ✅ Token removed - only sent via secure email for security
      })
    }

  } catch (error) {
    return handleApiError(error, 'account-deletion');
  }
}

/**
 * Get account deletion status
 * GET /api/account/delete
 */
export async function GET() {
  try {
    const session = await getServerSession()

    if (!session) {
      throw new AuthenticationError();
    }

    const { getAccountDeletionStatus } = await import("@/lib/db")
    const status = await getAccountDeletionStatus(session.userId)

    return NextResponse.json({
      success: true,
      status
    })

  } catch (error) {
    return handleApiError(error, 'account-deletion-status');
  }
}
