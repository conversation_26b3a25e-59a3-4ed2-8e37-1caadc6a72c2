#!/usr/bin/env node

/**
 * Manual E2E Test Runner
 * Builds the app, starts the server, and runs a focused E2E test
 */

const { spawn } = require('child_process');
const fs = require('fs');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log(`\n${colors.bold}${colors.blue}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    logInfo(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  logHeader('Manual E2E Test Runner');
  
  try {
    // Step 1: Build the application
    logHeader('Building Application');
    await runCommand('pnpm', ['build']);
    logSuccess('Application built successfully');

    // Step 2: Seed test user
    logHeader('Seeding Test User');
    await runCommand('pnpm', ['seed:test-user']);
    logSuccess('Test user seeded successfully');

    // Step 3: Run a focused E2E test
    logHeader('Running Focused E2E Test');
    logInfo('Running only the authentication flow test...');
    
    await runCommand('npx', ['playwright', 'test', 'e2e/auth.spec.ts', '--headed']);
    logSuccess('E2E test completed successfully');

  } catch (error) {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main().catch(error => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main };
