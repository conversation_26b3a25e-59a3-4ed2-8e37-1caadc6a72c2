import { AuthenticationError, handleApiError } from "@/lib/api-error-handler"
import {
  deleteNotification,
  getNotificationsByUserId,
  getUnreadNotificationCount,
  markAllNotificationsAsRead,
  markNotificationAsRead
} from "@/lib/db/notifications"
import { cookies } from "next/headers"
import { NextRequest, NextResponse } from "next/server"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

/**
 * Get notifications for the authenticated user
 * GET /api/notifications
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      throw new AuthenticationError("Authentication required")
    }

    const { searchParams } = new URL(request.url)
    const includeRead = searchParams.get("includeRead") !== "false"
    const countOnly = searchParams.get("countOnly") === "true"

    if (countOnly) {
      const count = await getUnreadNotificationCount(user.id)
      return NextResponse.json({
        success: true,
        count
      })
    }

    const notifications = await getNotificationsByUserId(user.id, includeRead)

    return NextResponse.json({
      success: true,
      notifications
    })
  } catch (error) {
    return handleApiError(error, "notifications-get")
  }
}

/**
 * Mark notifications as read or delete notifications
 * PATCH /api/notifications
 */
export async function PATCH(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      throw new AuthenticationError("Authentication required")
    }

    const body = await request.json()
    const { action, notificationId } = body

    if (!action) {
      return NextResponse.json(
        { success: false, error: "Action is required" },
        { status: 400 }
      )
    }

    let result

    switch (action) {
      case "mark_read":
        if (!notificationId) {
          return NextResponse.json(
            { success: false, error: "Notification ID is required for mark_read action" },
            { status: 400 }
          )
        }
        result = await markNotificationAsRead(notificationId, user.id)
        break

      case "mark_all_read":
        result = await markAllNotificationsAsRead(user.id)
        break

      default:
        return NextResponse.json(
          { success: false, error: "Invalid action" },
          { status: 400 }
        )
    }

    return NextResponse.json(result)
  } catch (error) {
    return handleApiError(error, "notifications-patch")
  }
}

/**
 * Delete a notification
 * DELETE /api/notifications
 */
export async function DELETE(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      throw new AuthenticationError("Authentication required")
    }

    const { searchParams } = new URL(request.url)
    const notificationId = searchParams.get("id")

    if (!notificationId) {
      return NextResponse.json(
        { success: false, error: "Notification ID is required" },
        { status: 400 }
      )
    }

    const result = await deleteNotification(notificationId, user.id)

    return NextResponse.json(result)
  } catch (error) {
    return handleApiError(error, "notifications-delete")
  }
}
