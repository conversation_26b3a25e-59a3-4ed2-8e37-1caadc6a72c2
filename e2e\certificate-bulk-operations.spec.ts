import { expect, test } from '@playwright/test';

test.describe('Certificate Bulk Operations', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' });

  test.beforeEach(async ({ page }) => {
    // Clean up any existing test data
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_certificates' }
    });

    // Seed fresh test data with multiple certificates for bulk operations
    await page.request.post('/api/admin/seed-notifications', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'seed_test_data' }
    });

    // Navigate to certificates page
    await page.goto('/certificates');
    await page.waitForLoadState('networkidle');
  });

  test('should show checkboxes by default and allow selection', async ({ page }) => {
    // Switch to table view to see checkboxes clearly
    const tableTab = page.locator('text=Table');
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(500);

      // Selection checkboxes should be visible by default
      const selectAllCheckbox = page.locator('[data-testid="select-all-checkbox"]');
      await expect(selectAllCheckbox).toBeVisible();

      // Individual certificate checkboxes should be visible
      const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const checkboxCount = await certificateCheckboxes.count();
      expect(checkboxCount).toBeGreaterThan(0);
    }

    // Check grid view also has checkboxes
    const gridTab = page.locator('text=Grid');
    if (await gridTab.isVisible()) {
      await gridTab.click();
      await page.waitForTimeout(500);

      // Certificate cards should have checkboxes
      const certificateCards = page.locator('[data-testid^="certificate-card-"]');
      const cardCount = await certificateCards.count();
      if (cardCount > 0) {
        // Check if first card has a checkbox
        const firstCardCheckbox = page.locator('[data-testid^="select-certificate-"]').first();
        await expect(firstCardCheckbox).toBeVisible();
      }
    }

    console.log('✅ Checkboxes are visible by default in both views');
  });

  test('should select and deselect certificates', async ({ page }) => {
    // Switch to table view for better checkbox visibility
    const tableTab = page.locator('text=Table');
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(500);

      // Get certificate checkboxes
      const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const checkboxCount = await certificateCheckboxes.count();

      if (checkboxCount > 0) {
        // Select first certificate
        await certificateCheckboxes.first().click();
        await page.waitForTimeout(300);

        // Bulk actions bar should appear
        const bulkActionsBar = page.locator('text=selected');
        await expect(bulkActionsBar).toBeVisible();

        // Select second certificate if available
        if (checkboxCount > 1) {
          await certificateCheckboxes.nth(1).click();
          await page.waitForTimeout(300);

          // Should show "2 selected"
          await expect(page.locator('text=2 selected')).toBeVisible();
        }

        // Test select all functionality
        const selectAllCheckbox = page.locator('[data-testid="select-all-checkbox"]');
        await selectAllCheckbox.click();
        await page.waitForTimeout(300);

        // Should show all certificates selected
        await expect(page.locator(`text=${checkboxCount} selected`)).toBeVisible();

        // Test deselect all
        const deselectAllButton = page.locator('[data-testid="deselect-all-button"]');
        if (await deselectAllButton.isVisible()) {
          await deselectAllButton.click();
          await page.waitForTimeout(300);

          // Bulk actions bar should disappear
          await expect(bulkActionsBar).not.toBeVisible();
        }
      }
    }

    console.log('✅ Certificate selection and deselection working correctly');
  });

  test('should perform bulk favorite operations', async ({ page }) => {
    // Switch to table view
    const tableTab = page.locator('text=Table');
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(500);

      // Select first certificate
      const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const checkboxCount = await certificateCheckboxes.count();

      if (checkboxCount > 0) {
        await certificateCheckboxes.first().click();
        await page.waitForTimeout(300);

        // Click bulk favorite button
        const bulkFavoriteButton = page.locator('[data-testid="bulk-favorite-button"]');
        if (await bulkFavoriteButton.isVisible()) {
          await bulkFavoriteButton.click();
          await page.waitForTimeout(500);

          // Should show success toast
          const successToast = page.locator('text=Successfully added to favorites');
          await expect(successToast).toBeVisible({ timeout: 5000 });

          console.log('✅ Bulk favorite operation completed successfully');
        }
      }
    }
  });

  test('should perform bulk delete operations with confirmation', async ({ page }) => {
    // Switch to table view
    const tableTab = page.locator('text=Table');
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(500);

      // Get initial certificate count
      const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const initialCount = await certificateCheckboxes.count();

      if (initialCount > 0) {
        // Select first certificate
        await certificateCheckboxes.first().click();
        await page.waitForTimeout(300);

        // Open bulk actions menu
        const bulkActionsMenu = page.locator('[data-testid="bulk-actions-menu"]');
        if (await bulkActionsMenu.isVisible()) {
          await bulkActionsMenu.click();
          await page.waitForTimeout(300);

          // Click delete option
          const deleteOption = page.locator('text=Delete Selected');
          if (await deleteOption.isVisible()) {
            await deleteOption.click();
            await page.waitForTimeout(300);

            // Should show confirmation dialog
            const confirmDialog = page.locator('text=Delete Certificates');
            await expect(confirmDialog).toBeVisible();

            // Cancel first to test cancellation
            const cancelButton = page.locator('text=Cancel');
            await cancelButton.click();
            await page.waitForTimeout(300);

            // Dialog should close
            await expect(confirmDialog).not.toBeVisible();

            // Try delete again and confirm
            await bulkActionsMenu.click();
            await deleteOption.click();
            await page.waitForTimeout(300);

            // Confirm deletion
            const confirmButton = page.locator('button:has-text("Delete 1 Certificate")');
            if (await confirmButton.isVisible()) {
              await confirmButton.click();
              await page.waitForTimeout(1000);

              // Should show success toast
              const successToast = page.locator('text=Successfully deleted');
              await expect(successToast).toBeVisible({ timeout: 5000 });

              console.log('✅ Bulk delete operation completed successfully');
            }
          }
        }
      }
    }
  });

  test('should handle bulk operations on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // On mobile, grid view is default - check if certificates are selectable
    const certificateCards = page.locator('[data-testid^="certificate-card"]');
    const cardCount = await certificateCards.count();

    if (cardCount > 0) {
      // Test grid view selection on mobile
      const gridCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const gridCheckboxCount = await gridCheckboxes.count();

      if (gridCheckboxCount > 0) {
        await gridCheckboxes.first().click();
        await page.waitForTimeout(300);

        // Bulk actions should be visible and usable on mobile
        const bulkActionsBar = page.locator('text=selected');
        await expect(bulkActionsBar).toBeVisible();
      }

      // Switch to table view for mobile testing
      const tableTab = page.locator('text=Table');
      if (await tableTab.isVisible()) {
        await tableTab.click();
        await page.waitForTimeout(500);

        // Should be able to select certificates on mobile
        const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
        const mobileCheckboxCount = await certificateCheckboxes.count();

        if (mobileCheckboxCount > 0) {
          await certificateCheckboxes.first().click();
          await page.waitForTimeout(300);

          // Bulk action buttons should be appropriately sized for mobile
          const bulkDownloadButton = page.locator('[data-testid="bulk-download-button"]');
          if (await bulkDownloadButton.isVisible()) {
            // Button should be at least 44px high for touch targets
            const buttonBox = await bulkDownloadButton.boundingBox();
            expect(buttonBox?.height).toBeGreaterThanOrEqual(44);
          }
        }
      }
    }

    console.log('✅ Mobile bulk operations working correctly');
  });

  test('should show appropriate feedback for bulk operations', async ({ page }) => {
    // Switch to table view
    const tableTab = page.locator('text=Table');
    if (await tableTab.isVisible()) {
      await tableTab.click();
      await page.waitForTimeout(500);

      const certificateCheckboxes = page.locator('[data-testid^="select-certificate-"]');
      const checkboxCount = await certificateCheckboxes.count();

      if (checkboxCount > 0) {
        // Select certificates
        await certificateCheckboxes.first().click();
        await page.waitForTimeout(300);

        // Note: Category functionality has been removed from the application

        // Test bulk download (should show "coming soon" message)
        const bulkDownloadButton = page.locator('[data-testid="bulk-download-button"]');
        if (await bulkDownloadButton.isVisible()) {
          await bulkDownloadButton.click();
          await page.waitForTimeout(500);

          // Should show coming soon toast
          const comingSoonToast = page.locator('text=Feature Coming Soon');
          await expect(comingSoonToast).toBeVisible({ timeout: 5000 });
        }
      }
    }

    console.log('✅ Bulk operations feedback working correctly');
  });
});
