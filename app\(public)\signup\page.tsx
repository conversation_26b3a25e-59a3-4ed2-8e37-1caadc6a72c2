"use client";

import React from "react";

import { Anchor } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  delayedRedirect,
  getOAuthProviders,
  handleApiError,
  handleAuthResponse,
  handleOAuthLogin,
  validatePasswordMatch,
} from "@/lib/auth-utils";
import { logger } from "@/lib/logger";

export default function SignupPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSSOLoading, setIsSSOLoading] = useState<string | null>(null);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [hasExistingSession, setHasExistingSession] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  // Check which OAuth providers are available
  const { isGoogleEnabled, isFacebookEnabled, hasOAuthProviders } =
    getOAuthProviders();

  // Check for existing unverified session on component mount
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const response = await fetch("/api/auth/session", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          if (data.session && !data.session.emailVerified) {
            setHasExistingSession(true);
            logger.info(
              "auth",
              "Existing unverified session detected on signup page",
              {
                currentEmail: data.session.email.substring(0, 3) + "***",
              }
            );
          }
        }
      } catch (error) {
        // Ignore errors - this is just for UX enhancement
        console.log("Session check failed:", error);
      }
    };

    checkExistingSession();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    const endTimer = logger.authStart("user-signup", formData.email);
    logger.info("auth", "Signup attempt started", {
      email: logger.isLoggingEnabled()
        ? formData.email.substring(0, 3) + "***"
        : undefined,
    });

    // Validate passwords match using shared utility
    if (!validatePasswordMatch(formData.password, formData.confirmPassword)) {
      setError("Passwords do not match");
      setIsLoading(false);
      endTimer();
      return;
    }

    try {
      const apiEndTimer = logger.apiRequest("POST", "/api/register");

      const requestBody = {
        name: formData.name,
        email: formData.email,
        password: formData.password,
      };

      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/register", response.status);

      // Use enhanced error handling
      const result = await handleAuthResponse(response);

      if (!result.success) {
        throw new Error(result.error || "Something went wrong");
      }

      const data = result.data;

      logger.authSuccess("user-signup", data.user?.id || "unknown-user");

      // Industry-standard UX: Immediate redirect to verification-pending
      if (data.requiresVerification) {
        logger.info("auth", "Signup successful, redirecting to verification", {
          userId: data.user?.id,
        });

        // Immediate redirect without showing success message on signup page
        router.push("/verification-pending");
      } else {
        // Fallback for accounts that don't require verification
        setSuccessMessage(
          "Account created successfully! Redirecting to login..."
        );
        delayedRedirect(router, "/login?registered=true", 1500);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      logger.authFailure("user-signup", errorMessage, formData.email);
      logger.error("auth", "Signup request failed", {
        error: errorMessage,
        email: formData.email.substring(0, 3) + "***",
      });
      setError(errorMessage);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  const handleSSOLogin = async (provider: string) => {
    await handleOAuthLogin(provider, setIsSSOLoading, setError, "/dashboard");
  };

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <Anchor className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            Create Your Sealog Account
          </CardTitle>
          <CardDescription className="text-center">
            Enter your details to create your account
          </CardDescription>
        </CardHeader>

        {/* SSO Login Options - Only show if OAuth providers are available */}
        {hasOAuthProviders && (
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {isGoogleEnabled && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSSOLogin("google")}
                  disabled={isSSOLoading === "google"}
                >
                  {isSSOLoading === "google"
                    ? "Connecting..."
                    : "Continue with Google"}
                </Button>
              )}
              {isFacebookEnabled && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSSOLogin("facebook")}
                  disabled={isSSOLoading === "facebook"}
                >
                  {isSSOLoading === "facebook"
                    ? "Connecting..."
                    : "Continue with Facebook"}
                </Button>
              )}
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with email
                </span>
              </div>
            </div>
          </CardContent>
        )}

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4 pt-0">
            {hasExistingSession && (
              <Alert variant="default" className="border-amber-200 bg-amber-50">
                <AlertDescription className="text-amber-800">
                  Creating a new account will replace your current unverified
                  session.
                </AlertDescription>
              </Alert>
            )}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            {successMessage && (
              <Alert variant="default" className="border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="Enter your full name"
                required
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm Password</Label>
              <Input
                id="confirm-password"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={handleChange}
              />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? "Creating Account..." : "Create My Sealog Account"}
            </Button>
            <div className="text-center text-sm">
              Already have an account?{" "}
              <Link href="/login" className="text-primary hover:underline">
                Login
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
