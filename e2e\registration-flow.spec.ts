import { expect, test } from '@playwright/test'

test.describe('Registration Flow - Core Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/signup')
  })

  test('should display signup form correctly', async ({ page }) => {
    // Check basic form elements are present
    await expect(page.locator('input[name="name"]')).toBeVisible()
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[name="password"]')).toBeVisible()
    await expect(page.locator('input[name="confirmPassword"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()

    console.log('✅ Signup form displays correctly')
  })

  test('should handle form validation and submission', async ({ page }) => {
    // Test form validation by submitting empty form
    await page.click('button[type="submit"]')

    // Check that form doesn't submit with empty fields (browser validation)
    await expect(page.locator('input[name="name"]:invalid')).toBeVisible()

    // Fill out form with valid data
    await page.fill('input[name="name"]', 'Test User')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'TestPassword123!')
    await page.fill('input[name="confirmPassword"]', 'TestPassword123!')

    // Check that form is now valid
    await expect(page.locator('input[name="name"]:invalid')).not.toBeVisible()

    console.log('✅ Form validation working correctly')
  })
})
