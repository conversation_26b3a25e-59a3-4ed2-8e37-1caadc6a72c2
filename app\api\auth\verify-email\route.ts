import { getEmailVerification, getUserById, markEmailAsVerified } from "@/lib/db"
import { NextResponse } from "next/server"

/**
 * Verify email address using token
 */
export async function POST(request: Request) {
  try {
    const { token } = await request.json()

    if (!token) {
      return NextResponse.json({
        success: false,
        error: "Verification token is required"
      }, { status: 400 })
    }

    // Get verification record
    const verification = await getEmailVerification(token)

    if (!verification) {
      return NextResponse.json({
        success: false,
        error: "Invalid or expired verification token"
      }, { status: 400 })
    }

    // Check if token has expired
    const now = new Date()
    if (verification.expiresAt < now) {
      return NextResponse.json({
        success: false,
        error: "Verification token has expired"
      }, { status: 400 })
    }

    // Get user to verify they exist
    const user = await getUserById(verification.userId)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: "User not found"
      }, { status: 404 })
    }

    // Check if already verified
    if (user.emailVerified) {
      return NextResponse.json({
        success: true,
        message: "Email already verified",
        alreadyVerified: true
      })
    }

    // Mark email as verified and clean up verification tokens
    await markEmailAsVerified(verification.userId)

    return NextResponse.json({
      success: true,
      message: "Email verified successfully",
      userId: verification.userId,
      shouldRedirect: true,
      redirectUrl: "/dashboard"
    })

  } catch (error) {
    console.error("Email verification error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to verify email. Please try again."
    }, { status: 500 })
  }
}

/**
 * Get verification status for a token (GET request for URL-based verification)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: "Verification token is required"
      }, { status: 400 })
    }

    // Get verification record
    const verification = await getEmailVerification(token)

    if (!verification) {
      return NextResponse.json({
        success: false,
        error: "Invalid verification token"
      }, { status: 400 })
    }

    // Check if token has expired
    const now = new Date()
    if (verification.expiresAt < now) {
      return NextResponse.json({
        success: false,
        error: "Verification token has expired"
      }, { status: 400 })
    }

    // Get user to check current verification status
    const user = await getUserById(verification.userId)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: "User not found"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      user: {
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified
      },
      tokenValid: true
    })

  } catch (error) {
    console.error("Email verification check error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to check verification status"
    }, { status: 500 })
  }
}
