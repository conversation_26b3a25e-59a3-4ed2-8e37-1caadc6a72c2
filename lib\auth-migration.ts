/**
 * Authentication migration utilities
 * Handles backward compatibility for role name changes and subscription plan updates
 */

import { UserRole, IndividualSubscriptionPlan, LEGACY_ROLE_MAPPING } from "./auth-config"

/**
 * Migrate legacy role names to new role names
 */
export function migrateLegacyRole(role: string): UserRole {
  // Handle specific legacy role migration
  if (role === "yachtie") {
    return UserRole.INDIVIDUAL_USER
  }

  // Check if it's in the legacy mapping
  if (role in LEGACY_ROLE_MAPPING) {
    return LEGACY_ROLE_MAPPING[role as keyof typeof LEGACY_ROLE_MAPPING]
  }

  // If it's already a valid new role, return as-is
  if (Object.values(UserRole).includes(role as UserRole)) {
    return role as UserRole
  }

  // Default fallback for unknown roles
  console.warn(`Unknown role "${role}", defaulting to INDIVIDUAL_USER`)
  return UserRole.INDIVIDUAL_USER
}

/**
 * Migrate legacy subscription plan names to new plan names
 */
export function migrateLegacySubscriptionPlan(
  plan: string,
  role: UserRole
): string {
  // Legacy plan mappings
  const legacyPlanMapping: Record<string, string> = {
    "free": IndividualSubscriptionPlan.FREE,
    "basic": IndividualSubscriptionPlan.BASIC,
    "premium": IndividualSubscriptionPlan.PREMIUM,
    "enterprise": IndividualSubscriptionPlan.PROFESSIONAL
  }

  // If it's a legacy plan, map it to the appropriate new plan based on role
  if (plan in legacyPlanMapping) {
    if (role === UserRole.INDIVIDUAL_USER) {
      return legacyPlanMapping[plan]
    }
    // For other roles, we'll need to determine appropriate mapping
    // This would be implemented based on business requirements
  }

  // If it's already a valid new plan format, return as-is
  return plan
}

/**
 * Check if a session needs migration
 */
export function needsMigration(sessionData: any): boolean {
  if (!sessionData?.user) return false

  // Check if role needs migration
  const hasLegacyRole = sessionData.user.role &&
    (sessionData.user.role === "yachtie" || sessionData.user.role in LEGACY_ROLE_MAPPING)

  // Check if subscription plan needs migration
  const hasLegacyPlan = sessionData.user.subscriptionPlan &&
    ["free", "basic", "premium", "enterprise"].includes(sessionData.user.subscriptionPlan)

  return hasLegacyRole || hasLegacyPlan
}

/**
 * Migrate a complete session object
 */
export function migrateSession(sessionData: any): any {
  if (!needsMigration(sessionData)) {
    return sessionData
  }

  const migratedData = { ...sessionData }

  if (migratedData.user) {
    // Migrate role
    if (migratedData.user.role) {
      const newRole = migrateLegacyRole(migratedData.user.role)
      migratedData.user.role = newRole

      // Migrate subscription plan based on new role
      if (migratedData.user.subscriptionPlan) {
        migratedData.user.subscriptionPlan = migrateLegacySubscriptionPlan(
          migratedData.user.subscriptionPlan,
          newRole
        )
      }
    }
  }

  return migratedData
}

/**
 * Database migration script (to be run once)
 * This would update existing user records in the database
 */
export async function migrateUserDatabase() {
  // This is a placeholder for the actual database migration
  // Implementation would depend on your database setup

  console.log("Starting user role migration...")

  // Example SQL for the migration:
  /*
  -- Add role and subscription plan columns if they don't exist
  ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "role" TEXT DEFAULT 'individual_user';
  ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "subscriptionPlan" TEXT DEFAULT 'individual_free';
  ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "tenantId" TEXT;
  ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "tenantRole" TEXT;

  -- Update existing users without roles to have INDIVIDUAL_USER role
  UPDATE "User"
  SET "role" = 'individual_user'
  WHERE "role" IS NULL;

  -- Update existing users without subscription plans
  UPDATE "User"
  SET "subscriptionPlan" = 'individual_free'
  WHERE "subscriptionPlan" IS NULL AND "role" = 'individual_user';

  -- Migrate any legacy subscription plans
  UPDATE "User"
  SET "subscriptionPlan" = 'individual_basic'
  WHERE "subscriptionPlan" = 'basic' AND "role" = 'individual_user';

  UPDATE "User"
  SET "subscriptionPlan" = 'individual_premium'
  WHERE "subscriptionPlan" = 'premium' AND "role" = 'individual_user';

  UPDATE "User"
  SET "subscriptionPlan" = 'individual_professional'
  WHERE "subscriptionPlan" = 'enterprise' AND "role" = 'individual_user';
  */

  console.log("User role migration completed")
}

/**
 * Validation function to ensure migrated data is correct
 */
export function validateMigratedSession(sessionData: any): boolean {
  if (!sessionData?.user) return false

  // Validate role
  const validRoles = Object.values(UserRole)
  if (!validRoles.includes(sessionData.user.role)) {
    console.error(`Invalid role after migration: ${sessionData.user.role}`)
    return false
  }

  // Validate subscription plan format
  if (sessionData.user.subscriptionPlan) {
    const hasValidPlanFormat =
      sessionData.user.subscriptionPlan.includes('_') ||
      sessionData.user.subscriptionPlan === 'system_admin'

    if (!hasValidPlanFormat) {
      console.error(`Invalid subscription plan format after migration: ${sessionData.user.subscriptionPlan}`)
      return false
    }
  }

  return true
}
