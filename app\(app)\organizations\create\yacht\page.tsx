"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { handleApiError } from "@/lib/auth-utils";

export default function CreateYachtPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    homePort: "",
    flagState: "",
    yachtType: "" as "motor" | "sailing" | "expedition" | "charter" | "",
    length: "",
    beam: "",
    draft: "",
    yearBuilt: "",
    crewSize: "",
    grossTonnage: "",
    website: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    // Validate required fields
    if (!formData.name.trim()) {
      setError("Yacht name is required");
      setIsLoading(false);
      return;
    }
    if (!formData.yachtType) {
      setError("Yacht type is required");
      setIsLoading(false);
      return;
    }

    try {
      const requestBody = {
        name: formData.name.trim(),
        type: "yacht_company",
        description: formData.description.trim() || undefined,
        website: formData.website.trim() || undefined,
        yachtDetails: {
          homePort: formData.homePort.trim() || undefined,
          flagState: formData.flagState.trim() || undefined,
          yachtType: formData.yachtType,
          length: formData.length ? parseFloat(formData.length) : undefined,
          beam: formData.beam ? parseFloat(formData.beam) : undefined,
          draft: formData.draft ? parseFloat(formData.draft) : undefined,
          yearBuilt: formData.yearBuilt
            ? parseInt(formData.yearBuilt)
            : undefined,
          crewSize: formData.crewSize ? parseInt(formData.crewSize) : undefined,
          grossTonnage: formData.grossTonnage
            ? parseFloat(formData.grossTonnage)
            : undefined,
        },
      };

      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Something went wrong");
      }

      setSuccessMessage(
        "Yacht profile created successfully! You can now start managing your crew and posting jobs."
      );

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/organizations/new">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization Types
          </Link>
        </Button>
      </div>

      <div className="max-w-3xl mx-auto">
        {/* Page Header */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <Anchor className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Create Yacht Profile
          </h1>
          <p className="text-muted-foreground text-lg">
            Set up your yacht profile for crew recruitment and management
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">
              Yacht Information
            </CardTitle>
            <CardDescription className="text-center">
              Provide details about your yacht to attract qualified crew members
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {successMessage && (
                <Alert
                  variant="default"
                  className="border-green-200 bg-green-50"
                >
                  <AlertDescription className="text-green-800">
                    {successMessage}
                  </AlertDescription>
                </Alert>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Yacht Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter yacht name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="yachtType">Yacht Type *</Label>
                    <Select
                      value={formData.yachtType}
                      onValueChange={(value) =>
                        handleSelectChange("yachtType", value)
                      }
                    >
                      <SelectTrigger data-testid="yacht-type-select">
                        <SelectValue placeholder="Select yacht type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="motor">Motor Yacht</SelectItem>
                        <SelectItem value="sailing">Sailing Yacht</SelectItem>
                        <SelectItem value="expedition">
                          Expedition Yacht
                        </SelectItem>
                        <SelectItem value="charter">Charter Yacht</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Brief description of your yacht, its character, and what makes it special..."
                    rows={3}
                    value={formData.description}
                    onChange={handleChange}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="homePort">Home Port</Label>
                    <Input
                      id="homePort"
                      name="homePort"
                      placeholder="e.g., Monaco, Fort Lauderdale"
                      value={formData.homePort}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="flagState">Flag State</Label>
                    <Input
                      id="flagState"
                      name="flagState"
                      placeholder="e.g., Cayman Islands, Marshall Islands"
                      value={formData.flagState}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>

              {/* Specifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Specifications</h3>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="length">Length (m)</Label>
                    <Input
                      id="length"
                      name="length"
                      type="number"
                      step="0.1"
                      placeholder="e.g., 50.5"
                      value={formData.length}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="beam">Beam (m)</Label>
                    <Input
                      id="beam"
                      name="beam"
                      type="number"
                      step="0.1"
                      placeholder="e.g., 9.2"
                      value={formData.beam}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="draft">Draft (m)</Label>
                    <Input
                      id="draft"
                      name="draft"
                      type="number"
                      step="0.1"
                      placeholder="e.g., 2.8"
                      value={formData.draft}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="yearBuilt">Year Built</Label>
                    <Input
                      id="yearBuilt"
                      name="yearBuilt"
                      type="number"
                      placeholder="e.g., 2018"
                      value={formData.yearBuilt}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="crewSize">Crew Size</Label>
                    <Input
                      id="crewSize"
                      name="crewSize"
                      type="number"
                      placeholder="e.g., 12"
                      value={formData.crewSize}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="grossTonnage">Gross Tonnage</Label>
                    <Input
                      id="grossTonnage"
                      name="grossTonnage"
                      type="number"
                      step="0.1"
                      placeholder="e.g., 499"
                      value={formData.grossTonnage}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="website">Website (Optional)</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    placeholder="https://www.yachtwebsite.com"
                    value={formData.website}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <Alert variant="default" className="border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  Your yacht profile will be created immediately. You can add
                  photos, amenities, and additional details after creation to
                  make your yacht more attractive to potential crew members.
                </AlertDescription>
              </Alert>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                className="w-full"
                type="submit"
                disabled={isLoading || !!successMessage}
              >
                {isLoading
                  ? "Creating Yacht Profile..."
                  : successMessage
                  ? "Yacht Profile Created!"
                  : "Create Yacht Profile"}
              </Button>

              <div className="text-center text-sm">
                <Link
                  href="/organizations/new"
                  className="text-muted-foreground hover:text-primary"
                >
                  Cancel and choose different type
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
