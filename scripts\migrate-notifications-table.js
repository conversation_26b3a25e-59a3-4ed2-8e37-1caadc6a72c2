#!/usr/bin/env node

/**
 * Migration Script: Add Notifications Table
 *
 * This script adds the notifications table for the notification system:
 * - Notification: Store user notifications with expiry tracking
 *
 * Run with: node scripts/migrate-notifications-table.js
 */

const { neon } = require("@neondatabase/serverless");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config({ path: ".env.local" });

const sql = neon(process.env.DATABASE_URL);

async function migrateNotificationsTable() {
  console.log("🚀 Starting notifications table migration...");

  try {
    // Create Notification table
    console.log("📝 Creating Notification table...");
    await sql`
      CREATE TABLE IF NOT EXISTS "Notification" (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        type TEXT NOT NULL, -- 'certificate_expiry' | 'system' | 'reminder'
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        read BOOLEAN DEFAULT false NOT NULL,
        "actionUrl" TEXT, -- Optional URL for notification action
        metadata TEXT, -- JSON metadata about the notification
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "expiresAt" TIMESTAMP -- Optional expiration for temporary notifications
      )
    `;
    console.log("✅ Created Notification table");

    // Create indexes for Notification table
    console.log("📝 Creating indexes for Notification table...");
    
    await sql`
      CREATE INDEX IF NOT EXISTS "notification_user_idx" ON "Notification"("userId")
    `;
    console.log("✅ Created notification user index");

    await sql`
      CREATE INDEX IF NOT EXISTS "notification_type_idx" ON "Notification"(type)
    `;
    console.log("✅ Created notification type index");

    await sql`
      CREATE INDEX IF NOT EXISTS "notification_read_idx" ON "Notification"(read)
    `;
    console.log("✅ Created notification read index");

    await sql`
      CREATE INDEX IF NOT EXISTS "notification_created_at_idx" ON "Notification"("createdAt")
    `;
    console.log("✅ Created notification created_at index");

    await sql`
      CREATE INDEX IF NOT EXISTS "notification_expires_at_idx" ON "Notification"("expiresAt")
    `;
    console.log("✅ Created notification expires_at index");

    console.log("🎉 Notifications table migration completed successfully!");
    console.log("");
    console.log("📋 Migration Summary:");
    console.log("   ✅ Created Notification table with proper schema");
    console.log("   ✅ Added foreign key constraint to User table");
    console.log("   ✅ Created 5 indexes for optimal query performance");
    console.log("   ✅ Ready for notification system functionality");
    console.log("");
    console.log("🚀 Next Steps:");
    console.log("   1. Run: pnpm seed:notification-test");
    console.log("   2. Test: pnpm test:notifications");
    console.log("   3. Start: pnpm dev");

  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run migration
migrateNotificationsTable();
