import { NextResponse } from "next/server"
import { getServerSession } from "@/lib/session"
import { getUserSocialAccounts } from "@/lib/db"

/**
 * Get all social accounts linked to the current user
 */
export async function GET() {
  try {
    const session = await getServerSession()
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user's linked social accounts
    const socialAccounts = await getUserSocialAccounts(session.userId)

    // Format the response to only include necessary information
    const formattedAccounts = socialAccounts.map(account => ({
      provider: account.provider,
      connectedAt: account.createdAt,
      // Don't expose sensitive tokens
    }))

    // Check which providers are available
    const availableProviders = {
      google: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
      facebook: !!(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET),
      apple: !!(process.env.APPLE_ID && process.env.APPLE_TEAM_ID && process.env.APPLE_PRIVATE_KEY),
    }

    return NextResponse.json({ 
      success: true,
      linkedAccounts: formattedAccounts,
      availableProviders
    })

  } catch (error) {
    console.error("Get social accounts error:", error)
    return NextResponse.json({ error: "Failed to get social accounts" }, { status: 500 })
  }
}
