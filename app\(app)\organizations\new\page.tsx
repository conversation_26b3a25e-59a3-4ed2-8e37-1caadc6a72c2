"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Anchor, GraduationCap } from "lucide-react";
import Link from "next/link";

export default function NewOrganizationPage() {
  return (
    <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Create Your Organization
          </h1>
          <p className="text-muted-foreground text-lg">
            Choose the type of organization you'd like to create
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 max-w-3xl mx-auto">
          {/* Yacht Card */}
          <Card
            className="border-2 hover:border-primary/50 transition-all duration-200 hover:shadow-lg cursor-pointer group"
            data-testid="yacht-organization-card"
          >
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-4 bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                <Anchor className="h-10 w-10 text-blue-600" />
              </div>
              <CardTitle className="text-xl" data-testid="yacht-card-title">
                Yacht
              </CardTitle>
              <CardDescription className="text-base">
                Create a yacht profile for crew recruitment and management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                  <span>Post job openings with yacht details</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                  <span>Manage crew certificates and documents</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                  <span>Showcase yacht photos and amenities</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                  <span>Track voyage schedules and crew rotations</span>
                </div>
              </div>
              <Button
                asChild
                className="w-full"
                data-testid="create-yacht-button"
              >
                <Link href="/organizations/create/yacht">
                  Create Yacht Profile
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Course Provider Card */}
          <Card
            className="border-2 hover:border-primary/50 transition-all duration-200 hover:shadow-lg cursor-pointer group"
            data-testid="training-provider-organization-card"
          >
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-4 bg-green-50 rounded-full w-20 h-20 flex items-center justify-center group-hover:bg-green-100 transition-colors">
                <GraduationCap className="h-10 w-10 text-green-600" />
              </div>
              <CardTitle
                className="text-xl"
                data-testid="training-provider-card-title"
              >
                Training Provider
              </CardTitle>
              <CardDescription className="text-base">
                Set up your maritime training and certification organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
                  <span>Create and manage course catalogs</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
                  <span>Enroll students and track progress</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
                  <span>Issue certificates and credentials</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
                  <span>Manage facilities and accommodations</span>
                </div>
              </div>
              <Button
                asChild
                className="w-full"
                data-testid="create-training-provider-button"
              >
                <Link href="/organizations/create/training-provider">
                  Create Training Provider
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground mb-4">
            You can create multiple organizations and switch between them
            anytime.
          </p>
          <Button variant="outline" asChild>
            <Link href="/dashboard">Cancel and return to dashboard</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
