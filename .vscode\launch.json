{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect"}, "skipFiles": ["<node_internals>/**"], "sourceMaps": true}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMaps": true}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect"}, "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "serverReadyAction": {"pattern": "ready - started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "test"}, "skipFiles": ["<node_internals>/**"]}]}