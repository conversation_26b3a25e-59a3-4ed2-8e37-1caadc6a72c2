/**
 * Session management utilities for enhanced authentication
 * Designed to support current simple auth and future RBAC/multi-tenant
 */

import { cookies } from "next/headers"
import {
  IndividualSubscriptionPlan,
  Permission,
  SessionContext,
  SubscriptionPlan,
  UserRole
} from "./auth-config"
// Note: Migration code removed since there are no real users and database can be reset

/**
 * Current simple session structure (for backward compatibility)
 */
interface SimpleSession {
  user: {
    id: string
    email: string
    name: string
    emailVerified?: boolean
  }
}

/**
 * Enhanced session structure (for future RBAC implementation)
 */
interface EnhancedSession extends SimpleSession {
  user: SimpleSession['user'] & {
    role: UserRole
    subscriptionPlan: SubscriptionPlan
    permissions: Permission[]
    tenantId?: string
    tenantRole?: string
  }
}

/**
 * Get session from cookies (server-side)
 */
export async function getServerSession(): Promise<SessionContext | null> {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")

    if (!sessionCookie?.value) {
      return null
    }

    const sessionData = JSON.parse(sessionCookie.value) as SimpleSession | EnhancedSession

    // Handle current simple session format
    if (!('role' in sessionData.user)) {
      return {
        userId: sessionData.user.id,
        email: sessionData.user.email,
        name: sessionData.user.name,
        role: UserRole.INDIVIDUAL_USER, // Default role for existing users
        subscriptionPlan: IndividualSubscriptionPlan.FREE, // Default plan for individual users
        permissions: getUserPermissions(UserRole.INDIVIDUAL_USER, IndividualSubscriptionPlan.FREE),
        emailVerified: sessionData.user.emailVerified || false // Default to false for safety
        // Note: Organization context handled via OrganizationMembership table
      }
    }

    // Handle enhanced session format
    const userRole = sessionData.user.role as UserRole
    const userPlan = sessionData.user.subscriptionPlan || IndividualSubscriptionPlan.FREE

    return {
      userId: sessionData.user.id,
      email: sessionData.user.email,
      name: sessionData.user.name,
      role: userRole,
      subscriptionPlan: userPlan as SubscriptionPlan,
      permissions: sessionData.user.permissions || getUserPermissions(userRole, userPlan as SubscriptionPlan),
      emailVerified: sessionData.user.emailVerified || false
      // Note: Organization context handled via OrganizationMembership table
    }
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

/**
 * Get session from cookies (client-side)
 * Note: This is a simplified version for client-side use
 */
export function getClientSession(): Promise<SessionContext | null> {
  return new Promise((resolve) => {
    try {
      // For client-side, we'll make an API call to get session info
      // This avoids exposing session parsing logic to the client
      fetch('/api/auth/session', { credentials: 'include' })
        .then(res => res.ok ? res.json() : null)
        .then(data => resolve(data?.session || null))
        .catch(() => resolve(null))
    } catch {
      resolve(null)
    }
  })
}

/**
 * Create enhanced session cookie
 * Currently creates simple session - will be enhanced for RBAC
 */
export async function createSession(user: {
  id: string
  email: string
  name: string
  role?: UserRole
  subscriptionPlan?: SubscriptionPlan
  emailVerified?: boolean
  tenantId?: string
  tenantRole?: string
}): Promise<void> {
  const cookieStore = await cookies()

  // For now, create simple session for backward compatibility
  const sessionData: SimpleSession = {
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified || false
    }
  }

  // Future: Create enhanced session
  // const sessionData: EnhancedSession = {
  //   user: {
  //     id: user.id,
  //     email: user.email,
  //     name: user.name,
  //     role: user.role || UserRole.INDIVIDUAL_USER,
  //     subscriptionPlan: user.subscriptionPlan || IndividualSubscriptionPlan.FREE,
  //     permissions: getUserPermissions(user.role, user.subscriptionPlan),
  //     tenantId: user.tenantId,
  //     tenantRole: user.tenantRole
  //   }
  // }

  cookieStore.set({
    name: "session",
    value: JSON.stringify(sessionData),
    httpOnly: true,
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 1 week
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax"
  })
}

/**
 * Clear session cookie
 */
export async function clearSession(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete("session")
}

/**
 * SCAFFOLDING: Get user permissions based on role and subscription plan
 * Note: Currently simplified - returns basic permissions only
 * Complex subscription-based permissions are kept for future RBAC implementation
 */
export function getUserPermissions(
  role: UserRole,
  _subscriptionPlan: SubscriptionPlan // Underscore prefix indicates intentionally unused parameter
): Permission[] {
  // SIMPLIFIED: Currently all individual users get basic certificate permissions
  // Complex subscription-based permissions are scaffolding for future implementation
  if (role === UserRole.INDIVIDUAL_USER) {
    return [
      Permission.CERTIFICATES_READ,
      Permission.CERTIFICATES_WRITE,
      Permission.CERTIFICATES_DELETE
    ]
  } else if (role === UserRole.SYSTEM_ADMIN) {
    // System admins get all permissions
    return Object.values(Permission)
  }

  // Note: Organization-specific permissions are handled via OrganizationMembership
  // Users don't have organization types as roles - they belong to organizations
  return []
}

/**
 * Check if user has specific permission
 * Will be used for component-level permission checks
 */
export function hasPermission(
  sessionContext: SessionContext | null,
  permission: Permission
): boolean {
  if (!sessionContext) return false
  return sessionContext.permissions.includes(permission)
}

/**
 * Check if user has any of the specified roles
 */
export function hasRole(
  sessionContext: SessionContext | null,
  roles: UserRole[]
): boolean {
  if (!sessionContext) return false
  return roles.includes(sessionContext.role)
}

/**
 * Check if user meets minimum subscription plan requirement
 * Note: This is simplified for now - will need role-specific logic when RBAC is implemented
 */
export function hasMinimumPlan(
  sessionContext: SessionContext | null,
  minimumPlan: SubscriptionPlan
): boolean {
  if (!sessionContext) return false

  // System admins always have access
  if (sessionContext.role === UserRole.SYSTEM_ADMIN) return true

  // For now, just check if plans match exactly
  // Future: implement proper hierarchy checking per role type
  return sessionContext.subscriptionPlan === minimumPlan

  // Future implementation would look like:
  // if (sessionContext.role === UserRole.INDIVIDUAL_USER) {
  //   const hierarchy = [IndividualSubscriptionPlan.FREE, IndividualSubscriptionPlan.BASIC, ...]
  //   return checkHierarchy(sessionContext.subscriptionPlan, minimumPlan, hierarchy)
  // } else if (sessionContext.role === UserRole.CERT_PROVIDER) {
  //   const hierarchy = [ProviderSubscriptionPlan.STARTER, ProviderSubscriptionPlan.PROFESSIONAL, ...]
  //   return checkHierarchy(sessionContext.subscriptionPlan, minimumPlan, hierarchy)
  // }
}

/**
 * Validate session context for route access
 * This will be the main function used by middleware
 */
export function validateRouteAccess(
  sessionContext: SessionContext | null,
  routeConfig: {
    requiresAuth: boolean
    allowedRoles?: UserRole[]
    requiredPermissions?: Permission[]
    minimumPlan?: SubscriptionPlan
    tenantRequired?: boolean
  }
): boolean {
  // Basic auth check
  if (routeConfig.requiresAuth && !sessionContext) {
    return false
  }

  if (!sessionContext) {
    return !routeConfig.requiresAuth
  }

  // Role check
  if (routeConfig.allowedRoles && !hasRole(sessionContext, routeConfig.allowedRoles)) {
    return false
  }

  // Permission check
  if (routeConfig.requiredPermissions) {
    const hasAllPermissions = routeConfig.requiredPermissions.every(permission =>
      hasPermission(sessionContext, permission)
    )
    if (!hasAllPermissions) {
      return false
    }
  }

  // Subscription plan check
  if (routeConfig.minimumPlan && !hasMinimumPlan(sessionContext, routeConfig.minimumPlan)) {
    return false
  }

  // Note: Tenant-based access removed - organization access handled via OrganizationMembership
  // if (routeConfig.tenantRequired && !sessionContext.tenantId) {
  //   return false
  // }

  return true
}
