import { AuthenticationError, handleApiError } from "@/lib/api-error-handler";
import { runAccountDeletionCleanup, runAllCleanupJobs, runExpiredTokensCleanup, runFileCleanup } from "@/lib/cron-jobs";
import { logger } from "@/lib/logger";
import { NextRequest, NextResponse } from "next/server";

/**
 * Cron job endpoint for automated cleanup tasks
 * This endpoint should be called by cron jobs or Vercel cron
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get("authorization");
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;

    if (!authHeader || authHeader !== expectedAuth) {
      logger.warn("cron", "Unauthorized cron job attempt", {
        authHeader: authHeader ? "[REDACTED]" : "missing",
        ip: request.headers.get("x-forwarded-for") || "unknown",
      });
      throw new AuthenticationError("Invalid cron secret");
    }

    // Get job type from query parameters
    const { searchParams } = new URL(request.url);
    const jobType = searchParams.get("type");

    logger.info("cron", "Cron job triggered", {
      jobType: jobType || "all",
      ip: request.headers.get("x-forwarded-for") || "unknown",
    });

    let result;

    switch (jobType) {
      case "account_deletion":
        result = await runAccountDeletionCleanup();
        break;

      case "expired_tokens":
        result = await runExpiredTokensCleanup();
        break;

      case "file_cleanup":
        result = await runFileCleanup();
        break;

      default:
        // Run all cleanup jobs
        result = await runAllCleanupJobs();
        break;
    }

    const responseData = {
      success: true,
      jobType: jobType || "all",
      timestamp: new Date().toISOString(),
      result,
    };

    logger.info("cron", "Cron job completed", responseData);

    return NextResponse.json(responseData);

  } catch (error) {
    return handleApiError(error, "cron-cleanup");
  }
}

/**
 * Get cron job status and history
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get("authorization");
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;

    if (!authHeader || authHeader !== expectedAuth) {
      throw new AuthenticationError("Invalid cron secret");
    }

    const { getJobStatus } = await import("@/lib/cron-jobs");
    const status = await getJobStatus();

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      ...status,
    });

  } catch (error) {
    return handleApiError(error, "cron-status");
  }
}
