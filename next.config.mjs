/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },

  // Source maps for better debugging
  productionBrowserSourceMaps:
    process.env.NODE_ENV === "development" ||
    process.env.ENABLE_SOURCE_MAPS === "true",
  // Webpack configuration for debugging
  webpack: (config, { dev }) => {
    // Only modify devtool for production builds with source maps enabled
    if (!dev && process.env.ENABLE_SOURCE_MAPS === "true") {
      config.devtool = "source-map";
    }

    // Better error handling
    config.optimization = {
      ...config.optimization,
      minimize: !dev,
    };

    return config;
  },
  // Logging configuration
  logging: {
    fetches: {
      fullUrl: process.env.NODE_ENV === "development",
    },
  },
};

export default nextConfig;
