import { logger } from "./lib/logger"

export async function register() {
  // This function runs when the Next.js server starts
  if (process.env.NODE_ENV === "development") {
    logger.info("instrumentation", "Development instrumentation enabled")
    
    // Enable additional debugging in development
    if (process.env.NEXT_PUBLIC_ENABLE_LOGGING === "true") {
      logger.info("instrumentation", "Enhanced logging enabled", {
        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || "debug",
        contexts: process.env.NEXT_PUBLIC_LOG_CONTEXTS || "all",
      })
    }
  }

  // Production instrumentation
  if (process.env.NODE_ENV === "production") {
    logger.info("instrumentation", "Production instrumentation enabled")
    
    // Initialize production monitoring
    if (process.env.NEXT_PUBLIC_ERROR_TRACKING_ENABLED === "true") {
      logger.info("instrumentation", "Error tracking enabled")
    }
  }
}
