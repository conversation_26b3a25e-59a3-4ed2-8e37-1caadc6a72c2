import { NextResponse } from "next/server"
import { getServerSession } from "@/lib/session"
import { signIn } from "next-auth/react"

/**
 * Link a social account to the current user
 * This endpoint initiates the OAuth flow for account linking
 */
export async function POST(req: Request) {
  try {
    const session = await getServerSession()
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { provider } = await req.json()

    if (!provider) {
      return NextResponse.json({ error: "Provider is required" }, { status: 400 })
    }

    // Validate provider
    const allowedProviders = ["google", "facebook"]
    if (!allowedProviders.includes(provider)) {
      return NextResponse.json({ error: "Invalid provider" }, { status: 400 })
    }

    // Check if provider is enabled
    const isProviderEnabled = 
      (provider === "google" && process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) ||
      (provider === "facebook" && process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET)

    if (!isProviderEnabled) {
      return NextResponse.json({ error: "Provider not available" }, { status: 400 })
    }

    // Return the OAuth URL for the client to redirect to
    // The actual linking will happen in the NextAuth callback
    return NextResponse.json({ 
      success: true,
      message: "Initiate OAuth flow on client side",
      provider 
    })

  } catch (error) {
    console.error("Account linking error:", error)
    return NextResponse.json({ error: "Failed to link account" }, { status: 500 })
  }
}
