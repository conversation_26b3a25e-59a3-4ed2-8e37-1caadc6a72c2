/**
 * Migration Script: Add Organization Lifecycle Management Tables
 *
 * This script adds the new tables for organization lifecycle management:
 * - OrganizationLifecycleEvent: Track admin deletion, grace periods, recovery
 * - AdminRecoveryRequest: Handle requests for admin access to orphaned organizations
 *
 * Run with: node scripts/migrate-lifecycle-tables.js
 */

const { neon } = require("@neondatabase/serverless");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config({ path: ".env.local" });

const sql = neon(process.env.DATABASE_URL);

async function migrateLifecycleTables() {
  console.log("🚀 Starting organization lifecycle tables migration...");

  try {
    // Create OrganizationLifecycleEvent table
    console.log("📝 Creating OrganizationLifecycleEvent table...");
    await sql`
      CREATE TABLE IF NOT EXISTS "OrganizationLifecycleEvent" (
        id TEXT PRIMARY KEY,
        "organizationId" TEXT NOT NULL REFERENCES "Organization"(id) ON DELETE CASCADE,
        "eventType" TEXT NOT NULL, -- 'admin_deleted' | 'grace_period_started' | 'grace_period_ended' | 'admin_recovered'
        "triggeredBy" TEXT NOT NULL, -- User ID who triggered the event
        "triggeredAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "gracePeriodEnds" TIMESTAMP, -- When grace period expires (for grace_period_started events)
        metadata TEXT, -- JSON metadata about the event
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    console.log("✅ Created OrganizationLifecycleEvent table");

    // Create indexes for OrganizationLifecycleEvent
    console.log("📝 Creating OrganizationLifecycleEvent indexes...");
    await sql`
      CREATE INDEX IF NOT EXISTS "lifecycle_event_organization_idx" ON "OrganizationLifecycleEvent"("organizationId")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "lifecycle_event_type_idx" ON "OrganizationLifecycleEvent"("eventType")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "lifecycle_event_triggered_at_idx" ON "OrganizationLifecycleEvent"("triggeredAt")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "lifecycle_event_grace_period_ends_idx" ON "OrganizationLifecycleEvent"("gracePeriodEnds")
    `;
    console.log("✅ Created OrganizationLifecycleEvent indexes");

    // Create AdminRecoveryRequest table
    console.log("📝 Creating AdminRecoveryRequest table...");
    await sql`
      CREATE TABLE IF NOT EXISTS "AdminRecoveryRequest" (
        id TEXT PRIMARY KEY,
        "organizationId" TEXT NOT NULL REFERENCES "Organization"(id) ON DELETE CASCADE,
        "requestedBy" TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
        "requestReason" TEXT NOT NULL, -- Why they need admin access
        status TEXT NOT NULL DEFAULT 'pending', -- 'pending' | 'approved' | 'rejected' | 'expired'
        "reviewedBy" TEXT, -- Platform admin who reviewed the request
        "reviewedAt" TIMESTAMP,
        "reviewNotes" TEXT, -- Admin notes about the decision
        "expiresAt" TIMESTAMP NOT NULL, -- When the request expires
        "createdAt" TIMESTAMP DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    console.log("✅ Created AdminRecoveryRequest table");

    // Create indexes for AdminRecoveryRequest
    console.log("📝 Creating AdminRecoveryRequest indexes...");
    await sql`
      CREATE INDEX IF NOT EXISTS "admin_recovery_organization_idx" ON "AdminRecoveryRequest"("organizationId")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "admin_recovery_requested_by_idx" ON "AdminRecoveryRequest"("requestedBy")
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "admin_recovery_status_idx" ON "AdminRecoveryRequest"(status)
    `;
    await sql`
      CREATE INDEX IF NOT EXISTS "admin_recovery_expires_at_idx" ON "AdminRecoveryRequest"("expiresAt")
    `;
    console.log("✅ Created AdminRecoveryRequest indexes");

    // Verify tables were created
    console.log("🔍 Verifying tables were created...");
    const lifecycleTableCheck = await sql`
      SELECT table_name FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'OrganizationLifecycleEvent'
    `;
    const recoveryTableCheck = await sql`
      SELECT table_name FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'AdminRecoveryRequest'
    `;

    if (lifecycleTableCheck.length > 0) {
      console.log("✅ OrganizationLifecycleEvent table verified");
    } else {
      console.log("❌ OrganizationLifecycleEvent table not found");
    }

    if (recoveryTableCheck.length > 0) {
      console.log("✅ AdminRecoveryRequest table verified");
    } else {
      console.log("❌ AdminRecoveryRequest table not found");
    }

    console.log(
      "🎉 Organization lifecycle tables migration completed successfully!"
    );
    console.log("");
    console.log("📋 Summary:");
    console.log("- ✅ OrganizationLifecycleEvent table created");
    console.log("- ✅ AdminRecoveryRequest table created");
    console.log("- ✅ All indexes created");
    console.log("");
    console.log("🔧 Next steps:");
    console.log("1. Test the lifecycle management functions");
    console.log("2. Integrate with admin dashboard");
    console.log("3. Add feature gates to organization UI");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
migrateLifecycleTables()
  .then(() => {
    console.log("✅ Migration script completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Migration script failed:", error);
    process.exit(1);
  });
