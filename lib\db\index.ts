/**
 * Database Connection and Main Exports
 *
 * This file provides the database connection and re-exports all schemas and functions
 * for backward compatibility with existing imports.
 */

import { neon } from "@neondatabase/serverless"
import { drizzle } from "drizzle-orm/neon-http"

// Export all schemas
export * from "./schema"

// Initialize the SQL client
const sql = neon(process.env.DATABASE_URL!)

// Initialize the Drizzle ORM
export const db = drizzle(sql)

// Re-export all functions from domain modules
// This ensures backward compatibility with existing imports like:
// import { getUserByEmail, createUser } from "@/lib/db"

// Domain modules (NEW - organized database functions)
export * from "./admin"
export * from "./auth"
export * from "./certificates"
export * from "./cleanup"
export * from "./lifecycle"
export * from "./notifications"
export * from "./organizations"
export * from "./users"

// All database functions have been successfully moved to domain modules
// The legacy db.ts file has been removed - all imports now use the organized structure

