/**
 * Organization Management Functions
 *
 * This module handles all organization-related database operations including:
 * - Organization CRUD operations
 * - Organization membership management
 * - Organization verification and status management
 * - Multi-tenant organization queries
 */

import { neon } from "@neondatabase/serverless"
import { and, count, desc, eq, inArray } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import { nanoid } from "nanoid"
import {
  organizationMemberships,
  organizations,
  users
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// BASIC ORGANIZATION OPERATIONS
// ============================================================================

/**
 * Get organization by ID
 */
export async function getOrganizationById(id: string) {
  try {
    const result = await db.select().from(organizations).where(eq(organizations.id, id))
    return result[0] || null
  } catch (error) {
    console.error("Error getting organization by ID:", error)
    return null
  }
}

/**
 * Create a new organization
 */
export async function createOrganization(organizationData: {
  id: string
  name: string
  type: "yacht_company" | "cert_provider"
  contactEmail: string
  description?: string
  website?: string
  status?: "pending" | "verified" | "suspended"
}) {
  try {
    const now = new Date()
    await db.insert(organizations).values({
      ...organizationData,
      status: organizationData.status || "pending", // Default to pending
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating organization:", error)
    throw error
  }
}

/**
 * Update organization information
 */
export async function updateOrganization(
  id: string,
  organizationData: Partial<{
    name: string
    type: "yacht_company" | "cert_provider"
    status: "pending" | "verified" | "suspended"
    contactEmail: string
    description: string | null
    website: string | null
    verifiedAt: Date
    verifiedBy: string
  }>
) {
  try {
    await db
      .update(organizations)
      .set({
        ...organizationData,
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, id))
    return { success: true }
  } catch (error) {
    console.error("Error updating organization:", error)
    throw error
  }
}

/**
 * Delete organization (soft delete by setting status to suspended)
 */
export async function deleteOrganization(id: string) {
  try {
    await db
      .update(organizations)
      .set({
        status: "suspended",
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, id))
    return { success: true }
  } catch (error) {
    console.error("Error deleting organization:", error)
    throw error
  }
}

/**
 * Get all organizations with optional filters
 */
export async function getOrganizations(options: {
  type?: "yacht_company" | "cert_provider"
  status?: "pending" | "verified" | "suspended"
  limit?: number
  offset?: number
} = {}) {
  try {
    const { type, status, limit, offset = 0 } = options

    // Build where conditions
    const conditions = []
    if (type) {
      conditions.push(eq(organizations.type, type))
    }
    if (status) {
      conditions.push(eq(organizations.status, status))
    }

    // Build query with all conditions at once to avoid type issues
    if (conditions.length > 0) {
      if (limit && offset > 0) {
        return await db.select().from(organizations)
          .where(and(...conditions))
          .orderBy(desc(organizations.createdAt))
          .limit(limit)
          .offset(offset)
      } else if (limit) {
        return await db.select().from(organizations)
          .where(and(...conditions))
          .orderBy(desc(organizations.createdAt))
          .limit(limit)
      } else if (offset > 0) {
        return await db.select().from(organizations)
          .where(and(...conditions))
          .orderBy(desc(organizations.createdAt))
          .offset(offset)
      } else {
        return await db.select().from(organizations)
          .where(and(...conditions))
          .orderBy(desc(organizations.createdAt))
      }
    } else {
      if (limit && offset > 0) {
        return await db.select().from(organizations)
          .orderBy(desc(organizations.createdAt))
          .limit(limit)
          .offset(offset)
      } else if (limit) {
        return await db.select().from(organizations)
          .orderBy(desc(organizations.createdAt))
          .limit(limit)
      } else if (offset > 0) {
        return await db.select().from(organizations)
          .orderBy(desc(organizations.createdAt))
          .offset(offset)
      } else {
        return await db.select().from(organizations)
          .orderBy(desc(organizations.createdAt))
      }
    }
  } catch (error) {
    console.error("Error getting organizations:", error)
    return []
  }
}

/**
 * Get all organizations with member counts for admin dashboard
 */
export async function getAllOrganizations(limit = 50) {
  try {
    // Get organizations with member counts
    const orgsWithMembers = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        type: organizations.type,
        status: organizations.status,
        contactEmail: organizations.contactEmail,
        description: organizations.description,
        website: organizations.website,
        verifiedAt: organizations.verifiedAt,
        verifiedBy: organizations.verifiedBy,
        createdAt: organizations.createdAt,
        memberCount: count(organizationMemberships.id),
      })
      .from(organizations)
      .leftJoin(
        organizationMemberships,
        eq(organizations.id, organizationMemberships.organizationId)
      )
      .groupBy(organizations.id)
      .orderBy(desc(organizations.createdAt))
      .limit(limit)

    return orgsWithMembers
  } catch (error) {
    console.error("Error getting all organizations:", error)
    return []
  }
}

// ============================================================================
// ORGANIZATION MEMBERSHIP MANAGEMENT
// ============================================================================

/**
 * Add user to organization
 */
export async function addUserToOrganization(
  userId: string,
  organizationId: string,
  role: "owner" | "admin" | "member" = "member",
  invitedBy?: string
) {
  try {
    const now = new Date()
    await db.insert(organizationMemberships).values({
      id: nanoid(),
      userId,
      organizationId,
      role,
      joinedAt: now,
      invitedBy,
      invitedAt: invitedBy ? now : null,
      acceptedAt: now,
      status: "active",
    })
    return { success: true }
  } catch (error) {
    console.error("Error adding user to organization:", error)
    throw error
  }
}

/**
 * Remove user from organization
 */
export async function removeUserFromOrganization(userId: string, organizationId: string) {
  try {
    await db
      .delete(organizationMemberships)
      .where(
        and(
          eq(organizationMemberships.userId, userId),
          eq(organizationMemberships.organizationId, organizationId)
        )
      )
    return { success: true }
  } catch (error) {
    console.error("Error removing user from organization:", error)
    throw error
  }
}

/**
 * Update user role in organization
 */
export async function updateUserRoleInOrganization(
  userId: string,
  organizationId: string,
  role: "owner" | "admin" | "member"
) {
  try {
    await db
      .update(organizationMemberships)
      .set({ role })
      .where(
        and(
          eq(organizationMemberships.userId, userId),
          eq(organizationMemberships.organizationId, organizationId)
        )
      )
    return { success: true }
  } catch (error) {
    console.error("Error updating user role in organization:", error)
    throw error
  }
}

/**
 * Get user's organizations
 */
export async function getUserOrganizations(userId: string) {
  try {
    const result = await db
      .select({
        organization: organizations,
        membership: organizationMemberships,
      })
      .from(organizationMemberships)
      .innerJoin(organizations, eq(organizationMemberships.organizationId, organizations.id))
      .where(
        and(
          eq(organizationMemberships.userId, userId),
          eq(organizationMemberships.status, "active")
        )
      )
      .orderBy(desc(organizationMemberships.joinedAt))

    return result.map(row => ({
      ...row.organization,
      role: row.membership.role,
      joinedAt: row.membership.joinedAt,
    }))
  } catch (error) {
    console.error("Error getting user organizations:", error)
    return []
  }
}

/**
 * Get organization members
 */
export async function getOrganizationMembers(organizationId: string) {
  try {
    const result = await db
      .select({
        user: users,
        membership: organizationMemberships,
      })
      .from(organizationMemberships)
      .innerJoin(users, eq(organizationMemberships.userId, users.id))
      .where(
        and(
          eq(organizationMemberships.organizationId, organizationId),
          eq(organizationMemberships.status, "active")
        )
      )
      .orderBy(desc(organizationMemberships.joinedAt))

    return result.map(row => ({
      ...row.user,
      role: row.membership.role,
      joinedAt: row.membership.joinedAt,
      invitedBy: row.membership.invitedBy,
    }))
  } catch (error) {
    console.error("Error getting organization members:", error)
    return []
  }
}

/**
 * Check if user is member of organization
 */
export async function isUserMemberOfOrganization(userId: string, organizationId: string) {
  try {
    const result = await db
      .select({ role: organizationMemberships.role })
      .from(organizationMemberships)
      .where(
        and(
          eq(organizationMemberships.userId, userId),
          eq(organizationMemberships.organizationId, organizationId),
          eq(organizationMemberships.status, "active")
        )
      )
      .limit(1)

    return result[0] || null
  } catch (error) {
    console.error("Error checking organization membership:", error)
    return null
  }
}

/**
 * Get user's role in organization
 */
export async function getUserRoleInOrganization(userId: string, organizationId: string) {
  try {
    const membership = await isUserMemberOfOrganization(userId, organizationId)
    return membership?.role || null
  } catch (error) {
    console.error("Error getting user role in organization:", error)
    return null
  }
}

// ============================================================================
// ORGANIZATION VERIFICATION AND STATUS MANAGEMENT
// ============================================================================

/**
 * Verify an organization
 */
export async function verifyOrganization(organizationId: string, verifiedBy: string) {
  try {
    await db
      .update(organizations)
      .set({
        status: "verified",
        verifiedAt: new Date(),
        verifiedBy,
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId))
    return { success: true }
  } catch (error) {
    console.error("Error verifying organization:", error)
    throw error
  }
}

/**
 * Suspend an organization
 */
export async function suspendOrganization(organizationId: string) {
  try {
    await db
      .update(organizations)
      .set({
        status: "suspended",
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId))
    return { success: true }
  } catch (error) {
    console.error("Error suspending organization:", error)
    throw error
  }
}

/**
 * Reactivate a suspended organization
 */
export async function reactivateOrganization(organizationId: string) {
  try {
    await db
      .update(organizations)
      .set({
        status: "pending", // Reset to pending for re-verification
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId))
    return { success: true }
  } catch (error) {
    console.error("Error reactivating organization:", error)
    throw error
  }
}

/**
 * Get organizations by status
 */
export async function getOrganizationsByStatus(status: "pending" | "verified" | "suspended") {
  try {
    return await db
      .select()
      .from(organizations)
      .where(eq(organizations.status, status))
      .orderBy(desc(organizations.createdAt))
  } catch (error) {
    console.error("Error getting organizations by status:", error)
    return []
  }
}

/**
 * Get pending verification organizations
 */
export async function getPendingVerificationOrganizations() {
  try {
    return await getOrganizationsByStatus("pending")
  } catch (error) {
    console.error("Error getting pending verification organizations:", error)
    return []
  }
}

// ============================================================================
// ORGANIZATION STATISTICS AND ADMIN QUERIES
// ============================================================================

/**
 * Get organization statistics
 */
export async function getOrganizationStats() {
  try {
    const totalOrgs = await db.select({ count: count() }).from(organizations)
    const pendingOrgs = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.status, "pending"))
    const verifiedOrgs = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.status, "verified"))
    const suspendedOrgs = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.status, "suspended"))

    const yachtCompanies = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.type, "yacht_company"))
    const certProviders = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.type, "cert_provider"))

    return {
      total: totalOrgs[0].count,
      pending: pendingOrgs[0].count,
      verified: verifiedOrgs[0].count,
      suspended: suspendedOrgs[0].count,
      yachtCompanies: yachtCompanies[0].count,
      certProviders: certProviders[0].count,
    }
  } catch (error) {
    console.error("Error getting organization stats:", error)
    return {
      total: 0,
      pending: 0,
      verified: 0,
      suspended: 0,
      yachtCompanies: 0,
      certProviders: 0,
    }
  }
}

/**
 * Get organizations with admin users (owners/admins)
 */
export async function getOrganizationsWithAdmins() {
  try {
    const result = await db
      .select({
        organization: organizations,
        adminCount: count(organizationMemberships.id),
      })
      .from(organizations)
      .leftJoin(
        organizationMemberships,
        and(
          eq(organizations.id, organizationMemberships.organizationId),
          inArray(organizationMemberships.role, ["owner", "admin"]),
          eq(organizationMemberships.status, "active")
        )
      )
      .groupBy(organizations.id)
      .orderBy(desc(organizations.createdAt))

    return result
  } catch (error) {
    console.error("Error getting organizations with admins:", error)
    return []
  }
}

/**
 * Get orphaned organizations (no active admin/owner)
 */
export async function getOrphanedOrganizations() {
  try {
    const result = await db
      .select({
        organization: organizations,
        adminCount: count(organizationMemberships.id),
      })
      .from(organizations)
      .leftJoin(
        organizationMemberships,
        and(
          eq(organizations.id, organizationMemberships.organizationId),
          inArray(organizationMemberships.role, ["owner", "admin"]),
          eq(organizationMemberships.status, "active")
        )
      )
      .groupBy(organizations.id)
      .having(eq(count(organizationMemberships.id), 0))
      .orderBy(desc(organizations.createdAt))

    return result.map(row => row.organization)
  } catch (error) {
    console.error("Error getting orphaned organizations:", error)
    return []
  }
}
