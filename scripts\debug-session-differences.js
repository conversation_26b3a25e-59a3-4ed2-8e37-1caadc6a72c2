#!/usr/bin/env node

/**
 * Debug script to compare session data between OAuth and credential logins
 * This will help identify why OAuth users experience different behavior
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 Debugging Session Differences Between OAuth and Credentials...\n');

async function testSessionAPI() {
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('1️⃣ Testing session API directly...');
    
    // Test session API without any authentication
    const sessionResponse = await fetch(`${baseUrl}/api/auth/session`, {
      credentials: 'include'
    });
    
    console.log(`   Session API Status: ${sessionResponse.status}`);
    
    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json();
      console.log('   Session Data:', JSON.stringify(sessionData, null, 2));
    } else {
      const errorText = await sessionResponse.text();
      console.log('   Session Error:', errorText);
    }
    
    console.log('\n2️⃣ Testing certificates API...');
    
    // Test certificates API
    const certsResponse = await fetch(`${baseUrl}/api/certificates`, {
      credentials: 'include'
    });
    
    console.log(`   Certificates API Status: ${certsResponse.status}`);
    
    if (!certsResponse.ok) {
      const errorText = await certsResponse.text();
      console.log('   Certificates Error:', errorText);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Check server status
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/session');
    return true;
  } catch (error) {
    return false;
  }
}

// Analyze the authentication flow differences
function analyzeAuthFlow() {
  console.log('\n3️⃣ Analyzing authentication flow differences...\n');
  
  console.log('📋 Credential Login Flow:');
  console.log('========================');
  console.log('1. User submits email/password to /api/login');
  console.log('2. /api/login validates credentials against database');
  console.log('3. /api/login creates custom session cookie with full user data');
  console.log('4. Session includes: id, email, name, role, subscriptionPlan, tenantId, tenantRole');
  console.log('5. Middleware and APIs use custom session cookie');
  
  console.log('\n📋 OAuth Login Flow:');
  console.log('===================');
  console.log('1. User initiates OAuth with Google');
  console.log('2. NextAuth signIn callback processes OAuth data');
  console.log('3. signIn callback updates user object with role/subscriptionPlan');
  console.log('4. NextAuth jwt callback stores user data in JWT token');
  console.log('5. NextAuth session callback creates custom session cookie');
  console.log('6. Session API prioritizes NextAuth session over custom session');
  
  console.log('\n🔍 Potential Issues:');
  console.log('====================');
  
  // Check if the session callback is properly creating custom cookies
  try {
    const authContent = fs.readFileSync('lib/auth.ts', 'utf8');
    
    if (authContent.includes('cookieStore.set')) {
      console.log('✅ Custom session cookie creation is implemented in NextAuth session callback');
    } else {
      console.log('❌ Custom session cookie creation missing in NextAuth session callback');
    }
    
    if (authContent.includes('token.role as string')) {
      console.log('✅ Role is being transferred from JWT token to session');
    } else {
      console.log('❌ Role transfer from JWT token to session missing');
    }
    
    if (authContent.includes('user.role = existingUser.role')) {
      console.log('✅ OAuth signIn callback updates user role from database');
    } else {
      console.log('❌ OAuth signIn callback not updating user role from database');
    }
    
  } catch (error) {
    console.log('❌ Error reading auth configuration:', error.message);
  }
  
  // Check session API priority
  try {
    const sessionApiContent = fs.readFileSync('app/api/auth/session/route.ts', 'utf8');
    
    if (sessionApiContent.includes('getNextAuthSession(authOptions)')) {
      console.log('✅ Session API checks NextAuth session first');
    } else {
      console.log('❌ Session API not checking NextAuth session');
    }
    
    if (sessionApiContent.includes('|| "individual_user"')) {
      console.log('✅ Session API provides default role for NextAuth sessions');
    } else {
      console.log('❌ Session API missing default role for NextAuth sessions');
    }
    
  } catch (error) {
    console.log('❌ Error reading session API:', error.message);
  }
  
  // Check certificates API authentication
  try {
    const certsApiContent = fs.readFileSync('app/api/certificates/route.ts', 'utf8');
    
    if (certsApiContent.includes('getUserFromSession')) {
      console.log('✅ Certificates API uses custom session parsing');
    } else {
      console.log('❌ Certificates API not using session parsing');
    }
    
    if (certsApiContent.includes('sessionCookie.get("session")')) {
      console.log('✅ Certificates API looks for custom session cookie');
    } else {
      console.log('❌ Certificates API not looking for custom session cookie');
    }
    
  } catch (error) {
    console.log('❌ Error reading certificates API:', error.message);
  }
}

async function runDiagnostics() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Development server not running. Please start with: pnpm dev');
    console.log('   Some tests will be skipped.\n');
  } else {
    console.log('✅ Development server is running\n');
    await testSessionAPI();
  }
  
  analyzeAuthFlow();
  
  console.log('\n🎯 Key Differences to Investigate:');
  console.log('==================================');
  console.log('1. Session Data Structure: OAuth vs Credentials');
  console.log('2. Custom Session Cookie Creation: Is it working for OAuth?');
  console.log('3. API Authentication: Which session type is being used?');
  console.log('4. Role/Permission Data: Is it properly transferred?');
  
  console.log('\n🧪 Manual Testing Steps:');
  console.log('========================');
  console.log('1. Login with credentials → Check browser cookies');
  console.log('2. Login with OAuth → Check browser cookies');
  console.log('3. Compare session cookie contents');
  console.log('4. Test /api/auth/session response for both');
  console.log('5. Test /api/certificates response for both');
  console.log('6. Check browser Network tab for failed requests');
  
  console.log('\n💡 Likely Root Cause:');
  console.log('=====================');
  console.log('The NextAuth session callback may not be properly creating');
  console.log('the custom session cookie, or the session data structure');
  console.log('differs between OAuth and credential logins, causing API');
  console.log('authentication to fail for OAuth users.');
}

runDiagnostics();
