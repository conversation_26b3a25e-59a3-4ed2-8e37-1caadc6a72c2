"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>heck,
  FileWarning,
  Plus,
  Ship,
  Waves,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { logger } from "@/lib/logger";

export default function DashboardPage() {
  const router = useRouter();
  const [userName, setUserName] = useState("User");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDashboard = async () => {
      logger.componentMount("DashboardPage");
      const endTimer = logger.authStart("dashboard-auth-check");

      try {
        logger.info("component", "Dashboard loading started");

        // Test API call to verify authentication
        const apiEndTimer = logger.apiRequest("GET", "/api/certificates");
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });
        apiEndTimer();

        logger.apiResponse("GET", "/api/certificates", response.status);

        if (!response.ok) {
          if (response.status === 401) {
            logger.authFailure(
              "dashboard-auth-check",
              "Unauthorized - redirecting to login"
            );
            logger.navigationStart("/dashboard", "/login");
            router.push("/login");
            return;
          }
          throw new Error(
            `Failed to verify authentication: ${response.status}`
          );
        }

        logger.authSuccess("dashboard-auth-check", "authenticated-user");

        // Get user info from the profile API
        try {
          const profileResponse = await fetch("/api/user/profile", {
            credentials: "include",
          });

          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            if (profileData.success) {
              setUserName(profileData.user.name);
            } else {
              setUserName("User");
            }
          } else {
            setUserName("User");
          }
        } catch (profileError) {
          console.error("Failed to fetch user profile:", profileError);
          setUserName("User");
        }
        setIsLoading(false);

        logger.info("component", "Dashboard loaded successfully");
        endTimer();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        logger.error("component", "Dashboard loading failed", {
          error: errorMessage,
        });
        setError("Failed to load dashboard. Please try again.");
        setIsLoading(false);
        endTimer();
      }
    };

    loadDashboard();
  }, [router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-64px)]">
        <div className="space-y-4 text-center">
          <Waves className="h-12 w-12 animate-pulse text-primary mx-auto" />
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  // Sample data for demonstration
  const recentCertificates = [
    {
      id: "1",
      name: "STCW Basic Safety Training",
      expiryDate: new Date("2025-01-15"),
      status: "active",
    },
    {
      id: "2",
      name: "Medical First Aid",
      expiryDate: new Date("2024-08-22"),
      status: "warning",
    },
  ];

  const upcomingRenewals = [
    {
      id: "2",
      name: "Medical First Aid",
      expiryDate: new Date("2024-08-22"),
      daysLeft: 60,
    },
    {
      id: "5",
      name: "GMDSS Radio Operator",
      expiryDate: new Date("2024-06-15"),
      daysLeft: 15,
    },
  ];

  return (
    <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome, {userName}!
          </h1>
          <p className="text-muted-foreground">
            Here's an overview of your certification status
          </p>
        </div>
        <Button asChild>
          <Link href="/certificates/new">
            <Plus className="mr-2 h-4 w-4" /> Add Certificate
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="border-l-4 border-l-amber-500">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Clock className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Certificates expiring in the next 90 days
            </p>
            <div className="mt-4">
              <div className="flex justify-between text-xs mb-1">
                <span>30 days</span>
                <span>60 days</span>
                <span>90 days</span>
              </div>
              <Progress value={33} className="h-2" />
            </div>
          </CardContent>
          <CardFooter className="p-2">
            <Button
              variant="link"
              size="sm"
              className="px-0 w-full justify-start"
              asChild
            >
              <Link href="/certificates?filter=expiring-soon">
                View expiring certificates
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Active Certificates
            </CardTitle>
            <FileCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              Currently valid certificates in your account
            </p>
            <div className="mt-4 flex items-center gap-2">
              <Ship className="h-4 w-4 text-green-500" />
              <span className="text-xs">
                All required certificates are valid
              </span>
            </div>
          </CardContent>
          <CardFooter className="p-2">
            <Button
              variant="link"
              size="sm"
              className="px-0 w-full justify-start"
              asChild
            >
              <Link href="/certificates">View all certificates</Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <FileWarning className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">
              Certificates that have expired
            </p>
            <div className="mt-4 flex items-center gap-2">
              <Calendar className="h-4 w-4 text-red-500" />
              <span className="text-xs text-red-500">
                Ship Security Officer expired 10 days ago
              </span>
            </div>
          </CardContent>
          <CardFooter className="p-2">
            <Button
              variant="link"
              size="sm"
              className="px-0 w-full justify-start"
              asChild
            >
              <Link href="/certificates?filter=expired">
                View expired certificates
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <Tabs defaultValue="upcoming" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-auto">
          <TabsTrigger value="upcoming">Upcoming Renewals</TabsTrigger>
          <TabsTrigger value="recent">Recent Certificates</TabsTrigger>
        </TabsList>
        <TabsContent value="upcoming" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Certificates Requiring Renewal
              </CardTitle>
              <CardDescription>
                Certificates that will expire in the next 90 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingRenewals.map((cert) => (
                  <div
                    key={cert.id}
                    className="flex items-center justify-between border-b pb-3"
                  >
                    <div>
                      <p className="font-medium">{cert.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Expires: {cert.expiryDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={`text-sm ${
                          cert.daysLeft <= 30
                            ? "text-red-500"
                            : "text-amber-500"
                        }`}
                      >
                        {cert.daysLeft} days left
                      </div>
                      <Button size="sm" asChild>
                        <Link href={`/certificates/${cert.id}/renew`}>
                          Renew
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="recent" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Recently Added Certificates
              </CardTitle>
              <CardDescription>
                Certificates you've added in the last 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentCertificates.map((cert) => (
                  <div
                    key={cert.id}
                    className="flex items-center justify-between border-b pb-3"
                  >
                    <div>
                      <p className="font-medium">{cert.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Expires: {cert.expiryDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={`px-2 py-1 rounded-full text-xs ${
                          cert.status === "active"
                            ? "bg-green-100 text-green-800"
                            : "bg-amber-100 text-amber-800"
                        }`}
                      >
                        {cert.status === "active" ? "Active" : "Expiring Soon"}
                      </div>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/certificates/${cert.id}`}>View</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Certificate Status Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1 text-sm">
                <span>Active</span>
                <span>5 (83%)</span>
              </div>
              <Progress
                value={83}
                className="h-2 bg-muted"
                indicatorColor="bg-green-500"
              />
            </div>
            <div>
              <div className="flex justify-between mb-1 text-sm">
                <span>Expiring Soon</span>
                <span>2 (17%)</span>
              </div>
              <Progress
                value={17}
                className="h-2 bg-muted"
                indicatorColor="bg-amber-500"
              />
            </div>
            <div>
              <div className="flex justify-between mb-1 text-sm">
                <span>Expired</span>
                <span>1 (8%)</span>
              </div>
              <Progress
                value={8}
                className="h-2 bg-muted"
                indicatorColor="bg-red-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
