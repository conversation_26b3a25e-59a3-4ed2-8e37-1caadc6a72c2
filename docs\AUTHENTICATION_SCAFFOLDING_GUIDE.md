# 🔐 Authentication Architecture Guide

**SINGLE SOURCE OF TRUTH** for Sealog authentication system architecture.

This document clarifies what is currently implemented vs what is scaffolding, and provides clear guidance for future implementation.

## 🚨 CRITICAL: What's Actually Implemented vs Scaffolding

**Last Updated**: After comprehensive audit and cleanup (no real users, database can be reset)

### ✅ **ACTUALLY IMPLEMENTED (Active Business Logic)**

#### Core Authentication
- **User Roles**: Only `individual_user` and `system_admin`
- **Login Flow**: Redirects system admins to `/admin`, all others to `/dashboard`
- **Route Protection**: Basic authentication + role-based access for admin routes
- **Session Management**: Simple session cookies with user info
- **Email Verification**: Complete email verification flow

#### Organization System
- **Organization Types**: `yacht_company` and `cert_provider` (as organization properties, NOT user roles)
- **Organization Membership**: Users belong to organizations with `admin`/`member` roles
- **Organization Context Switching**: Users can switch between personal and organization contexts
- **Organization Creation**: Card-based selection flow with type-specific forms

#### Database Schema
- **Users Table**: Simple role field (`individual_user` | `system_admin`) - tenant fields removed
- **Organizations Table**: Organization types and verification status
- **OrganizationMembership Table**: Simple admin/member roles (placeholder)
- **Certificates Table**: Personal certificate management
- **No Complex Tables**: No permission tables, subscription tables, or tenant isolation tables

### ⚠️ **SCAFFOLDING ONLY (Not Used in Business Logic)**

#### Complex Permission System
- **Permission Enum**: Detailed permissions like `CERTIFICATES_READ`, `TENANT_ADMIN`, etc.
- **Permission Mappings**: `individualPlanFeatures`, `providerPlanFeatures`, `companyPlanFeatures`
- **Permission Checks**: `hasPermission()`, `validateRouteAccess()` functions exist but are simplified
- **Route Permissions**: Route configs have permission fields but they're not enforced

#### Subscription System
- **Subscription Plans**: Complex plan enums for different organization types
- **Plan-Based Access**: Subscription plan checking logic exists but returns simplified results
- **Subscription Enforcement**: No actual subscription restrictions implemented

#### Tenant System (Removed)
- **Tenant Fields**: Completely removed from database schema and session context
- **Tenant Logic**: All tenant-related code removed or commented out
- **Multi-Tenant Isolation**: Fully replaced by organization membership approach

## 🎯 **Current Business Logic (What Actually Works)**

### Authentication Flow
```typescript
// Login API (app/api/login/route.ts)
if (user.role === "system_admin") {
  redirectUrl = "/admin"  // Only system admins
} else {
  redirectUrl = "/dashboard"  // All other users
}
```

### Route Protection
```typescript
// Middleware (middleware.ts)
if (routeConfig.allowedRoles && !routeConfig.allowedRoles.includes(userRole)) {
  return NextResponse.redirect("/dashboard?error=unauthorized")
}
```

### API Protection
```typescript
// Admin API routes
if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
}
```

### Organization Management
```typescript
// Organization membership via OrganizationMembership table
// Users can be admin/member of multiple organizations
// Organization context switching in UI components
```

## ⚠️ **Important: Two Separate Access Control Systems**

### 1. **User-Based Permissions** (Scaffolding)
- **File**: `lib/auth-config.ts`
- **Purpose**: Individual user permissions based on roles and subscription plans
- **Status**: SCAFFOLDING ONLY - not used in business logic
- **Usage**: Don't use for actual access control

### 2. **Organization-Based Feature Gates** (Active)
- **File**: `lib/feature-access-control.ts`
- **Purpose**: Organization feature access based on verification status
- **Status**: ACTIVELY USED - controls organization features
- **Usage**: Use this for organization-specific feature control

**CRITICAL**: These are separate systems. Don't confuse user permissions with organization feature gates.

## 🔧 **How Scaffolding is Maintained**

### Permission System
- Functions exist but return simplified results
- All individual users get basic certificate permissions
- System admins get all permissions
- No subscription-based restrictions

### Session Context
- Contains permission arrays but they're populated simply
- Tenant fields exist but are marked as legacy
- Subscription plan stored but not enforced

### Route Configuration
- Permission and subscription fields exist in route configs
- `hasRouteAccess()` function ignores complex checks
- Only basic auth and role checks are enforced

## 📋 **Implementation Guidelines**

### ✅ **DO (Safe to Use)**
- Use `UserRole.INDIVIDUAL_USER` and `UserRole.SYSTEM_ADMIN`
- Use organization membership for organization-specific features
- Use simple role-based route protection
- Use organization context switching
- Use verification status for organization features

### ❌ **DON'T (Scaffolding Only)**
- Don't implement complex permission-based business logic
- Don't rely on subscription plan enforcement
- Don't use tenant-based multi-tenancy
- Don't implement detailed permission mappings
- Don't build features requiring complex RBAC

### 🔄 **WHEN TO CONVERT SCAFFOLDING**
Only convert scaffolding to active business logic when:
1. User explicitly requests subscription/permission features
2. Business requirements are clearly defined
3. Database migrations are planned
4. Testing strategy is in place

## 🗂️ **File-by-File Status**

### Active Business Logic Files
- `app/api/login/route.ts` - ✅ Simple role-based redirects
- `middleware.ts` - ✅ Basic auth + admin role protection
- `app/api/admin/*` - ✅ System admin role checks
- `lib/db/organizations.ts` - ✅ Organization membership
- `components/organization-context-switcher.tsx` - ✅ Context switching

### Scaffolding Files (Use Carefully)
- `lib/auth-config.ts` - ⚠️ Complex permissions/subscriptions are scaffolding
- `lib/session.ts` - ⚠️ Permission functions simplified
- `docs/RBAC_IMPLEMENTATION_GUIDE.md` - ⚠️ Future implementation guide
- `lib/feature-access-control.ts` - ⚠️ Organization feature gates (separate system)

## 🚀 **Next Steps for Future Implementation**

When ready to implement full RBAC:
1. Review business requirements for permission granularity
2. Design subscription plan business logic
3. Plan database migrations for permission tables
4. Update session management to populate real permissions
5. Enable complex route validation in middleware
6. Add component-level permission checks
7. Implement subscription enforcement

## 🔍 **How to Identify Scaffolding**

Look for these indicators:
- Comments mentioning "scaffolding", "future", "not implemented"
- Functions that return simplified/hardcoded results
- Complex enums/types that aren't used in business logic
- TODO comments about RBAC implementation
- Commented-out complex logic in middleware
