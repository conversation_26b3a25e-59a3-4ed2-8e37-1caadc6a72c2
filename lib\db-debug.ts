import { logger } from "@/lib/logger"

export interface DbQueryDebugInfo {
  query: string
  params?: any[]
  duration?: number
  rowCount?: number
  error?: string
  timestamp: string
  requestId?: string
}

export class DatabaseDebugger {
  private static queries: DbQueryDebugInfo[] = []
  private static maxQueries = 100
  private static isEnabled = process.env.NODE_ENV === 'development' ||
    process.env.NEXT_PUBLIC_DB_DEBUG_ENABLED === 'true'

  static logQuery(info: Omit<DbQueryDebugInfo, 'timestamp'>) {
    if (!this.isEnabled) return

    const debugInfo: DbQueryDebugInfo = {
      ...info,
      timestamp: new Date().toISOString(),
    }

    // Add to query history
    this.queries.push(debugInfo)
    if (this.queries.length > this.maxQueries) {
      this.queries.shift()
    }

    // Log the query
    const logLevel = info.error ? 'error' : info.duration && info.duration > 1000 ? 'warn' : 'debug'
    logger[logLevel]('database', `DB Query: ${info.query.substring(0, 100)}...`, {
      fullQuery: info.query,
      params: info.params,
      duration: info.duration ? `${info.duration}ms` : undefined,
      rowCount: info.rowCount,
      error: info.error,
      requestId: info.requestId,
    })
  }

  static getQueryHistory(): DbQueryDebugInfo[] {
    return [...this.queries]
  }

  static getSlowQueries(thresholdMs: number = 1000): DbQueryDebugInfo[] {
    return this.queries.filter(q => q.duration && q.duration > thresholdMs)
  }

  static clearHistory() {
    this.queries.length = 0
  }

  static getQueryStats() {
    const total = this.queries.length
    const withDuration = this.queries.filter(q => q.duration !== undefined)
    const errors = this.queries.filter(q => q.error)

    if (withDuration.length === 0) {
      return { total, errors: errors.length, avgDuration: 0, slowQueries: 0 }
    }

    const durations = withDuration.map(q => q.duration!).sort((a, b) => a - b)
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
    const slowQueries = durations.filter(d => d > 1000).length

    return {
      total,
      errors: errors.length,
      avgDuration: Math.round(avgDuration),
      slowQueries,
      p95: durations[Math.floor(durations.length * 0.95)] || 0,
    }
  }
}

// Wrapper for Drizzle queries with debugging
export function withDbDebug<T>(
  queryFn: () => Promise<T>,
  queryInfo: { query: string; params?: any[]; requestId?: string }
): Promise<T> {
  if (!DatabaseDebugger['isEnabled']) {
    return queryFn()
  }

  const startTime = Date.now()

  return queryFn()
    .then(result => {
      const duration = Date.now() - startTime
      const rowCount = Array.isArray(result) ? result.length : 1

      DatabaseDebugger.logQuery({
        ...queryInfo,
        duration,
        rowCount,
      })

      return result
    })
    .catch(error => {
      const duration = Date.now() - startTime

      DatabaseDebugger.logQuery({
        ...queryInfo,
        duration,
        error: error.message,
      })

      throw error
    })
}

// Connection pool monitoring
export class ConnectionPoolMonitor {
  private static stats = {
    activeConnections: 0,
    totalConnections: 0,
    errors: 0,
    lastError: null as string | null,
  }

  static recordConnection() {
    this.stats.activeConnections++
    this.stats.totalConnections++

    logger.debug('database', 'Connection opened', {
      active: this.stats.activeConnections,
      total: this.stats.totalConnections,
    })
  }

  static recordDisconnection() {
    this.stats.activeConnections = Math.max(0, this.stats.activeConnections - 1)

    logger.debug('database', 'Connection closed', {
      active: this.stats.activeConnections,
    })
  }

  static recordError(error: string) {
    this.stats.errors++
    this.stats.lastError = error

    logger.error('database', 'Connection error', {
      error,
      totalErrors: this.stats.errors,
    })
  }

  static getStats() {
    return { ...this.stats }
  }

  static reset() {
    this.stats = {
      activeConnections: 0,
      totalConnections: 0,
      errors: 0,
      lastError: null,
    }
  }
}

// Database health checker
export class DatabaseHealthChecker {
  static async checkHealth(): Promise<{
    healthy: boolean
    latency?: number
    error?: string
    timestamp: string
  }> {
    const startTime = Date.now()

    try {
      // Import here to avoid circular dependencies
      const { neon } = await import("@neondatabase/serverless")
      const sql = neon(process.env.DATABASE_URL!)

      await sql`SELECT 1 as health_check`

      const latency = Date.now() - startTime

      logger.info('database', 'Health check passed', { latency: `${latency}ms` })

      return {
        healthy: true,
        latency,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      const latency = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error('database', 'Health check failed', {
        error: errorMessage,
        latency: `${latency}ms`,
      })

      return {
        healthy: false,
        latency,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      }
    }
  }

  static async startHealthMonitoring(intervalMs: number = 60000) {
    const interval = setInterval(async () => {
      await this.checkHealth()
    }, intervalMs)

    return () => clearInterval(interval)
  }
}


