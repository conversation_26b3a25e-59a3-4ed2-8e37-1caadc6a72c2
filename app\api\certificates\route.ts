import { AuthenticationError, ValidationError, handleApiError } from "@/lib/api-error-handler"
import { createCertificate, createCertificateWithFiles, getCertificatesWithFilters } from "@/lib/db"
import { nanoid } from "nanoid"
import { cookies } from "next/headers"
import { NextRequest, NextResponse } from "next/server"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      console.log("Certificates API: No session cookie found")
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    console.log("Certificates API: Session found", {
      userId: session.user?.id,
      email: session.user?.email,
      role: session.user?.role,
      subscriptionPlan: session.user?.subscriptionPlan
    })
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates - Fetch certificates with filters
export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      throw new AuthenticationError();
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const filter = searchParams.get("filter") as "all" | "favorites" | "expiring-soon" | "expired" || "all"
    const sortBy = searchParams.get("sortBy") as "name" | "dateIssued" | "expiryDate" || "expiryDate"
    const sortOrder = searchParams.get("sortOrder") as "asc" | "desc" || "asc"
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : undefined
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0

    const certificates = await getCertificatesWithFilters(user.id, {
      search,
      filter,
      sortBy,
      sortOrder,
      limit,
      offset,
    })

    // Transform dates to ISO strings for JSON serialization
    const serializedCertificates = certificates.map(cert => ({
      ...cert,
      dateIssued: cert.dateIssued.toISOString(),
      expiryDate: cert.expiryDate?.toISOString() || null,
      createdAt: cert.createdAt.toISOString(),
      updatedAt: cert.updatedAt.toISOString(),
    }))

    return NextResponse.json(serializedCertificates)
  } catch (error) {
    return handleApiError(error, 'certificates-get');
  }
}

// POST /api/certificates - Create a new certificate
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      throw new AuthenticationError();
    }

    const body = await request.json()

    // Validate the certificate data
    const certificateData = {
      name: body.name,
      issuingAuthority: body.issuingAuthority,
      certificateNumber: body.certificateNumber,
      dateIssued: body.dateIssued,
      expiryDate: body.expiryDate,
      notes: body.notes,
      isFavorite: body.isFavorite || false
    };

    // Basic validation (we'll use Zod schema validation when imports work)
    if (!certificateData.name || !certificateData.issuingAuthority ||
      !certificateData.certificateNumber || !certificateData.dateIssued) {
      throw new ValidationError("Missing required fields: name, issuingAuthority, certificateNumber, and dateIssued are required");
    }

    const {
      documentUrl,
      documentName,
      documentSize,
      documentType,
      files = [], // New field for multiple files
    } = body

    const certificateId = nanoid()
    const finalCertificateData = {
      id: certificateId,
      name: certificateData.name,
      issuingAuthority: certificateData.issuingAuthority,
      certificateNumber: certificateData.certificateNumber,
      dateIssued: new Date(certificateData.dateIssued),
      expiryDate: certificateData.expiryDate ? new Date(certificateData.expiryDate) : null,
      notes: certificateData.notes || null,
      isFavorite: certificateData.isFavorite,
      userId: user.id,
    }

    // Check if we have multiple files or legacy single file
    if (files && files.length > 0) {
      // Use new multi-file approach
      const fileRecords = files.map((file: any) => ({
        id: nanoid(),
        fileName: file.fileName,
        fileUrl: file.fileUrl,
        fileSize: file.fileSize,
        fileType: file.fileType,
        uploadthingKey: file.uploadthingKey,
      }))

      await createCertificateWithFiles(finalCertificateData, fileRecords)
    } else {
      // Use legacy single file approach for backward compatibility
      const legacyCertificateData = {
        ...finalCertificateData,
        documentUrl: documentUrl || null,
        documentName: documentName || null,
        documentSize: documentSize || null,
        documentType: documentType || null,
      }
      await createCertificate(legacyCertificateData)
    }

    return NextResponse.json(
      { message: "Certificate created successfully", id: finalCertificateData.id },
      { status: 201 }
    )
  } catch (error) {
    return handleApiError(error, 'certificates-create');
  }
}
