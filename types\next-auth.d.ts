import type { DefaultSession } from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: string
      subscriptionPlan: string
      emailVerified: boolean
      tenantId?: string
      tenantRole?: string
    } & DefaultSession["user"]
  }

  interface User {
    id: string
    name: string
    email: string
    role: string
    subscriptionPlan: string
    emailVerified: boolean
    tenantId?: string
    tenantRole?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role: string
    subscriptionPlan: string
    emailVerified: boolean
    tenantId?: string
    tenantRole?: string
  }
}
