# Production Deployment Guide - Sealog Maritime Platform

## 🚀 Admin User Setup for Production

### **Overview**
The Sealog Maritime Platform includes a secure admin user seeding system for production deployment. This guide covers how to properly set up the initial system administrator account.

### **Development vs Production Admin Setup**

#### **Development Environment (Current)**
- **Default Admin Email**: `<EMAIL>`
- **Default Password**: `AdminSealog2025!`
- **Usage**: For local development and testing only
- **Security**: Acceptable for localhost development

#### **Production Environment (Recommended)**
- **Custom Admin Email**: Use your company domain
- **Strong Password**: 20+ character secure password
- **Environment Variables**: Set via production environment
- **Security**: Follow enterprise security practices

---

## 🔧 Production Deployment Steps

### **Step 1: Environment Configuration**

Set these environment variables in your production environment:

```bash
# Admin User Configuration (Production)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="YourSecurePassword123!@#"
ADMIN_NAME="Your Full Name"

# Required Database Configuration
DATABASE_URL="postgresql://user:password@host:port/database"

# Other required environment variables
NEXTAUTH_SECRET="your-secure-nextauth-secret"
NEXTAUTH_URL="https://yourdomain.com"
RESEND_API_KEY="your-resend-api-key"
FROM_EMAIL="<EMAIL>"
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
CRON_SECRET="your-secure-cron-secret"
```

### **Step 2: Database Setup**

1. **Create Production Database**
   - Set up PostgreSQL database (Neon, Supabase, AWS RDS, etc.)
   - Ensure SSL is enabled
   - Configure connection pooling if needed

2. **Run Database Migrations**
   ```bash
   # The application will auto-create tables on first run
   # Or run manual migrations if needed
   ```

### **Step 3: Admin User Creation**

**Option A: Using Environment Variables (Recommended)**
```bash
# Set environment variables first, then run:
node scripts/seed-admin-user.js
```

**Option B: Interactive Setup**
```bash
# Run with help to see production guidance:
node scripts/seed-admin-user.js --help
```

### **Step 4: Security Verification**

1. **Test Admin Login**
   - Navigate to `https://yourdomain.com/login`
   - Log in with admin credentials
   - Verify access to `/admin` dashboard

2. **Change Default Password**
   - Log in to admin dashboard
   - Navigate to profile settings
   - Change password immediately

3. **Secure the Seeding Script**
   - Delete `scripts/seed-admin-user.js` after use
   - Or restrict file permissions: `chmod 600 scripts/seed-admin-user.js`
   - Remove from production deployment if possible

---

## 🔐 Security Best Practices

### **Password Requirements**
- **Minimum Length**: 20 characters
- **Complexity**: Mix of uppercase, lowercase, numbers, symbols
- **Uniqueness**: Don't reuse passwords from other systems
- **Storage**: Use enterprise password manager

### **Admin Account Security**
- **Email Domain**: Use your company domain, not personal email
- **2FA**: Enable when feature becomes available
- **Access Logging**: Monitor admin login attempts
- **Regular Rotation**: Change password every 90 days

### **Production Environment Security**
- **Environment Variables**: Never commit secrets to version control
- **Access Control**: Limit who can access production environment
- **Backup Admin**: Create a second admin user as backup
- **Audit Trail**: Monitor all admin actions

---

## 🏗️ Deployment Platform Specific Instructions

### **Vercel Deployment**
```bash
# Set environment variables in Vercel dashboard
vercel env add ADMIN_EMAIL
vercel env add ADMIN_PASSWORD
vercel env add ADMIN_NAME

# Deploy application
vercel --prod

# Run admin seeding (one-time)
vercel exec -- node scripts/seed-admin-user.js
```

### **Railway Deployment**
```bash
# Set environment variables in Railway dashboard
railway variables set ADMIN_EMAIL=<EMAIL>
railway variables set ADMIN_PASSWORD=YourSecurePassword123!

# Deploy and run seeding
railway up
railway run node scripts/seed-admin-user.js
```

### **Docker Deployment**
```dockerfile
# In your Dockerfile, add environment variables
ENV ADMIN_EMAIL=<EMAIL>
ENV ADMIN_PASSWORD=YourSecurePassword123!

# Run seeding during container initialization
RUN node scripts/seed-admin-user.js
```

---

## 🚨 Important Security Warnings

### **DO NOT:**
- ❌ Use default credentials in production
- ❌ Commit admin credentials to version control
- ❌ Share admin credentials via insecure channels
- ❌ Leave seeding script accessible in production
- ❌ Use weak or predictable passwords

### **DO:**
- ✅ Use strong, unique passwords
- ✅ Set credentials via environment variables
- ✅ Store credentials in secure password manager
- ✅ Change default password immediately after setup
- ✅ Monitor admin account access
- ✅ Create backup admin accounts
- ✅ Remove or secure seeding script after use

---

## 🔄 Admin User Management

### **Creating Additional Admin Users**
After initial setup, create additional admin users through the admin dashboard:
1. Log in to `/admin`
2. Navigate to "User Management"
3. Create new user with `system_admin` role
4. Send secure credentials to new admin

### **Admin Account Recovery**
If admin access is lost:
1. **Database Access**: Manually update user role in database
2. **Seeding Script**: Re-run with different email if script still available
3. **Support**: Contact platform support for assistance

### **Removing Admin Access**
To revoke admin access:
1. Change user role from `system_admin` to `individual_user`
2. Or soft-delete the user account
3. Audit all actions performed by the user

---

## 📞 Support and Troubleshooting

### **Common Issues**
- **Database Connection**: Verify DATABASE_URL is correct
- **Environment Variables**: Ensure all required variables are set
- **Password Complexity**: Use strong passwords meeting requirements
- **Email Verification**: Admin users are auto-verified

### **Getting Help**
- Check application logs for error details
- Verify environment variable configuration
- Test database connectivity
- Review security requirements

---

*This guide ensures secure and proper setup of admin users for the Sealog Maritime Platform in production environments.*
