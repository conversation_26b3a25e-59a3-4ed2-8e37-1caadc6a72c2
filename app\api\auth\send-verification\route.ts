import { NextResponse } from "next/server"
import { nanoid } from "nanoid"
import { getUserByEmail, createEmailVerification, updateUserVerificationToken } from "@/lib/db"
import { sendVerificationEmail } from "@/lib/email"

/**
 * Send initial email verification (used during registration)
 */
export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({ 
        success: false, 
        error: "Email address is required" 
      }, { status: 400 })
    }

    // Get user by email
    const user = await getUserByEmail(email)
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: "User not found" 
      }, { status: 404 })
    }

    // Check if already verified
    if (user.emailVerified) {
      return NextResponse.json({ 
        success: false, 
        error: "Email address is already verified" 
      }, { status: 400 })
    }

    // Generate verification token
    const token = nanoid(32)
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS || '48'))

    // Create verification record
    await createEmailVerification(user.id, token, expiresAt)
    
    // Update user with token
    await updateUserVerificationToken(user.id, token, expiresAt)

    // Send verification email
    const emailResult = await sendVerificationEmail(user.email, user.name, token)
    
    if (!emailResult.success) {
      console.error("Failed to send verification email:", emailResult.error)
      return NextResponse.json({ 
        success: false, 
        error: "Failed to send verification email. Please try again later." 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: "Verification email sent successfully" 
    })

  } catch (error) {
    console.error("Send verification error:", error)
    return NextResponse.json({ 
      success: false, 
      error: "Failed to send verification email. Please try again." 
    }, { status: 500 })
  }
}
