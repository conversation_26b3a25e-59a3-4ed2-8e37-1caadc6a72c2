#!/usr/bin/env node

/**
 * Unified Migration Script
 * Runs all pending migrations in the correct order with state tracking
 */

const fs = require('fs');
const path = require('path');

// Migration definitions in execution order
const MIGRATIONS = [
  {
    id: 'user-roles',
    name: 'User Roles Migration',
    script: './migrate-user-roles.js',
    description: 'Migrate user roles to new schema'
  },
  {
    id: 'organizations',
    name: 'Organizations Migration', 
    script: './migrate-organization-tables.js',
    description: 'Create organization tables and relationships'
  },
  {
    id: 'notifications',
    name: 'Notifications Migration',
    script: './migrate-notifications-table.js', 
    description: 'Create notifications table and indexes'
  }
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log(`\n${colors.bold}${colors.blue}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Simple migration state tracking (file-based for now)
const MIGRATION_STATE_FILE = path.join(__dirname, '.migration-state.json');

function loadMigrationState() {
  try {
    if (fs.existsSync(MIGRATION_STATE_FILE)) {
      const content = fs.readFileSync(MIGRATION_STATE_FILE, 'utf8');
      return JSON.parse(content);
    }
  } catch (error) {
    logWarning(`Could not load migration state: ${error.message}`);
  }
  return { completed: [], lastRun: null };
}

function saveMigrationState(state) {
  try {
    fs.writeFileSync(MIGRATION_STATE_FILE, JSON.stringify(state, null, 2));
  } catch (error) {
    logError(`Could not save migration state: ${error.message}`);
  }
}

function isMigrationCompleted(migrationId, state) {
  return state.completed.includes(migrationId);
}

function markMigrationCompleted(migrationId, state) {
  if (!state.completed.includes(migrationId)) {
    state.completed.push(migrationId);
    state.lastRun = new Date().toISOString();
  }
}

async function runMigration(migration) {
  const scriptPath = path.join(__dirname, migration.script);
  
  if (!fs.existsSync(scriptPath)) {
    logError(`Migration script not found: ${scriptPath}`);
    return false;
  }

  try {
    logInfo(`Running: ${migration.name}`);
    logInfo(`Description: ${migration.description}`);
    
    // Import and run the migration script
    const migrationModule = require(scriptPath);
    
    // If the script exports a function, call it; otherwise it runs on import
    if (typeof migrationModule === 'function') {
      await migrationModule();
    }
    
    logSuccess(`Completed: ${migration.name}`);
    return true;
  } catch (error) {
    logError(`Failed: ${migration.name} - ${error.message}`);
    return false;
  }
}

async function main() {
  logHeader('Sealog Database Migration Runner');
  
  const state = loadMigrationState();
  const pendingMigrations = MIGRATIONS.filter(m => !isMigrationCompleted(m.id, state));
  
  if (pendingMigrations.length === 0) {
    logSuccess('All migrations are up to date!');
    logInfo(`Last migration run: ${state.lastRun || 'Never'}`);
    return;
  }

  logInfo(`Found ${pendingMigrations.length} pending migration(s)`);
  
  for (const migration of pendingMigrations) {
    const success = await runMigration(migration);
    
    if (success) {
      markMigrationCompleted(migration.id, state);
      saveMigrationState(state);
    } else {
      logError(`Migration failed: ${migration.name}`);
      logError('Stopping migration process to prevent data corruption');
      process.exit(1);
    }
  }
  
  logHeader('Migration Summary');
  logSuccess(`Successfully completed ${pendingMigrations.length} migration(s)`);
  logInfo(`Total migrations completed: ${state.completed.length}/${MIGRATIONS.length}`);
  
  if (state.completed.length === MIGRATIONS.length) {
    logSuccess('🎉 All migrations completed successfully!');
  }
}

// Handle script execution
if (require.main === module) {
  main().catch(error => {
    logError(`Migration runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main, MIGRATIONS };
