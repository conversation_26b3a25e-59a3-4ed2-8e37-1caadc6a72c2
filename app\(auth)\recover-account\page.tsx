"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RefreshCw, CheckCircle, AlertCircle, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

function RecoverAccountContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isRecovered, setIsRecovered] = useState(false)
  const [email, setEmail] = useState("")
  const [userId, setUserId] = useState("")
  const [recoveryToken, setRecoveryToken] = useState("")

  useEffect(() => {
    // Get parameters from URL
    const tokenParam = searchParams.get("token")
    const emailParam = searchParams.get("email")
    const userIdParam = searchParams.get("userId")

    if (tokenParam) setRecoveryToken(tokenParam)
    if (emailParam) setEmail(decodeURIComponent(emailParam))
    if (userIdParam) setUserId(userIdParam)

    // If we have a token but no userId, try to extract it from the token
    // In a real implementation, you might decode the token or make an API call
    if (tokenParam && !userIdParam) {
      // For now, we'll require the userId to be passed separately
      console.log("Recovery token found, but userId is required")
    }
  }, [searchParams])

  const handleRecoverAccount = async () => {
    if (!userId || !recoveryToken) {
      toast.error("Invalid recovery link. Please check your email for the correct link.")
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch("/api/account/recover", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          recoveryToken,
          email: email || undefined,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setIsRecovered(true)
        toast.success("Account recovered successfully! You can now log in.")
      } else {
        toast.error(data.error || "Failed to recover account")
      }
    } catch (error) {
      console.error("Account recovery error:", error)
      toast.error("Failed to recover account. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoToLogin = () => {
    router.push("/login")
  }

  if (isRecovered) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card className="border-green-200 bg-green-50">
            <CardHeader className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-green-800">Account Recovered!</CardTitle>
              <CardDescription className="text-green-700">
                Your account has been successfully recovered and all your data has been restored.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-800 mb-2">What's been restored:</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>✅ Full account access</li>
                  <li>✅ All certificates and files</li>
                  <li>✅ Profile information</li>
                  <li>✅ Account settings</li>
                </ul>
              </div>

              <Button onClick={handleGoToLogin} className="w-full">
                Continue to Login
              </Button>

              <div className="text-center">
                <Link href="/" className="text-sm text-gray-600 hover:text-gray-800">
                  ← Back to Home
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
              <RefreshCw className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle>Recover Your Account</CardTitle>
            <CardDescription>
              Restore your account and regain access to all your maritime certifications.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {!userId || !recoveryToken ? (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-amber-800 mb-2">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Invalid Recovery Link</span>
                </div>
                <p className="text-sm text-amber-700">
                  This recovery link appears to be invalid or incomplete. Please check your email for the correct recovery link.
                </p>
              </div>
            ) : (
              <>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 mb-2">Account Recovery</h4>
                  <p className="text-sm text-blue-700">
                    Click the button below to recover your account. This will restore all your data and re-enable account access.
                  </p>
                  {email && (
                    <p className="text-sm text-blue-600 mt-2">
                      Email: {email}
                    </p>
                  )}
                </div>

                <div className="space-y-4">
                  {!email && (
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address (optional)</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                      <p className="text-xs text-gray-500">
                        Providing your email will send you a confirmation of the recovery.
                      </p>
                    </div>
                  )}

                  <Button
                    onClick={handleRecoverAccount}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Recovering Account...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Recover My Account
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}

            <div className="text-center space-y-2">
              <Link href="/login" className="text-sm text-gray-600 hover:text-gray-800 flex items-center justify-center gap-1">
                <ArrowLeft className="h-3 w-3" />
                Back to Login
              </Link>
              <div className="text-xs text-gray-500">
                Need help? Contact our support team.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function RecoverAccountPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse">Loading...</div>
      </div>
    }>
      <RecoverAccountContent />
    </Suspense>
  )
}
