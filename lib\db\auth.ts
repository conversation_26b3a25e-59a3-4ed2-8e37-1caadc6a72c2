/**
 * Authentication Functions
 *
 * This module handles all authentication-related database operations including:
 * - Social account management
 * - Email verification
 * - Password reset functionality
 * - Authentication tokens and sessions
 */

import { neon } from "@neondatabase/serverless"
import { and, eq, lte } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import { nanoid } from "nanoid"
import {
  emailVerifications,
  passwordResets,
  socialAccounts,
  users
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// SOCIAL ACCOUNT MANAGEMENT
// ============================================================================

/**
 * Link a social account to a user
 */
export async function linkSocialAccount(userId: string, accountData: {
  provider: string
  providerAccountId: string
  accessToken?: string | null
  refreshToken?: string | null
  expiresAt?: number | null
}) {
  try {
    const now = new Date()
    await db.insert(socialAccounts).values({
      id: nanoid(),
      userId,
      provider: accountData.provider,
      providerAccountId: accountData.providerAccountId,
      accessToken: accountData.accessToken,
      refreshToken: accountData.refreshToken,
      expiresAt: accountData.expiresAt ? new Date(accountData.expiresAt * 1000) : null,
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error linking social account:", error)
    throw error
  }
}

/**
 * Get user by social account provider and ID
 */
export async function getUserBySocialAccount(provider: string, providerAccountId: string) {
  try {
    const result = await db
      .select()
      .from(socialAccounts)
      .where(
        and(
          eq(socialAccounts.provider, provider),
          eq(socialAccounts.providerAccountId, providerAccountId)
        )
      )
    return result[0] || null
  } catch (error) {
    console.error("Error getting user by social account:", error)
    return null
  }
}

/**
 * Get all social accounts for a user
 */
export async function getUserSocialAccounts(userId: string) {
  try {
    const result = await db
      .select()
      .from(socialAccounts)
      .where(eq(socialAccounts.userId, userId))
    return result
  } catch (error) {
    console.error("Error getting user social accounts:", error)
    return []
  }
}

/**
 * Unlink a social account from a user
 */
export async function unlinkSocialAccount(userId: string, provider: string) {
  try {
    await db
      .delete(socialAccounts)
      .where(
        and(
          eq(socialAccounts.userId, userId),
          eq(socialAccounts.provider, provider)
        )
      )
    return { success: true }
  } catch (error) {
    console.error("Error unlinking social account:", error)
    throw error
  }
}

/**
 * Update social account tokens
 */
export async function updateSocialAccountTokens(
  userId: string,
  provider: string,
  tokenData: {
    accessToken?: string
    refreshToken?: string
    expiresAt?: number
  }
) {
  try {
    await db
      .update(socialAccounts)
      .set({
        accessToken: tokenData.accessToken,
        refreshToken: tokenData.refreshToken,
        expiresAt: tokenData.expiresAt ? new Date(tokenData.expiresAt * 1000) : null,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(socialAccounts.userId, userId),
          eq(socialAccounts.provider, provider)
        )
      )
    return { success: true }
  } catch (error) {
    console.error("Error updating social account tokens:", error)
    throw error
  }
}

// ============================================================================
// EMAIL VERIFICATION
// ============================================================================

/**
 * Create email verification token
 */
export async function createEmailVerification(userId: string, token: string, expiresAt: Date) {
  try {
    await db.insert(emailVerifications).values({
      id: nanoid(),
      userId,
      token,
      expiresAt,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating email verification:", error)
    throw error
  }
}

/**
 * Get email verification by token
 */
export async function getEmailVerification(token: string) {
  try {
    const result = await db
      .select()
      .from(emailVerifications)
      .where(eq(emailVerifications.token, token))
      .limit(1)
    return result[0] || null
  } catch (error) {
    console.error("Error getting email verification:", error)
    return null
  }
}

/**
 * Delete email verification token
 */
export async function deleteEmailVerification(token: string) {
  try {
    await db.delete(emailVerifications).where(eq(emailVerifications.token, token))
    return { success: true }
  } catch (error) {
    console.error("Error deleting email verification:", error)
    throw error
  }
}

/**
 * Delete all email verification tokens for a user
 */
export async function deleteUserEmailVerifications(userId: string) {
  try {
    await db.delete(emailVerifications).where(eq(emailVerifications.userId, userId))
    return { success: true }
  } catch (error) {
    console.error("Error deleting user email verifications:", error)
    throw error
  }
}

/**
 * Mark user's email as verified and clean up verification tokens
 */
export async function markEmailAsVerified(userId: string) {
  try {
    await db
      .update(users)
      .set({
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))

    // Clean up verification tokens
    await deleteUserEmailVerifications(userId)

    return { success: true }
  } catch (error) {
    console.error("Error marking email as verified:", error)
    throw error
  }
}

/**
 * Update user's verification token (legacy field support)
 */
export async function updateUserVerificationToken(userId: string, token: string, expiresAt: Date) {
  try {
    await db
      .update(users)
      .set({
        emailVerificationToken: token,
        emailVerificationExpires: expiresAt,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))

    return { success: true }
  } catch (error) {
    console.error("Error updating user verification token:", error)
    throw error
  }
}

// ============================================================================
// PASSWORD RESET
// ============================================================================

/**
 * Create password reset token
 */
export async function createPasswordReset(userId: string, token: string, expiresAt: Date) {
  try {
    await db.insert(passwordResets).values({
      id: nanoid(),
      userId,
      token,
      expiresAt,
      used: false,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating password reset:", error)
    throw error
  }
}

/**
 * Get password reset by token
 */
export async function getPasswordReset(token: string) {
  try {
    const result = await db
      .select()
      .from(passwordResets)
      .where(
        and(
          eq(passwordResets.token, token),
          eq(passwordResets.used, false)
        )
      )
    return result[0] || null
  } catch (error) {
    console.error("Error getting password reset:", error)
    return null
  }
}

/**
 * Mark password reset token as used
 */
export async function markPasswordResetAsUsed(token: string) {
  try {
    await db
      .update(passwordResets)
      .set({ used: true })
      .where(eq(passwordResets.token, token))
    return { success: true }
  } catch (error) {
    console.error("Error marking password reset as used:", error)
    throw error
  }
}

/**
 * Delete expired password reset tokens
 */
export async function cleanupExpiredPasswordResets() {
  try {
    const now = new Date()
    const result = await db
      .delete(passwordResets)
      .where(lte(passwordResets.expiresAt, now))

    return { success: true, deletedCount: result.rowCount || 0 }
  } catch (error) {
    console.error("Error cleaning up expired password resets:", error)
    throw error
  }
}

/**
 * Delete all password reset tokens for a user
 */
export async function deleteUserPasswordResets(userId: string) {
  try {
    await db.delete(passwordResets).where(eq(passwordResets.userId, userId))
    return { success: true }
  } catch (error) {
    console.error("Error deleting user password resets:", error)
    throw error
  }
}
