import { NextRequest, NextResponse } from "next/server";
import { EmailDeliveryTester, emailPerformanceMonitor } from "@/lib/email-testing";
import { handleApiError, AuthenticationError, AuthorizationError, ValidationError } from "@/lib/api-error-handler";
import { getServerSession } from "next-auth";
import { logger } from "@/lib/logger";

/**
 * Email testing and monitoring endpoint (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      throw new AuthenticationError();
    }
    
    // Check if user is admin (for now, just check if user exists - implement proper RBAC later)
    if (!session.user) {
      throw new AuthorizationError("Admin access required");
    }
    
    const body = await request.json();
    const { action, testEmail, environment = "development", skipActualSend = false } = body;
    
    if (!action) {
      throw new ValidationError("Action is required");
    }
    
    if (action === "test" && !testEmail) {
      throw new ValidationError("Test email is required for testing");
    }
    
    logger.info("admin", "Email test requested", {
      userId: session.user.id,
      action,
      testEmail: testEmail ? "[REDACTED]" : undefined,
      environment,
    });
    
    let result;
    
    switch (action) {
      case "test":
        const tester = new EmailDeliveryTester({
          testEmail,
          environment,
          skipActualSend,
        });
        
        result = await tester.runAllTests();
        break;
        
      case "test_basic":
        const basicTester = new EmailDeliveryTester({
          testEmail,
          environment,
          skipActualSend,
        });
        
        result = await basicTester.testBasicDelivery();
        break;
        
      case "test_templates":
        const templateTester = new EmailDeliveryTester({
          testEmail,
          environment,
          skipActualSend,
        });
        
        result = await templateTester.testEmailTemplates();
        break;
        
      case "test_retry":
        const retryTester = new EmailDeliveryTester({
          testEmail: testEmail || "<EMAIL>",
          environment,
          skipActualSend: true, // Always skip for retry tests
        });
        
        result = await retryTester.testRetryLogic();
        break;
        
      default:
        throw new ValidationError(`Unknown action: ${action}`);
    }
    
    return NextResponse.json({
      success: true,
      action,
      timestamp: new Date().toISOString(),
      result,
    });
    
  } catch (error) {
    return handleApiError(error, "admin-email-test");
  }
}

/**
 * Get email performance metrics (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      throw new AuthenticationError();
    }
    
    // Check if user is admin (for now, just check if user exists - implement proper RBAC later)
    if (!session.user) {
      throw new AuthorizationError("Admin access required");
    }
    
    const { searchParams } = new URL(request.url);
    const hours = parseInt(searchParams.get("hours") || "24");
    
    logger.info("admin", "Email metrics requested", {
      userId: session.user.id,
      hours,
    });
    
    const metrics = emailPerformanceMonitor.getMetrics(hours);
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      timeframe: `${hours} hours`,
      metrics,
    });
    
  } catch (error) {
    return handleApiError(error, "admin-email-metrics");
  }
}
