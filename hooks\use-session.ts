"use client"

import { SessionContext } from "@/lib/auth-config"
import { useEffect, useState } from "react"

/**
 * React hook for accessing session context in client components
 * Provides session data and loading state
 */
export function useSession() {
  const [session, setSession] = useState<SessionContext | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch('/api/auth/session', {
          credentials: 'include'
        })

        if (response.ok) {
          const data = await response.json()
          setSession(data.session)
        } else if (response.status === 401) {
          setSession(null)
        } else {
          throw new Error('Failed to fetch session')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setSession(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSession()
  }, [])

  return {
    session,
    isLoading,
    error,
    isAuthenticated: !!session
  }
}

/**
 * SCAFFOLDING: Hook for checking specific permissions
 * Note: Currently simplified - all individual users have basic certificate permissions
 * Complex permission checking is scaffolding for future RBAC implementation
 */
export function usePermissions() {
  const { session } = useSession()

  const hasPermission = (permission: string) => {
    // SIMPLIFIED: Currently all authenticated users have basic permissions
    // Complex permission logic is scaffolding for future implementation
    if (!session) return false

    // System admins have all permissions
    if (session.role === 'system_admin') return true

    // Individual users have basic certificate permissions
    const basicPermissions = ['certificates:read', 'certificates:write', 'certificates:delete']
    return basicPermissions.includes(permission)
  }

  const hasAnyPermission = (permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]) => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: session?.permissions || [] // Populated by session utilities but simplified
  }
}

/**
 * Hook for role-based access control
 */
export function useRole() {
  const { session } = useSession()

  const hasRole = (role: string) => {
    return session?.role === role
  }

  const hasAnyRole = (roles: string[]) => {
    return roles.includes(session?.role || '')
  }

  return {
    role: session?.role,
    hasRole,
    hasAnyRole,
    isIndividualUser: hasRole('individual_user'),
    isSystemAdmin: hasRole('system_admin'),
    // Note: Organization roles are handled via OrganizationMembership, not user roles
    // Use organization context switching for org-specific permissions
  }
}
