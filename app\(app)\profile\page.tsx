"use client";

import { signIn } from "next-auth/react";
import type React from "react";

import { Camera, Mail, Phone, Ship, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  subscriptionPlan: string;
  emailVerified: boolean;
  twoFactorEnabled: boolean;
  lastLoginAt: string | null;
  createdAt: string;
  phone: string | null;
  position: string | null;
  bio: string | null;
  company: string | null;
  location: string | null;
}

interface LinkedAccount {
  provider: string;
  connectedAt: string;
}

interface AvailableProviders {
  google: boolean;
  facebook: boolean;
}

export default function ProfilePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [availableProviders, setAvailableProviders] =
    useState<AvailableProviders>({
      google: false,
      facebook: false,
    });
  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        const response = await fetch("/api/user/profile", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
          throw new Error("Failed to load profile");
        }

        const data = await response.json();

        if (data.success) {
          setUserData(data.user);
          setLinkedAccounts(data.linkedAccounts || []);
          setAvailableProviders(
            data.availableProviders || { google: false, facebook: false }
          );
        } else {
          setError("Failed to load profile data");
        }
      } catch (error) {
        console.error("Profile loading failed:", error);
        setError("Failed to load profile. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, [router]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setUserData((prev) => {
      if (!prev) return prev;
      return { ...prev, [name]: value };
    });
  };

  const handleLogout = async () => {
    try {
      // First, call our custom logout API to clear all sessions
      const logoutResponse = await fetch("/api/logout", {
        method: "POST",
        credentials: "include",
      });

      if (!logoutResponse.ok) {
        console.error("Logout API failed:", await logoutResponse.text());
      }

      // Also use NextAuth signOut to ensure complete cleanup
      const { signOut } = await import("next-auth/react");
      await signOut({
        redirect: false, // We'll handle redirect manually
        callbackUrl: "/login",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Force redirect to login page and clear any cached data
    window.location.replace("/login");
  };

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    // Simulate saving data
    setTimeout(() => {
      setIsSaving(false);
    }, 1000);
  };

  const handleConnectAccount = async (provider: string) => {
    setIsConnecting(provider);
    setError(null);

    try {
      // Use NextAuth signIn to link the account
      // This will redirect to the OAuth provider and then back to the callback URL
      await signIn(provider, {
        callbackUrl: "/profile?tab=account&connected=" + provider,
      });
    } catch (error) {
      console.error(`${provider} connection error:`, error);
      setError(`Failed to connect ${provider} account. Please try again.`);
      setIsConnecting(null);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading profile...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
        <p className="text-muted-foreground">
          Manage your personal information and account settings
        </p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <Card className="md:w-1/3">
          <CardHeader>
            <CardTitle>Profile Picture</CardTitle>
            <CardDescription>Update your profile photo</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <Avatar className="h-32 w-32">
              <AvatarImage
                src="/placeholder.svg?height=128&width=128"
                alt={userData?.name || "User"}
              />
              <AvatarFallback className="text-4xl">
                {userData?.name
                  ? userData.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()
                      .slice(0, 2)
                  : "U"}
              </AvatarFallback>
            </Avatar>
            <Button variant="outline" className="mt-4">
              <Camera className="mr-2 h-4 w-4" /> Change Photo
            </Button>
          </CardContent>
        </Card>

        <div className="flex-1">
          <Tabs defaultValue="personal">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="personal">Personal Information</TabsTrigger>
              <TabsTrigger value="account">Account Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="personal" className="mt-4">
              <Card>
                <form onSubmit={handleSave}>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                    <CardDescription>
                      Update your personal details
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <div className="relative">
                          <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="name"
                            name="name"
                            value={userData?.name || ""}
                            onChange={handleInputChange}
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={userData?.email || ""}
                            onChange={handleInputChange}
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="phone"
                            name="phone"
                            value={userData?.phone || ""}
                            onChange={handleInputChange}
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="position">Position</Label>
                        <div className="relative">
                          <Ship className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="position"
                            name="position"
                            value={userData?.position || ""}
                            onChange={handleInputChange}
                            className="pl-10"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        name="bio"
                        value={userData?.bio || ""}
                        onChange={handleInputChange}
                        rows={4}
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="company">Company</Label>
                        <Input
                          id="company"
                          name="company"
                          value={userData?.company || ""}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          name="location"
                          value={userData?.location || ""}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? "Saving..." : "Save Changes"}
                    </Button>
                    <Button variant="destructive" onClick={handleLogout}>
                      Logout
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
            <TabsContent value="account" className="mt-4">
              <div className="space-y-6">
                {/* Email Verification Status */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Email Verification
                    </CardTitle>
                    <CardDescription>
                      Verify your email address for enhanced security
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className={`flex items-center gap-2 ${
                            userData?.emailVerified
                              ? "text-green-600"
                              : "text-orange-600"
                          }`}
                        >
                          <User className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {userData?.emailVerified
                              ? "Email Verified"
                              : "Email Not Verified"}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={userData?.emailVerified}
                      >
                        {userData?.emailVerified
                          ? "Verified"
                          : "Send Verification"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Password Management for OAuth Users */}
                {linkedAccounts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Password Settings
                      </CardTitle>
                      <CardDescription>
                        Add a password to your account for additional login
                        options
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Alert>
                          <AlertDescription>
                            Your account was created using social login. Adding
                            a password will allow you to sign in with either
                            your email/password or your connected social
                            accounts.
                          </AlertDescription>
                        </Alert>
                        <Button variant="outline" className="w-full">
                          Add Password to Account
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Connected Accounts */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Connected Accounts
                    </CardTitle>
                    <CardDescription>
                      Link your social accounts for easier sign-in
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Google Account */}
                    {availableProviders.google && (
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <span className="text-red-600 font-semibold text-sm">
                              G
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">Google</p>
                            <p className="text-sm text-muted-foreground">
                              {linkedAccounts.some(
                                (acc) => acc.provider === "google"
                              )
                                ? `Connected ${new Date(
                                    linkedAccounts.find(
                                      (acc) => acc.provider === "google"
                                    )?.connectedAt || ""
                                  ).toLocaleDateString()}`
                                : "Not connected"}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={
                            linkedAccounts.some(
                              (acc) => acc.provider === "google"
                            ) || isConnecting === "google"
                          }
                          onClick={() => handleConnectAccount("google")}
                        >
                          <User className="h-4 w-4 mr-2" />
                          {isConnecting === "google"
                            ? "Connecting..."
                            : linkedAccounts.some(
                                (acc) => acc.provider === "google"
                              )
                            ? "Connected"
                            : "Connect"}
                        </Button>
                      </div>
                    )}

                    {/* Facebook Account */}
                    <div className="flex items-center justify-between p-4 border rounded-lg opacity-50">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold text-sm">
                            f
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">Facebook</p>
                          <p className="text-sm text-muted-foreground">
                            {availableProviders.facebook
                              ? linkedAccounts.some(
                                  (acc) => acc.provider === "facebook"
                                )
                                ? "Connected"
                                : "Not connected"
                              : "Requires business verification"}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={!availableProviders.facebook}
                      >
                        {availableProviders.facebook
                          ? "Connect"
                          : "Unavailable"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Two-Factor Authentication */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Two-Factor Authentication
                    </CardTitle>
                    <CardDescription>
                      Add an extra layer of security to your account
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className={`flex items-center gap-2 ${
                            userData?.twoFactorEnabled
                              ? "text-green-600"
                              : "text-muted-foreground"
                          }`}
                        >
                          <User className="h-4 w-4" />
                          <span className="text-sm">
                            {userData?.twoFactorEnabled
                              ? "Two-factor authentication is enabled"
                              : "Two-factor authentication is disabled"}
                          </span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        {userData?.twoFactorEnabled
                          ? "Disable 2FA"
                          : "Enable 2FA"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Password Change */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Change Password
                    </CardTitle>
                    <CardDescription>
                      Update your account password
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input id="current-password" type="password" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input id="new-password" type="password" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">
                        Confirm New Password
                      </Label>
                      <Input id="confirm-password" type="password" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button>Update Password</Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
