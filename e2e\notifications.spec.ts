import { expect, test } from '@playwright/test';

test.describe('Notification System - Critical Path', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' });

  test.beforeEach(async ({ page }) => {
    // Seed notification test data before each test
    console.log('🌱 Seeding notification test data...');

    // Run the seeding script via API call to ensure fresh test data
    const response = await page.request.post('/api/admin/seed-notifications', {
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        action: 'seed_test_data'
      }
    });

    if (!response.ok()) {
      console.warn('⚠️ Could not seed notification test data via API, continuing with existing data');
    }
  });

  test('should display notification system in topbar and navigate to notifications page', async ({ page }) => {
    await page.goto('/dashboard');

    // Check if notification bell is visible
    const notificationBell = page.locator('[data-testid="notification-bell"]');

    await expect(notificationBell).toBeVisible();
    console.log('✅ Notification bell is visible in topbar');

    // Navigate to notifications page
    await page.goto('/notifications');

    // Check if notifications page loads correctly
    await expect(page.locator('h1')).toContainText(/notifications?/i);

    // Check for notification tabs
    await expect(page.getByRole('tab', { name: 'Unread' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'All Notifications' })).toBeVisible();

    console.log('✅ Notifications page loaded successfully');
  });

  test('should display and interact with notifications', async ({ page }) => {
    await page.goto('/notifications');
    await page.waitForTimeout(2000);

    // Check if notification cards are present or empty state is shown
    const notificationCards = page.locator('[data-testid="notification-card"]').or(
      page.locator('.relative.overflow-hidden').filter({ has: page.locator('.absolute.left-0') })
    );

    const emptyState = page.locator('text=No notifications');
    const cardCount = await notificationCards.count();
    const emptyStateCount = await emptyState.count();

    if (cardCount > 0) {
      await expect(notificationCards.first()).toBeVisible();
      console.log(`✅ Found ${cardCount} notification cards`);

      // Test mark as read functionality
      const markAsReadButton = page.locator('text=Mark as Read').first();
      if (await markAsReadButton.isVisible()) {
        await markAsReadButton.click();
        await page.waitForTimeout(1000);
        console.log('✅ Marked notification as read');
      }
    } else if (emptyStateCount > 0) {
      await expect(emptyState.first()).toBeVisible();
      console.log('ℹ️ No notifications - empty state displayed');
    }

    // Test tab switching
    await page.getByRole('tab', { name: 'All Notifications' }).click();
    await page.waitForTimeout(500);
    await page.getByRole('tab', { name: 'Unread' }).click();
    await page.waitForTimeout(500);
    console.log('✅ Successfully switched between notification tabs');
  });

  test('should test notification API endpoints', async ({ page }) => {
    // Test GET /api/notifications
    const notificationsResponse = await page.request.get('/api/notifications');
    expect(notificationsResponse.ok()).toBeTruthy();

    const notificationsData = await notificationsResponse.json();
    expect(notificationsData.success).toBeTruthy();
    expect(Array.isArray(notificationsData.notifications)).toBeTruthy();

    console.log(`✅ API returned ${notificationsData.notifications.length} notifications`);

    // Test GET /api/notifications?countOnly=true
    const countResponse = await page.request.get('/api/notifications?countOnly=true');
    expect(countResponse.ok()).toBeTruthy();

    const countData = await countResponse.json();
    expect(countData.success).toBeTruthy();
    expect(typeof countData.count).toBe('number');

    console.log(`✅ API returned unread count: ${countData.count}`);
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/notifications');
    await page.waitForTimeout(2000);

    // Check if page is responsive
    await expect(page.locator('h1')).toBeVisible();

    // Check if tabs are still functional on mobile
    await page.getByRole('tab', { name: 'All Notifications' }).click();
    await page.waitForTimeout(500);

    await page.getByRole('tab', { name: 'Unread' }).click();
    await page.waitForTimeout(500);

    console.log('✅ Notifications page is responsive on mobile');
  });
});
