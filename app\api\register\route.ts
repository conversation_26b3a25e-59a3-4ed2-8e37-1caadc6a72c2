import { handleApiError } from "@/lib/api-error-handler";
import { IndividualSubscriptionPlan, UserRole } from "@/lib/auth-config";
import {
  createEmailVerification,
  createUser,
  getUserByEmail,
  updateUserVerificationToken
} from "@/lib/db";
import { sendVerificationEmail } from "@/lib/email";
import { clearSession, createSession, getServerSession } from "@/lib/session";
import { userRegistrationSchema, validateRequestBody } from "@/lib/validation-schemas";
import { hash } from "bcrypt";
import { nanoid } from "nanoid";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    // Validate request body
    const validatedData = await validateRequestBody(req, userRegistrationSchema);
    const { name, email, password } = validatedData;

    // CRITICAL FIX: Handle existing unverified sessions
    // Allow users to create new accounts even if they have an existing unverified session
    const currentSession = await getServerSession()
    if (currentSession && !currentSession.emailVerified) {
      console.log(`Clearing existing unverified session for new signup: ${currentSession.email} -> ${email}`)
      await clearSession()
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email)
    if (existingUser) {
      return NextResponse.json({ error: "User with this email already exists" }, { status: 409 })
    }

    // Hash password
    const hashedPassword = await hash(password, 10)
    const userId = nanoid()

    // All users register as individual users
    const userRole = UserRole.INDIVIDUAL_USER;

    // Create user (unverified by default for credential users)
    await createUser({
      id: userId,
      name,
      email,
      password: hashedPassword,
      role: userRole,
      subscriptionPlan: IndividualSubscriptionPlan.FREE,
      emailVerified: false, // Credential users must verify email
    })

    // Generate verification token
    const token = nanoid(32)
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS || '48'))

    // Create verification record
    await createEmailVerification(userId, token, expiresAt)

    // Update user with token
    await updateUserVerificationToken(userId, token, expiresAt)

    // Send verification email
    const emailResult = await sendVerificationEmail(email, name, token)

    if (!emailResult.success) {
      console.error("Failed to send verification email:", emailResult.error)

      // Development mode: Show verification link in console for testing
      if (process.env.NODE_ENV === 'development') {
        const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${token}`
        console.log('\n🔧 DEVELOPMENT MODE - Manual Verification Link:')
        console.log(`📧 User: ${name} (${email})`)
        console.log(`🔗 Verification URL: ${verificationUrl}`)
        console.log('✅ Use this link to manually verify the account\n')
      }

      // Don't fail registration if email fails, but log it
      console.warn("User created but verification email failed to send")
    }

    // Create session for the new user to enable access to verification-pending page
    const newUser = {
      id: userId,
      email,
      name,
      role: userRole,
      subscriptionPlan: IndividualSubscriptionPlan.FREE,
      emailVerified: false
    }

    // Use the proper session utility to create the session
    await createSession(newUser)

    const message = "Account created successfully. Please check your email to verify your account.";

    return NextResponse.json(
      {
        success: true,
        user: {
          id: userId,
          name,
          email,
        },
        message,
        requiresVerification: true
      },
      { status: 201 },
    )
  } catch (error) {
    return handleApiError(error, 'register');
  }
}
