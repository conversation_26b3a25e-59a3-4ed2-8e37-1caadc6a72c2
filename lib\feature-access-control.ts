/**
 * Feature Access Control System
 *
 * This module handles both verification requirements AND subscription requirements
 * for different organization types and features. Organizations are created as "pending"
 * by default with immediate access to basic workspace features.
 *
 * Two independent gate systems:
 * 1. Verification Gates - Based on organization verification status
 * 2. Subscription Gates - Based on payment/subscription level
 */

export type OrganizationType = "yacht_company" | "cert_provider"
export type OrganizationStatus = "pending" | "verified" | "suspended"
export type SubscriptionPlan = "free" | "basic" | "premium" | "enterprise"

export interface Organization {
  id: string
  name: string
  type: OrganizationType
  status: OrganizationStatus
  subscriptionPlan?: SubscriptionPlan
  verifiedAt?: Date | null
  verifiedBy?: string | null
}

/**
 * Feature categories with different access requirements
 */
export enum FeatureCategory {
  // Basic workspace features (no gates)
  BASIC_WORKSPACE = "basic_workspace",
  CERTIFICATE_MANAGEMENT = "certificate_management",
  MEMBER_MANAGEMENT = "member_management",

  // Verification-only features (verification required, no payment)
  JOB_MARKET_ACCESS = "job_market_access",
  PUBLIC_LISTINGS = "public_listings",
  CERTIFICATE_ISSUANCE = "certificate_issuance",

  // Subscription-only features (payment required, no verification)
  ADVANCED_ANALYTICS = "advanced_analytics",
  BULK_OPERATIONS = "bulk_operations",
  PRIORITY_SUPPORT = "priority_support",

  // Premium features (both verification AND subscription required)
  API_ACCESS = "api_access",
  ADVANCED_INTEGRATIONS = "advanced_integrations",
  WHITE_LABEL_SOLUTIONS = "white_label_solutions",
  CUSTOM_BRANDING = "custom_branding",
}

/**
 * Access requirements for each feature
 */
export interface FeatureRequirements {
  verificationRequired: boolean
  minimumSubscription: SubscriptionPlan | null
}

/**
 * Feature requirements for yacht companies
 */
export const YACHT_COMPANY_REQUIREMENTS: Record<FeatureCategory, FeatureRequirements> = {
  // Basic features - no gates
  [FeatureCategory.BASIC_WORKSPACE]: { verificationRequired: false, minimumSubscription: null },
  [FeatureCategory.CERTIFICATE_MANAGEMENT]: { verificationRequired: false, minimumSubscription: null },
  [FeatureCategory.MEMBER_MANAGEMENT]: { verificationRequired: false, minimumSubscription: null },

  // Verification-only features
  [FeatureCategory.JOB_MARKET_ACCESS]: { verificationRequired: true, minimumSubscription: null },
  [FeatureCategory.PUBLIC_LISTINGS]: { verificationRequired: true, minimumSubscription: null },
  [FeatureCategory.CERTIFICATE_ISSUANCE]: { verificationRequired: false, minimumSubscription: null }, // N/A for yachts

  // Subscription-only features
  [FeatureCategory.ADVANCED_ANALYTICS]: { verificationRequired: false, minimumSubscription: "basic" },
  [FeatureCategory.BULK_OPERATIONS]: { verificationRequired: false, minimumSubscription: "basic" },
  [FeatureCategory.PRIORITY_SUPPORT]: { verificationRequired: false, minimumSubscription: "premium" },

  // Premium features - both gates
  [FeatureCategory.API_ACCESS]: { verificationRequired: true, minimumSubscription: "premium" },
  [FeatureCategory.ADVANCED_INTEGRATIONS]: { verificationRequired: true, minimumSubscription: "premium" },
  [FeatureCategory.WHITE_LABEL_SOLUTIONS]: { verificationRequired: true, minimumSubscription: "enterprise" },
  [FeatureCategory.CUSTOM_BRANDING]: { verificationRequired: true, minimumSubscription: "enterprise" },
}

/**
 * Feature requirements for training providers
 */
export const TRAINING_PROVIDER_REQUIREMENTS: Record<FeatureCategory, FeatureRequirements> = {
  // Basic features - no gates
  [FeatureCategory.BASIC_WORKSPACE]: { verificationRequired: false, minimumSubscription: null },
  [FeatureCategory.CERTIFICATE_MANAGEMENT]: { verificationRequired: false, minimumSubscription: null },
  [FeatureCategory.MEMBER_MANAGEMENT]: { verificationRequired: false, minimumSubscription: null },

  // Verification-only features
  [FeatureCategory.JOB_MARKET_ACCESS]: { verificationRequired: false, minimumSubscription: null }, // N/A for providers
  [FeatureCategory.PUBLIC_LISTINGS]: { verificationRequired: true, minimumSubscription: null },
  [FeatureCategory.CERTIFICATE_ISSUANCE]: { verificationRequired: true, minimumSubscription: null },

  // Subscription-only features
  [FeatureCategory.ADVANCED_ANALYTICS]: { verificationRequired: false, minimumSubscription: "basic" },
  [FeatureCategory.BULK_OPERATIONS]: { verificationRequired: false, minimumSubscription: "basic" },
  [FeatureCategory.PRIORITY_SUPPORT]: { verificationRequired: false, minimumSubscription: "premium" },

  // Premium features - both gates
  [FeatureCategory.API_ACCESS]: { verificationRequired: true, minimumSubscription: "premium" },
  [FeatureCategory.ADVANCED_INTEGRATIONS]: { verificationRequired: true, minimumSubscription: "premium" },
  [FeatureCategory.WHITE_LABEL_SOLUTIONS]: { verificationRequired: true, minimumSubscription: "enterprise" },
  [FeatureCategory.CUSTOM_BRANDING]: { verificationRequired: true, minimumSubscription: "enterprise" },
}

/**
 * Subscription plan hierarchy for comparison
 */
const SUBSCRIPTION_HIERARCHY: Record<SubscriptionPlan, number> = {
  free: 0,
  basic: 1,
  premium: 2,
  enterprise: 3,
}

/**
 * Check if subscription meets minimum requirement
 */
function meetsSubscriptionRequirement(
  currentPlan: SubscriptionPlan | undefined,
  requiredPlan: SubscriptionPlan | null
): boolean {
  if (requiredPlan === null) return true
  if (!currentPlan) return false

  return SUBSCRIPTION_HIERARCHY[currentPlan] >= SUBSCRIPTION_HIERARCHY[requiredPlan]
}

/**
 * Get feature requirements for organization type
 */
function getFeatureRequirements(organizationType: OrganizationType): Record<FeatureCategory, FeatureRequirements> {
  return organizationType === "yacht_company"
    ? YACHT_COMPANY_REQUIREMENTS
    : TRAINING_PROVIDER_REQUIREMENTS
}

/**
 * Check if an organization has access to a specific feature
 */
export function hasFeatureAccess(
  organization: Organization,
  feature: FeatureCategory
): boolean {
  const requirements = getFeatureRequirements(organization.type)[feature]

  // Check verification requirement
  if (requirements.verificationRequired && organization.status !== "verified") {
    return false
  }

  // Check subscription requirement
  if (!meetsSubscriptionRequirement(organization.subscriptionPlan, requirements.minimumSubscription)) {
    return false
  }

  // Check if organization is suspended
  if (organization.status === "suspended") {
    return false
  }

  return true
}

/**
 * Get detailed access information for a feature
 */
export function getFeatureAccessInfo(
  organization: Organization,
  feature: FeatureCategory
): {
  hasAccess: boolean
  verificationRequired: boolean
  verificationMet: boolean
  subscriptionRequired: boolean
  subscriptionMet: boolean
  minimumSubscription: SubscriptionPlan | null
  currentSubscription: SubscriptionPlan | undefined
  message: string
  actionRequired?: string
} {
  const requirements = getFeatureRequirements(organization.type)[feature]
  const verificationMet = !requirements.verificationRequired || organization.status === "verified"
  const subscriptionMet = meetsSubscriptionRequirement(organization.subscriptionPlan, requirements.minimumSubscription)
  const hasAccess = verificationMet && subscriptionMet && organization.status !== "suspended"

  let message = ""
  let actionRequired = ""

  if (organization.status === "suspended") {
    message = "Organization is suspended. Contact support for assistance."
    actionRequired = "Contact support to resolve suspension."
  } else if (hasAccess) {
    message = "You have access to this feature."
  } else {
    const issues = []
    if (!verificationMet) {
      issues.push("verification required")
    }
    if (!subscriptionMet) {
      issues.push(`${requirements.minimumSubscription} subscription required`)
    }

    message = `Access denied: ${issues.join(" and ")}.`

    if (!verificationMet && !subscriptionMet) {
      actionRequired = "Complete verification and upgrade subscription."
    } else if (!verificationMet) {
      actionRequired = "Contact support to begin verification process."
    } else {
      actionRequired = `Upgrade to ${requirements.minimumSubscription} subscription.`
    }
  }

  return {
    hasAccess,
    verificationRequired: requirements.verificationRequired,
    verificationMet,
    subscriptionRequired: requirements.minimumSubscription !== null,
    subscriptionMet,
    minimumSubscription: requirements.minimumSubscription,
    currentSubscription: organization.subscriptionPlan,
    message,
    actionRequired: actionRequired || undefined
  }
}

/**
 * Get user-friendly feature descriptions
 */
export function getFeatureDescription(feature: FeatureCategory): string {
  const descriptions: Record<FeatureCategory, string> = {
    [FeatureCategory.BASIC_WORKSPACE]: "Access to organization dashboard and basic features",
    [FeatureCategory.CERTIFICATE_MANAGEMENT]: "Upload, manage, and organize certificates",
    [FeatureCategory.MEMBER_MANAGEMENT]: "Invite and manage organization members",
    [FeatureCategory.JOB_MARKET_ACCESS]: "Post jobs and search for crew members",
    [FeatureCategory.PUBLIC_LISTINGS]: "Appear in public course catalogs and directories",
    [FeatureCategory.CERTIFICATE_ISSUANCE]: "Issue official certificates to students",
    [FeatureCategory.ADVANCED_ANALYTICS]: "Advanced reporting and analytics dashboard",
    [FeatureCategory.BULK_OPERATIONS]: "Bulk import/export and batch operations",
    [FeatureCategory.PRIORITY_SUPPORT]: "Priority customer support and dedicated assistance",
    [FeatureCategory.API_ACCESS]: "Programmatic access to platform features",
    [FeatureCategory.ADVANCED_INTEGRATIONS]: "Connect with external systems and tools",
    [FeatureCategory.WHITE_LABEL_SOLUTIONS]: "Custom branding and white-label options",
    [FeatureCategory.CUSTOM_BRANDING]: "Full customization of platform appearance",
  }

  return descriptions[feature]
}

/**
 * Organization lifecycle management for admin account deletion
 */
export interface OrganizationLifecycleEvent {
  organizationId: string
  eventType: "admin_deleted" | "grace_period_started" | "grace_period_ended" | "admin_recovered"
  triggeredBy: string
  triggeredAt: Date
  gracePeriodEnds?: Date
}

/**
 * Handle admin account deletion - start 30-day grace period
 */
export function handleAdminAccountDeletion(
  organizationId: string,
  deletedAdminUserId: string
): OrganizationLifecycleEvent {
  const now = new Date()
  const gracePeriodEnds = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)) // 30 days

  return {
    organizationId,
    eventType: "grace_period_started",
    triggeredBy: deletedAdminUserId,
    triggeredAt: now,
    gracePeriodEnds
  }
}

/**
 * Check if organization is in grace period
 */
export function isInGracePeriod(lifecycleEvent: OrganizationLifecycleEvent): boolean {
  if (lifecycleEvent.eventType !== "grace_period_started") {
    return false
  }

  if (!lifecycleEvent.gracePeriodEnds) {
    return false
  }

  return new Date() < lifecycleEvent.gracePeriodEnds
}

/**
 * Calculate days remaining in grace period
 */
export function getGracePeriodDaysRemaining(lifecycleEvent: OrganizationLifecycleEvent): number {
  if (!isInGracePeriod(lifecycleEvent) || !lifecycleEvent.gracePeriodEnds) {
    return 0
  }

  const now = new Date()
  const msRemaining = lifecycleEvent.gracePeriodEnds.getTime() - now.getTime()
  const daysRemaining = Math.ceil(msRemaining / (24 * 60 * 60 * 1000))

  return Math.max(0, daysRemaining)
}
