/**
 * Database Schema Definitions
 *
 * This file contains all table schemas for the Sealog maritime platform.
 * Organized by domain: Users, Auth, Organizations, Certificates, Admin, Lifecycle
 */

import { boolean, index, integer, pgSchema, pgTable, text, timestamp, uniqueIndex } from "drizzle-orm/pg-core"

// ============================================================================
// USER TABLES
// ============================================================================

export const users = pgTable("User", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password"), // Optional for SSO users
  role: text("role").default("individual_user").notNull(),
  subscriptionPlan: text("subscriptionPlan").default("individual_free").notNull(),
  tenantId: text("tenantId"),
  tenantRole: text("tenantRole"),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  emailVerificationToken: text("emailVerificationToken"),
  emailVerificationExpires: timestamp("emailVerificationExpires"),
  twoFactorEnabled: boolean("twoFactorEnabled").default(false).notNull(),
  twoFactorSecret: text("twoFactorSecret"),
  lastLoginAt: timestamp("lastLoginAt"),
  // Account deletion fields
  deletedAt: timestamp("deletedAt"), // Soft deletion timestamp
  deletionRequestedAt: timestamp("deletionRequestedAt"), // When user requested deletion
  deletionReason: text("deletionReason"), // Optional reason for deletion
  deletionToken: text("deletionToken"), // Token for account recovery
  deletionTokenExpires: timestamp("deletionTokenExpires"), // Recovery token expiration
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
})

// ============================================================================
// AUTHENTICATION TABLES
// ============================================================================

export const socialAccounts = pgTable("SocialAccount", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  provider: text("provider").notNull(),
  providerAccountId: text("providerAccountId").notNull(),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  expiresAt: timestamp("expiresAt"),
  tokenType: text("tokenType"),
  scope: text("scope"),
  idToken: text("idToken"),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
}, (table) => ({
  providerAccountIdx: uniqueIndex("provider_account_idx").on(table.provider, table.providerAccountId),
  userIdx: index("social_account_user_idx").on(table.userId),
}))

export const emailVerifications = pgTable("EmailVerification", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
}, (table) => ({
  userIdx: index("email_verification_user_idx").on(table.userId),
}))

export const passwordResets = pgTable("PasswordReset", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expiresAt").notNull(),
  used: boolean("used").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
}, (table) => ({
  userIdx: index("password_reset_user_idx").on(table.userId),
}))

// ============================================================================
// ORGANIZATION TABLES
// ============================================================================

export const organizations = pgTable("Organization", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // 'yacht_company' | 'cert_provider'
  status: text("status").notNull().default("pending"), // 'pending' | 'verified' | 'suspended'
  contactEmail: text("contactEmail").notNull(),
  description: text("description"),
  website: text("website"),
  verifiedAt: timestamp("verifiedAt"),
  verifiedBy: text("verifiedBy"), // Admin user ID who verified
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
}, (table) => ({
  nameIdx: index("organization_name_idx").on(table.name),
  typeIdx: index("organization_type_idx").on(table.type),
  statusIdx: index("organization_status_idx").on(table.status),
  contactEmailIdx: index("organization_contact_email_idx").on(table.contactEmail),
}))

export const organizationMemberships = pgTable("OrganizationMembership", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  organizationId: text("organizationId").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  role: text("role").notNull().default("member"), // 'owner' | 'admin' | 'member'
  joinedAt: timestamp("joinedAt").defaultNow().notNull(),
  invitedBy: text("invitedBy"), // User ID who invited this member
  invitedAt: timestamp("invitedAt"),
  acceptedAt: timestamp("acceptedAt"),
  status: text("status").notNull().default("active"), // 'active' | 'suspended' | 'pending'
}, (table) => ({
  userOrgIdx: uniqueIndex("user_organization_idx").on(table.userId, table.organizationId),
  userIdx: index("organization_membership_user_idx").on(table.userId),
  organizationIdx: index("organization_membership_org_idx").on(table.organizationId),
  roleIdx: index("organization_membership_role_idx").on(table.role),
  statusIdx: index("organization_membership_status_idx").on(table.status),
}))

// ============================================================================
// CERTIFICATE TABLES
// ============================================================================

export const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  issuingAuthority: text("issuingAuthority").notNull(),
  certificateNumber: text("certificateNumber").notNull(),
  dateIssued: timestamp("dateIssued").notNull(),
  expiryDate: timestamp("expiryDate"),
  documentUrl: text("documentUrl"), // Legacy field - kept for backward compatibility
  documentName: text("documentName"), // Legacy field - kept for backward compatibility
  documentSize: text("documentSize"), // Legacy field - kept for backward compatibility
  documentType: text("documentType"), // Legacy field - kept for backward compatibility
  notes: text("notes"),
  isFavorite: boolean("isFavorite").default(false).notNull(),
  tags: text("tags"), // Simple comma-separated tags for now
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  // Multi-tenant fields
  organizationId: text("organizationId").references(() => organizations.id, { onDelete: "cascade" }),
  scope: text("scope").notNull().default("personal"), // 'personal' | 'organization'
}, (table) => ({
  userIdx: index("certificate_user_idx").on(table.userId),
  organizationIdx: index("certificate_organization_idx").on(table.organizationId),
  scopeIdx: index("certificate_scope_idx").on(table.scope),
  expiryDateIdx: index("certificate_expiry_date_idx").on(table.expiryDate),
  createdAtIdx: index("certificate_created_at_idx").on(table.createdAt),
}))

export const certificateFiles = pgTable("CertificateFile", {
  id: text("id").primaryKey(),
  certificateId: text("certificateId")
    .notNull()
    .references(() => certificates.id, { onDelete: "cascade" }),
  fileName: text("fileName").notNull(),
  fileUrl: text("fileUrl").notNull(),
  fileSize: integer("fileSize").notNull(), // Size in bytes
  fileType: text("fileType").notNull(),
  uploadthingKey: text("uploadthingKey"), // For file deletion
  uploadOrder: integer("uploadOrder").default(0).notNull(), // Order of upload
  createdAt: timestamp("createdAt").defaultNow().notNull(),
})

// ============================================================================
// NOTIFICATION TABLES
// ============================================================================

export const notifications = pgTable("Notification", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  type: text("type").notNull(), // 'certificate_expiry' | 'system' | 'reminder'
  title: text("title").notNull(),
  message: text("message").notNull(),
  read: boolean("read").default(false).notNull(),
  actionUrl: text("actionUrl"), // Optional URL for notification action
  metadata: text("metadata"), // JSON metadata about the notification
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  expiresAt: timestamp("expiresAt"), // Optional expiration for temporary notifications
}, (table) => ({
  userIdx: index("notification_user_idx").on(table.userId),
  typeIdx: index("notification_type_idx").on(table.type),
  readIdx: index("notification_read_idx").on(table.read),
  createdAtIdx: index("notification_created_at_idx").on(table.createdAt),
  expiresAtIdx: index("notification_expires_at_idx").on(table.expiresAt),
}))

// ============================================================================
// ADMIN & AUDIT TABLES
// ============================================================================

export const accountDeletionAudits = pgTable("AccountDeletionAudit", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull(), // Don't use foreign key since user might be deleted
  userEmail: text("userEmail").notNull(), // Store email for audit trail
  userName: text("userName").notNull(), // Store name for audit trail
  userRole: text("userRole").notNull(), // Store role for audit trail
  deletionType: text("deletionType").notNull(), // 'soft', 'hard', 'recovered'
  deletionReason: text("deletionReason"), // User-provided reason
  initiatedBy: text("initiatedBy").notNull(), // 'user', 'admin', 'system'
  initiatorId: text("initiatorId"), // ID of who initiated (if admin)
  dataRetained: text("dataRetained"), // JSON of what data was retained
  dataDeleted: text("dataDeleted"), // JSON of what data was deleted
  certificateCount: integer("certificateCount").default(0).notNull(),
  fileCount: integer("fileCount").default(0).notNull(),
  ipAddress: text("ipAddress"), // IP address of deletion request
  userAgent: text("userAgent"), // User agent of deletion request
  createdAt: timestamp("createdAt").defaultNow().notNull(),
}, (table) => ({
  userIdIdx: index("account_deletion_audit_user_id_idx").on(table.userId),
  deletionTypeIdx: index("account_deletion_audit_type_idx").on(table.deletionType),
  createdAtIdx: index("account_deletion_audit_created_at_idx").on(table.createdAt),
}))

export const cleanupJobs = pgTable("CleanupJob", {
  id: text("id").primaryKey(),
  jobType: text("jobType").notNull(), // 'account_deletion', 'file_cleanup', etc.
  status: text("status").notNull(), // 'pending', 'running', 'completed', 'failed'
  scheduledFor: timestamp("scheduledFor").notNull(),
  startedAt: timestamp("startedAt"),
  completedAt: timestamp("completedAt"),
  recordsProcessed: integer("recordsProcessed").default(0).notNull(),
  recordsDeleted: integer("recordsDeleted").default(0).notNull(),
  errors: text("errors"), // JSON array of errors
  metadata: text("metadata"), // JSON metadata about the job
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
}, (table) => ({
  jobTypeIdx: index("cleanup_job_type_idx").on(table.jobType),
  statusIdx: index("cleanup_job_status_idx").on(table.status),
  scheduledForIdx: index("cleanup_job_scheduled_for_idx").on(table.scheduledFor),
}))

// ============================================================================
// ORGANIZATION LIFECYCLE TABLES
// ============================================================================

export const organizationLifecycleEvents = pgTable("OrganizationLifecycleEvent", {
  id: text("id").primaryKey(),
  organizationId: text("organizationId").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  eventType: text("eventType").notNull(), // 'admin_deleted' | 'grace_period_started' | 'grace_period_ended' | 'admin_recovered'
  triggeredBy: text("triggeredBy").notNull(), // User ID who triggered the event
  triggeredAt: timestamp("triggeredAt").defaultNow().notNull(),
  gracePeriodEnds: timestamp("gracePeriodEnds"), // When grace period expires (for grace_period_started events)
  metadata: text("metadata"), // JSON metadata about the event
  createdAt: timestamp("createdAt").defaultNow().notNull(),
}, (table) => ({
  organizationIdx: index("lifecycle_event_organization_idx").on(table.organizationId),
  eventTypeIdx: index("lifecycle_event_type_idx").on(table.eventType),
  triggeredAtIdx: index("lifecycle_event_triggered_at_idx").on(table.triggeredAt),
  gracePeriodEndsIdx: index("lifecycle_event_grace_period_ends_idx").on(table.gracePeriodEnds),
}))

export const adminRecoveryRequests = pgTable("AdminRecoveryRequest", {
  id: text("id").primaryKey(),
  organizationId: text("organizationId").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  requestedBy: text("requestedBy").notNull().references(() => users.id, { onDelete: "cascade" }),
  requestReason: text("requestReason").notNull(), // Why they need admin access
  status: text("status").notNull().default("pending"), // 'pending' | 'approved' | 'rejected' | 'expired'
  reviewedBy: text("reviewedBy"), // Platform admin who reviewed the request
  reviewedAt: timestamp("reviewedAt"),
  reviewNotes: text("reviewNotes"), // Admin notes about the decision
  expiresAt: timestamp("expiresAt").notNull(), // When the request expires
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
}, (table) => ({
  organizationIdx: index("admin_recovery_organization_idx").on(table.organizationId),
  requestedByIdx: index("admin_recovery_requested_by_idx").on(table.requestedBy),
  statusIdx: index("admin_recovery_status_idx").on(table.status),
  expiresAtIdx: index("admin_recovery_expires_at_idx").on(table.expiresAt),
}))

// ============================================================================
// EXTERNAL SCHEMAS
// ============================================================================

export const neonAuthSchema = pgSchema("neon_auth")

export const usersSync = neonAuthSchema.table("users_sync", {
  id: text("id").primaryKey(),
  name: text("name"),
  email: text("email"),
  createdAt: timestamp("created_at"),
  updatedAt: timestamp("updated_at"),
  deletedAt: timestamp("deleted_at"),
  rawJson: text("raw_json"),
})
